# 知识管理Excel导入模板说明

## 模板格式

Excel文件必须包含名为"**知识管理**"的工作表，第一行为表头，第二行开始为数据。

### 列定义（按顺序）

| 列序号 | 列名称 | 是否必填 | 数据类型 | 说明 |
|--------|--------|----------|----------|------|
| A (0) | 文档编号 | **是** | 文本 | 知识文档的唯一编号 |
| B (1) | 标题 | **是** | 文本 | 知识文档的标题 |
| C (2) | 负责人 | **是** | 文本 | 必须是系统中已存在的用户姓名 |
| D (3) | 文件类型 | **是** | 文本 | 文档的类型分类 |
| E (4) | 说明 | 否 | 文本 | 知识文档的详细说明 |

## 模板示例

```
文档编号 | 标题 | 负责人 | 文件类型 | 说明
---------|------|--------|----------|------
DOC001 | CRM系统操作手册 | 张三 | 操作手册 | 详细介绍CRM系统的操作流程
DOC002 | 销售流程规范 | 李四 | 流程文档 | 标准化销售流程的规范文档
DOC003 | 客户服务标准 | 王五 | 标准文档 | 客户服务的标准化要求
```

## 导入规则

### 数据验证规则
1. **文档编号**：不能为空，建议使用唯一标识
2. **标题**：不能为空，建议简洁明了
3. **负责人**：必须在系统中已存在的用户，否则导入失败
4. **文件类型**：不能为空，建议使用标准化的类型名称

### 数据转换规则
1. **负责人 → 负责人ID**：系统自动根据负责人姓名查找对应的用户ID
2. **数据清理**：自动去除字段值首尾空格

### 错误处理
- 如果负责人不存在，显示错误：`用户[姓名]不存在`
- 如果必填字段为空，显示相应的验证错误信息
- 所有错误会在导入时统一显示，导入失败时数据回滚

## 使用步骤

1. **准备Excel文件**
   - 创建包含"知识管理"工作表的Excel文件
   - 按照模板格式填写数据
   - 确保必填字段已正确填写

2. **数据准备**
   - 确认所有负责人在系统中已存在
   - 检查数据格式和完整性
   - 验证文档编号的唯一性

3. **执行导入**
   - 通过系统的知识管理导入功能上传Excel文件
   - 系统会自动解析并验证数据
   - 导入成功后，知识管理记录会添加到系统中

4. **验证结果**
   - 检查导入的知识管理信息是否正确
   - 确认字段映射和数据转换是否准确

## 注意事项

1. **文件格式**：支持 `.xlsx` 和 `.xls` 格式
2. **工作表名称**：必须是"知识管理"（区分大小写）
3. **数据行数**：建议单次导入不超过1000条记录
4. **字符编码**：建议使用UTF-8编码
5. **数据备份**：导入前建议备份现有数据
6. **事务性**：导入操作具有事务性，失败时会自动回滚

## 常见问题

### Q: 为什么提示"用户不存在"？
A: 请确认Excel中的负责人姓名与系统中的用户姓名完全一致，包括空格和标点符号。

### Q: 文档编号可以重复吗？
A: 建议使用唯一的文档编号，避免数据混淆。

### Q: 文件类型有固定的选项吗？
A: 文件类型可以自定义，建议使用标准化的类型名称便于管理。

### Q: 导入失败后数据会保留吗？
A: 不会。导入操作具有事务性，任何错误都会导致整批数据回滚。

## 字段说明

### 文档编号
- 用于唯一标识知识文档
- 建议使用有意义的编码规则
- 例如：DOC001、MANUAL-001、PROC-001等

### 标题
- 知识文档的主要标识
- 应简洁明了，便于搜索和识别
- 建议不超过50个字符

### 负责人
- 知识文档的责任人
- 必须是系统中已注册的用户
- 用于权限控制和责任追踪

### 文件类型
- 用于分类管理知识文档
- 常见类型：操作手册、流程文档、标准文档、技术文档等
- 可根据企业需要自定义

### 说明
- 知识文档的详细描述
- 可选字段，用于提供更多上下文信息
- 建议不超过200个字符

---

*如有其他问题，请联系系统管理员。*
