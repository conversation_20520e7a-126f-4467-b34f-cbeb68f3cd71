import request from "@/utils/request";
import config from "../configs";
import { transFormData } from "@/utils/util";
import { getToken } from "@/utils/auth";
import { downloadJsonData } from "@/utils/request";

let baseUrl = `${config.PROJAPI}/knowledge`;

// 分页
export function getKnowledgePage(data) {
  return request({
    url: `${baseUrl}/find`,
    method: "post",
    data,
  });
}

// 新增
export function addKnowledge(data) {
  return request({
    url: `${baseUrl}/add`,
    method: "post",
    data,
  });
}

// 编辑
export function editKnowledge(data) {
  return request({
    url: `${baseUrl}/update`,
    method: "post",
    data,
  });
}

// 删除
export function deleteKnowledge(data) {
  return request({
    url: `${baseUrl}/delete`,
    method: "post",
    data,
  });
}

// 查看所有附件
export function getKnowledgeFile(data) {
  return request({
    url: `${baseUrl}/file/find`,
    method: "post",
    data,
  });
}

// 上传附件
export function uploadKnowledgeFile(data) {
  return request({
    url: `${baseUrl}/file/upload`,
    method: "post",
    data: transFormData(data),
  });
}

// 下载附件
export function downloadKnowledgeFile(data) {
  return downloadJsonData(`${baseUrl}/file/download`, data);
}

// 删除附件
export function deleteKnowledgeFile(data) {
  return request({
    url: `${baseUrl}/file/delete`,
    method: "post",
    data,
  });
}

// 预览
export function previewKnowledgeFile(params) {
  return request({
    url: `${baseUrl}/file/onlineView`,
    method: "get",
    responseType: "blob",
    params,
  });
}

// 文件预览
export function getKnowledgeFileUrl(params) {
  return `${process.env.VUE_APP_BASE_API}${baseUrl}/file/onlineView?fileId=${
    params.fileId
  }&token=${getToken()}`;
}

// 详情
export function getKnowledgeDetail(data) {
  return request({
    url: `${baseUrl}/findById`,
    method: "post",
    data,
  });
}

// 导入
export function importKnowledge(data) {
  return request({
    url: `${baseUrl}/import`,
    method: "post",
    headers: {
      "Content-Type": "multipart/form-data",
    },
    data,
  });
}

// 导出
export function exportKnowledge(data) {
  return downloadJsonData(`${baseUrl}/export`, data);
}
