package com.swxa.prp.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.swxa.prp._enum.Constant;
import com.swxa.prp._enum.RpcAuthConstant;
import com.swxa.prp.business.dict.vo.BussDicDataVO;
import com.swxa.prp.constant.SwErrorCodeConstant;
import com.swxa.prp.dao.SysRoleDao;
import com.swxa.prp.dao.SysRolePermissionDao;
import com.swxa.prp.entity.LoginUser;
import com.swxa.prp.entity.TreeTable;
import com.swxa.prp.entity.ZtreeModel;
import com.swxa.prp.exception.SwPrpException;
import com.swxa.prp.model.*;
import com.swxa.prp.model.vo.SysRoleUsersVO;
import com.swxa.prp.service.*;
import com.swxa.prp.util.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class RoleServiceImpl implements RoleService {

    // private static final Logger log = LoggerFactory.getLogger("adminLogger");
    @Autowired
    private SysRoleDao sysRoleDao;
    @Autowired
    private ProjectRoleRelationService projectRoleRelationService;
    @Autowired
    private SysProjectService projectService;
    @Autowired
    private SysRolePermissionService rolePermissionService;
    @Autowired
    private SysRolePermissionDao sysRolePermissionDao;
    @Autowired
    private SysRoleUserService roleUserService;
    @Autowired
    private UserService userService;


    @Value("${constant.auth.ipAddr}")
    private String authUrl;

    @Override
    public List<SysRoleUsersVO> queryRoleList() {
        String responseData = HttpUtil.sendPostRequestBodySSL(authUrl + RpcAuthConstant.RPC_ROLE_QUERY_LIST, "1", MyUtil.getToken());
        ResponseData queryMsg = JSONObject.parseObject(responseData, ResponseData.class);
        if (SwErrorCodeConstant.QUERY_SUCCESS_CODE_0A0A0001.equals(queryMsg.getCode())) {//代表请求成功
            //获取加密信息
            String listInfo = queryMsg.getObject().toString();

            //解密信息
            String decrypt = SwEncryptUtil.decrypt(listInfo);

            List<SysRoleUsersVO> result= JSONObject.parseArray(decrypt, SysRoleUsersVO.class);

            return result;
        }
        else{
            throw new SwPrpException(SwErrorCodeConstant.QUERY_ERROR_CODE_0A0A0001);

        }
    }
    @Override
    public List<TreeTable> list(LoginUser user, String projName) {

        Long projId = null;
        if (Constant.SYSTEM_ID_PROJECT != user.getProjId()) {
            projId = user.getProjId();
        }
        Long userId = user.getId();

        List<TreeTable> treeTables = new ArrayList<TreeTable>();
        List<SysProject> projects = projectService.findWithRoleRealtion(projId, projName);
        if (projects != null && projects.size() > 0) {
            for (SysProject project : projects) {

                List<SysRole> roles = project.getRoles();
                if (roles != null && roles.size() > 0) {
                    for (SysRole sysRole : roles) {

                        //非超级管理员
                        if (!Constant.SYS_MANAGER_USER_IDS.contains(userId.toString())) {
                            if (!userId.equals(sysRole.getCreateAtId())) {
                                continue;
                            }
                        }

                        TreeTable roleTable = new TreeTable(sysRole.getId(), sysRole.getName(),
                                sysRole.getDescription(), sysRole.getCreateTime(), sysRole.getUpdateTime(),
                                project.getId());

                        roleTable.setCreateAtId(sysRole.getCreateAtId());
                        roleTable.setCreateAtName(sysRole.getCreateAtName());

                        treeTables.add(roleTable);
                    }
                }
            }
        }
        return treeTables;
    }


    @Override
    public ResponseData edit(SysRole sysRole, String permissionIds, String projId) {
        ResponseData j = new ResponseData();
        LoginUser user = UserUtil.getLoginUser();

        try {
            if (sysRole.getId() != null) {// edit

                sysRole.setUpdateTime(new Date());
                sysRoleDao.update(sysRole);

                // 删除角色 项目 资源关系表
                projectRoleRelationService.deleteByRoleAndProId(sysRole.getId());
                rolePermissionService.deleteByRoleId(sysRole.getId());
            } else {// add

                //创建者
                sysRole.setCreateAtId(user.getId());
                sysRole.setCreateAtName(user.getUsername() + "[" + user.getNickname() + "]");

                sysRole.setCreateTime(new Date());
                sysRoleDao.save(sysRole);
            }

            List<SysProjectRoleRelation> relations = new ArrayList<>();
            if (StringUtils.isNoneBlank(projId)) {
                String[] ids = projId.trim().split(",");
                for (String id : ids) {
                    relations.add(new SysProjectRoleRelation(sysRole.getId(), Long.valueOf(id)));
                }
            }
            projectRoleRelationService.batchInsert(relations);

            List<SysRolePermission> rolePermissions = new ArrayList<>();
            if (StringUtils.isNoneBlank(permissionIds)) {
                String[] ids = permissionIds.trim().split(",");
                for (String id : ids) {
                    rolePermissions.add(new SysRolePermission(sysRole.getId(), Long.valueOf(id)));
                }
            }
            sysRolePermissionDao.batchInsert(rolePermissions);

            j = new ResponseData(true, "操作成功", null);
        } catch (Exception e) {
            e.printStackTrace();
            j.setMessage("操作失败");
        }

        return j;
    }

    @Override
    public SysRole findById(Long id) {
        return sysRoleDao.findById(id);
    }

    @Override
    public List<Long> findPermissionIdsByRoleId(Long roleId) {
        return sysRoleDao.findPermissionIdsByRoleId(roleId);
    }

    @Transactional
    @Override
    public ResponseData delete(Long id) {
        ResponseData j = new ResponseData();
        try {
            projectRoleRelationService.deleteByRoleAndProId(id);
            rolePermissionService.deleteByRoleId(id);
            roleUserService.delete(id);
            sysRoleDao.delete(id);
            j = new ResponseData(true, "删除操作成功", "");
        } catch (Exception e) {
            j.setMessage("删除操作失败");
            e.printStackTrace();
        }
        return j;
    }

    @Override
    public List<ZtreeModel> findRolePersion(Long projId) {
        List<ZtreeModel> ztreeModels = new ArrayList<ZtreeModel>();
        List<Map<String, Object>> roleUsers = sysRoleDao.findRolePersion(projId);
        if (roleUsers != null && roleUsers.size() > 0) {
            for (Map<String, Object> map : roleUsers) {
                Long roleId = Long.valueOf(map.get("roleId").toString());

                String roleName = map.get("roleName").toString();
                String userList = map.get("userList").toString();
                ZtreeModel ztreeModel = new ZtreeModel(roleId, 0l, roleName);
                ztreeModels.add(ztreeModel);

                if (userList != null) {
                    String[] array = userList.split(",");
                    for (String userIdName : array) {
                        String[] idName = userIdName.split("_");
                        Long userId = Long.valueOf(idName[0].toString());
                        String userName = idName[1].toString();
                        ZtreeModel ztreeModelChild = new ZtreeModel(userId, ztreeModel.getId(), userName);
                        ztreeModels.add(ztreeModelChild);
                    }
                }
            }
        }
        return ztreeModels;
    }

    @Override
    public ResponseData chechUnique(String name, Long projId) {
        ResponseData j = new ResponseData(false, "改角色名称已经存在", "");
        Long num = sysRoleDao.chechUnique(name, projId);
        if (num != null && num == 0L) {
            j = new ResponseData(true, "不存在的角色名称", "");
        }
        return j;
    }

    @Override
    public List<SysRole> queryByProId(Long projId) {
        return sysRoleDao.queryByProId(projId);
    }

    @Override
    public List<SysRole> queryByProIdAndUserId(Long projId, Long userId) {
        if (projId == null) {
            throw new IllegalArgumentException("项目ID不能为空");
        }
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        return sysRoleDao.queryByProIdAndUserId(projId, userId);
    }

    @Override
    public List<SysRole> find(Map<String, Object> map) {
        return sysRoleDao.find(map);
    }

    @Override
    public void configRoleUser(Long roleId, String userIds) {
        if (roleId == null) {
            throw new IllegalArgumentException("角色ID不能为空");
        }
        if (StringUtils.isBlank(userIds)) {
            throw new IllegalArgumentException("角色所属用户不能为空");
        }
        String[] userIdsArray = userIds.trim().split(",");
        if (userIdsArray.length != 1) {
            throw new IllegalArgumentException("角色所属用户数量大于1");
        }

        //设置该角色的所属用户
        SysUser sysUser = userService.selectById(Long.valueOf(userIds.trim()));
        if (sysUser == null) {
            throw new IllegalArgumentException("用户不存在");
        }

        SysRole sysRole = new SysRole();
        sysRole.setId(roleId);
        sysRole.setCreateAtId(Long.valueOf(userIds));
        sysRole.setCreateAtName(sysUser.getUsername() + "[" + sysUser.getNickname() + "]");

        sysRoleDao.update(sysRole);

    }


}
