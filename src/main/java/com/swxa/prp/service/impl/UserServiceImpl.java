package com.swxa.prp.service.impl;

import java.util.*;

import com.swxa.prp._enum.Constant;
import com.swxa.prp.config.SecurityPasswordEncoderConfig;
import com.swxa.prp.constant.SwErrorCodeConstant;
import com.swxa.prp.dao.SysUserDao;
import com.swxa.prp.entity.LoginUser;
import com.swxa.prp.exception.SwPrpException;
import com.swxa.prp.model.SysUser;
import com.swxa.prp.model.dto.UserQueryDTO;
import com.swxa.prp.service.SysRoleUserService;
import com.swxa.prp.service.UserService;
import com.swxa.prp.util.ResponseData;
import com.swxa.prp.util.MyDateUtil;
import com.swxa.prp.util.MyUtil;
import com.swxa.prp.util.UserUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Service
public class UserServiceImpl implements UserService {

    private static final Logger log = LoggerFactory.getLogger("adminLogger");

    @Autowired
    private SysUserDao userDao;
   // @Autowired
    private BCryptPasswordEncoder passwordEncoder;
    @Autowired
    private SecurityPasswordEncoderConfig securityPasswordEncoderConfig;
    @Autowired
    private SysRoleUserService roleUserService;


    @Value("${constant.login.pwd.expireDays}")
    private Long expireDays;

    @Override
    @Transactional
    public SysUser saveUser(SysUser sysUser) {
        sysUser.setPassword(securityPasswordEncoderConfig.encode(sysUser.getPassword()));
        sysUser.setStatus(SysUser.Status.VALID);
        sysUser.setCreateTime(new Date());
        userDao.insertSelective(sysUser);
        return sysUser;
    }


    @Override
    @Transactional
    public SysUser frontSaveUser(SysUser sysUser) {

        //用户前段校验不为空
        sysUser.frontCheckNoneEmptyParam();
        sysUser.setUsername(sysUser.getEmail().trim());

        SysUser u = getUser(sysUser.getUsername());
        if (u != null) {
            throw new IllegalArgumentException(sysUser.getUsername() + "已存在");
        }

        sysUser.setPassword(passwordEncoder.encode(sysUser.getPassword()));
        sysUser.setStatus(SysUser.Status.VALID);
        sysUser.setCreateTime(new Date());
        userDao.insertSelective(sysUser);
        return sysUser;
    }

    @Override
    public SysUser getUser(String username) {
        return userDao.getUser(username);
    }


    @Transactional
    @Override
    public ResponseData changePassword(Long userId, String oldPassword, String newPassword) {

        if (userId == null) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_0A030020);
            //throw new IllegalArgumentException("The user ID cannot be empty.");
        }

        // 先检查代码是否符合规范
/*        Boolean checkPwd = MyUtil.checkPwd(newPassword);
        if (!checkPwd) {// 不符合要求
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_0A030017);
            //throw new IllegalArgumentException("The password must be a combination of uppercase letters, lowercase letters, special characters, and digits, and must be at least eight characters long.");
        }*/

        if (StringUtils.isBlank(oldPassword) || StringUtils.isBlank(newPassword)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_0A030021);
            //throw new IllegalArgumentException("The old and new passwords cannot be empty.");
        }

        SysUser sysUser = selectById(userId);
        if (sysUser == null) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_0A030010);
            //throw new IllegalArgumentException("The queried user does not exist.");
        }
        if (!passwordEncoder.matches(oldPassword, sysUser.getPassword())) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_0A030022);
            //throw new IllegalArgumentException("Old password error.");
        }

        boolean pwdMatch = passwordEncoder.matches(newPassword, sysUser.getPassword());
        if (pwdMatch) {// 新密码跟旧密码一样  ERROR_CODE_0A030023
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_0A030023);
            //throw new IllegalArgumentException("The new and old passwords cannot be the same.");
        }

        String newPwdFinal = passwordEncoder.encode(newPassword);
        userDao.changePassword(sysUser.getId(), newPwdFinal);

        sysUser.setUpdatePwdFlag("1");// 修改密码 不需要在登录修改，1代表不需要登录再修改密码

        sysUser.setPassword(newPwdFinal);// 在设置一下新的密码，防止篡改

        sysUser.setUpdatePwdDate(new Date());

        updateUser(sysUser);

        return ResponseData.buildResult(SwErrorCodeConstant.QUERY_SUCCESS_CODE_0A0A0001, true, "");
    }

    @Override
    @Transactional
    public ResponseData updateUser(SysUser sysUser) {
        ResponseData j = new ResponseData();
        try {
            sysUser.setUpdateTime(new Date());
            userDao.update(sysUser);
            //j = new JsonMsgUtil(true, "Successful operation...", "");
            return ResponseData.buildResult(SwErrorCodeConstant.QUERY_SUCCESS_CODE_0A0A0001, true, sysUser);

        } catch (Exception e) {
            j.setMessage("Operation failure...");
            e.printStackTrace();
            return ResponseData.buildResult(SwErrorCodeConstant.ERROR_CODE_0A030019, true, sysUser);
        }
    }

    @Override
    public List<SysUser> find(Map<String, Object> map) {
        return userDao.find(map);
    }

    @Transactional
    @Override
    public ResponseData delete(String ids, Long projId, Long userId) {
        ResponseData j = new ResponseData();

        if (StringUtils.isNoneBlank(ids)) {

            String[] idsArray = ids.trim().split(",");
            Long[] array = new Long[idsArray.length];

            //标记存在删除的人
            List<Long> delUserIds = new ArrayList<>();

            for (int i = 0; i < idsArray.length; i++) {
                String id = idsArray[i];
                // 排除
                if (!Constant.SYS_MANAGER_USER_IDS.contains(id)) {
                    Long userid = Long.valueOf(id.trim());
                    if (userid != userId) {// 不能删除自己
                        array[i] = userid;
                        delUserIds.add(userid);
                    }
                }
            }

            if (delUserIds != null && !delUserIds.isEmpty()) {
                // 删除用户角色关系
                roleUserService.deleteBatch(array);
                userDao.deleteBatch(array, projId);
            }

            j = new ResponseData(true, "删除操作成功", "");
        }

        return j;
    }


    @Override
    public ResponseData findById(Long id) {
        ResponseData j = new ResponseData();
        try {
            j = new ResponseData(true, "操作成功", userDao.getById(id));
        } catch (Exception e) {
            j.setMessage("操作失败");
            e.printStackTrace();
        }
        return j;
    }

    @Override
    public String findUserNames(Long roleId) {
        return userDao.findUserNames(roleId);
    }

    @Override
    public List<Long> findUserIds(Long roleId) {
        return userDao.findUserIds(roleId);
    }

    @Override
    public SysUser queryUser(Long projId, String username) {
        return userDao.queryUser(projId, username);
    }


    @Override
    public void insertSelective(SysUser sysUser) {
        userDao.insertSelective(sysUser);
    }

    @Override
    public SysUser selectById(Long id) {
        return userDao.getById(id);
    }

    @Transactional
    @Override
    public ResponseData resetPassword(Long userId, String userPwd, String isUpdateFlag) {
        if (userId == null || StringUtils.isBlank(userPwd)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_0A030016);
            //throw new IllegalArgumentException("Reset the user id and new password cannot be empty.");
        }

        SysUser sysUser = selectById(userId);
        if (sysUser == null) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_0A030010);
            //throw new IllegalArgumentException("The user information does not exist.");
        }

        // 先检查代码是否符合规范
        Boolean checkPwd = MyUtil.checkPwd(userPwd.trim());
        if (!checkPwd) {// 不符合要求
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_0A030017);
            //throw new IllegalArgumentException("The password must be a combination of uppercase letters, lowercase letters, special characters, and digits, and must be at least eight characters long.");
        }

        sysUser.setPassword(passwordEncoder.encode(userPwd.trim()));
        if (StringUtils.isBlank(isUpdateFlag)) {
            isUpdateFlag = "0";
        }
        sysUser.setUpdatePwdFlag(isUpdateFlag);// 0第一次登录需要修改密码
        sysUser.setUpdatePwdDate(new Date());
        userDao.update(sysUser);

        //重置完成
        return ResponseData.buildResult(SwErrorCodeConstant.QUERY_SUCCESS_CODE_0A0A0001, true, null);
    }

    @Override
    public ResponseData list(SysUser sysUser) {
        Map<String, Object> paramMap = new HashMap<>();
        if (sysUser != null) {
            paramMap.put("userId", sysUser.getId());
        }
        List<SysUser> datas = find(paramMap);
        return new ResponseData(true, "查询成功", datas);
    }


    /**
     * 根据用户名和昵称查询用户列表
     *
     * @param userQueryDTO 用户 dto
     * @return {@link List }<{@link SysUser }>
     * <AUTHOR>
     * @date 2023/11/15
     */
    @Override
    public List<SysUser> queryList(UserQueryDTO userQueryDTO) {
        return userDao.queryList(userQueryDTO);
    }

    @Transactional
    @Override
    public SysUser checkUpdatePwd() {

        LoginUser loginUser = UserUtil.getLoginUser();
        Date updatePwdDate = loginUser.getUpdatePwdDate();
        String updatePwdFlag = loginUser.getUpdatePwdFlag();

        SysUser sysUser = new SysUser();
        sysUser.setId(loginUser.getId());
        if (updatePwdDate == null) {//为空的话 立马设置当前值为初次修改密码的值
            sysUser.setUpdatePwdDate(new Date());
            updateUser(sysUser);
        } else {
            //判单现在的时间 到修改密码的时间 是否大于180天
            Long diffDays = MyDateUtil.getDiffDays(updatePwdDate, new Date());
            if (diffDays != null && diffDays.compareTo(expireDays) > 0) {//超过180天了
                sysUser.setUpdatePwdFlag("0");
                loginUser.setUpdatePwdFlag("0");

                updateUser(sysUser);
            }

        }


        return loginUser;
    }

    @Override
    public void updatePwd() {
        String newPwdFinal = passwordEncoder.encode("123465");
        userDao.changePassword(8769L, newPwdFinal);

    }



}
