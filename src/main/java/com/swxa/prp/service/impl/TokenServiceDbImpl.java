package com.swxa.prp.service.impl;

import java.security.Key;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;

import com.swxa.prp._enum.RpcAuthConstant;
import com.swxa.prp.constant.SwErrorCodeConstant;
import com.swxa.prp.dao.TokenDao;
import com.swxa.prp.entity.LoginUser;
import com.swxa.prp.entity.TokenModel;
import com.swxa.prp.service.TokenService;
import com.swxa.prp.util.HttpUtil;
import com.swxa.prp.util.ResponseData;
import com.swxa.prp.util.SwEncryptUtil;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;

/**
 * token数据库操作
 *
 * <AUTHOR>
 * @date 2018年9月2日 上午2:24:10
 */
@Primary
@Service
public class TokenServiceDbImpl implements TokenService {

    private static final Logger log = LoggerFactory.getLogger("adminLogger");
    /**
     * token过期秒数
     */
    @Value("${token.expire.seconds}")
    private Integer expireSeconds;
    @Autowired
    private TokenDao tokenDao;
    private static final Long MINUTES_10 = 10 * 60 * 1000L;
    @Autowired
    private UserDetailsService userDetailsService;
    /**
     * 私钥
     */
    @Value("${token.jwtSecret}")
    private String jwtSecret;

    private static Key KEY = null;
    private static final String LOGIN_USER_KEY = "LOGIN_USER_KEY";


    @Value("${constant.auth.ipAddr}")
    private String authUrl;


    @Override
    public String saveToken(LoginUser loginUser) {

        String username = loginUser.getUsername();

        //调用认证中心接口获取用户信息
        Map<String, String> userNameMap = new HashMap<>();
        userNameMap.put(RpcAuthConstant.INFO_SYS_AUTH_USERINFO_USERNAME_REQ, username);
        String responseData = HttpUtil.sendPostRequestBodySSL(authUrl + RpcAuthConstant.INFO_SYS_AUTH_TOKEN_URL, JSONObject.toJSONString(userNameMap), null);
        //解密
        ResponseData userTokenMsg = JSONObject.parseObject(responseData, ResponseData.class);
        if (SwErrorCodeConstant.QUERY_SUCCESS_CODE_0A0A0001.equals(userTokenMsg.getCode())) {//代表请求成功
            //获取加密信息
            String encodeTokenInfo = userTokenMsg.getObject().toString();
            //解密信息
            String token = SwEncryptUtil.decrypt(encodeTokenInfo);
            return token;
        }
        return null;
    }

    /**
     * 生成jwt
     *
     * @param loginUser
     * @return
     */
    private String createJWTToken(LoginUser loginUser) {
        Map<String, Object> claims = new HashMap<>();
        claims.put(LOGIN_USER_KEY, loginUser.getToken());// 放入一个随机字符串，通过该串可找到登陆用户

        String jwtToken = Jwts.builder().setClaims(claims).signWith(SignatureAlgorithm.HS256, getKeyInstance()).compact();

        return jwtToken;
    }

    @Override
    public void refresh(LoginUser loginUser) {
        loginUser.setLoginTime(System.currentTimeMillis());
        loginUser.setExpireTime(loginUser.getLoginTime() + expireSeconds * 1000);

        TokenModel model = tokenDao.getById(loginUser.getToken());
        model.setUpdateTime(new Date());
        model.setExpireTime(new Date(loginUser.getExpireTime()));
        model.setVal(JSONObject.toJSONString(loginUser));

        tokenDao.update(model);
    }

    @Override
    public LoginUser getLoginUser(String jwtToken) {

        //调用认证中心接口获取用户信息
        Map<String, String> userNameMap = new HashMap<>();
        userNameMap.put(RpcAuthConstant.INFO_SYS_AUTH_TOKEN_REQ, jwtToken);
        String responseData = HttpUtil.sendPostRequestBodySSL(authUrl + RpcAuthConstant.INFO_SYS_AUTH_TOKEN_USERDATA_URL, JSONObject.toJSONString(userNameMap), null);
        //解密
        ResponseData userTokenMsg = JSONObject.parseObject(responseData, ResponseData.class);
        if (SwErrorCodeConstant.QUERY_SUCCESS_CODE_0A0A0001.equals(userTokenMsg.getCode())) {//代表请求成功
            //获取加密信息
            String encodeTokenInfo = userTokenMsg.getObject().toString();
            //解密信息
            String userDataStr = SwEncryptUtil.decrypt(encodeTokenInfo);

            //转为用户信息
            LoginUser loginUser = JSONObject.parseObject(userDataStr, LoginUser.class);
            return loginUser;
        }
        return null;
    }

    @Override
    public boolean deleteToken(String jwtToken) {
        String uuid = getUUIDFromJWT(jwtToken);
        if (uuid != null) {
            TokenModel model = tokenDao.getById(uuid);
            LoginUser loginUser = toLoginUser(model);
            if (loginUser != null) {
                tokenDao.delete(uuid);
                // logService.save(loginUser.getId(), "退出", true, null);
                return true;
            }
        }

        return false;
    }

    private LoginUser toLoginUser(TokenModel model) {
        if (model == null) {
            return null;
        }

        // 校验是否已过期
        if (model.getExpireTime().getTime() > System.currentTimeMillis()) {
            return JSONObject.parseObject(model.getVal(), LoginUser.class);
        }

        return null;
    }

    private Key getKeyInstance() {
        if (KEY == null) {
            synchronized (TokenServiceDbImpl.class) {
                if (KEY == null) {// 双重锁
                    byte[] apiKeySecretBytes = DatatypeConverter.parseBase64Binary(jwtSecret);
                    KEY = new SecretKeySpec(apiKeySecretBytes, SignatureAlgorithm.HS256.getJcaName());
                }
            }
        }

        return KEY;
    }

    private String getUUIDFromJWT(String jwt) {
        if ("null".equals(jwt) || StringUtils.isBlank(jwt)) {
            return null;
        }

        Map<String, Object> jwtClaims = null;
        try {
            jwtClaims = Jwts.parser().setSigningKey(getKeyInstance()).parseClaimsJws(jwt).getBody();
            return MapUtils.getString(jwtClaims, LOGIN_USER_KEY);
        } catch (ExpiredJwtException e) {
            log.error("{}已过期", jwt);
        } catch (Exception e) {
            log.error("{}", e);
        }

        return null;
    }

    @Override
    public ResponseData queryByToken(String token) {
        if (StringUtils.isBlank(token)) {
            throw new IllegalArgumentException("token凭证不能为空");
        }
        LoginUser loginUser = getLoginUser(token);
        if (loginUser == null) {
            throw new IllegalArgumentException("用户信息不存在");
        }
        //判断是否要过期
        checkLoginTime(token);

        return new ResponseData(true, "查询成功", loginUser);
    }

    @Override
    public ResponseData removeToken(String userToken) {
        return new ResponseData(deleteToken(userToken), "删除token成功", "");
    }

    @Override
    public ResponseData checkUpdatePwd(String token) {
        return new ResponseData(true);
    }

    public LoginUser checkLoginTime(String jwtToken) {
        //调用认证中心接口获取信息
        Map<String, String> userNameMap = new HashMap<>();
        String responseData = HttpUtil.sendPostRequestSSL(authUrl + RpcAuthConstant.INFO_SYS_CHECK_LOGIN_TIME_URL, null, jwtToken);
        //解密
        ResponseData userMsg = JSONObject.parseObject(responseData, ResponseData.class);
        if (SwErrorCodeConstant.QUERY_SUCCESS_CODE_0A0A0001.equals(userMsg.getCode())) {//代表请求成功
            //获取加密信息
            String encodeUserInfo = userMsg.getObject().toString();
            //解密信息
            String userDataStr = SwEncryptUtil.decrypt(encodeUserInfo);

            //转为用户信息
            LoginUser loginUser = JSONObject.parseObject(userDataStr, LoginUser.class);
            return loginUser;
        }
        return null;
    }

}
