package com.swxa.prp.service.impl;

import java.util.ArrayList;
import java.util.List;

import com.swxa.prp._enum.Constant;
import com.swxa.prp.entity.DistributeTreeTable;
import com.swxa.prp.entity.LoginUser;
import com.swxa.prp.model.SysProject;
import com.swxa.prp.model.SysRole;
import com.swxa.prp.model.SysRoleDataType;
import com.swxa.prp.model.SysRoleUser;
import com.swxa.prp.service.*;
import com.swxa.prp.util.ResponseData;
import com.swxa.prp.util.UserUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class DistributeServiceImpl implements DistributeService {

    // private static final Logger log = LoggerFactory.getLogger("adminLogger");
    @Autowired
    private SysProjectService projectService;
    @Autowired
    private SysRoleUserService roleUserService;
    @Autowired
    private UserService userService;


    @Override
    public List<DistributeTreeTable> list(Long projId, String projName) {


        LoginUser user = UserUtil.getLoginUser();
        Long userId = user.getId();

        List<DistributeTreeTable> treeTables = new ArrayList<DistributeTreeTable>();
        List<SysProject> projects = projectService.findWithRoleRealtion(projId, projName);
        if (projects != null && projects.size() > 0) {
            for (SysProject project : projects) {
                //项目的信息先去掉
                List<SysRole> roles = project.getRoles();
                if (roles != null && roles.size() > 0) {
                    for (SysRole sysRole : roles) {


                        //非超级管理员
                        if (!Constant.SYS_MANAGER_USER_IDS.contains(userId.toString())) {
                            if (!userId.equals(sysRole.getCreateAtId())) {
                                continue;
                            }
                        }


                        // 查询对应的用户
                        //group_concat  这个默认是存在长度的问题的，1024  吵过之后就被截取了  ，新的数据库需要记住
                        String userNames = userService.findUserNames(sysRole.getId());
                        DistributeTreeTable roleTable = new DistributeTreeTable(sysRole.getId(), sysRole.getName(),
                                userNames == null ? "" : userNames, project.getId());




                        //创建者
                        roleTable.setCreateAtId(sysRole.getCreateAtId());
                        roleTable.setCreateAtName(sysRole.getCreateAtName());

                        treeTables.add(roleTable);
                    }
                }
            }
        }
        return treeTables;
    }

    @Override
    public List<Long> findUserIds(Long roleId) {
        return userService.findUserIds(roleId);
    }

    @Transactional
    @Override
    public ResponseData distribute(Long roleId, String userIds) {
        ResponseData j = new ResponseData();
        try {
            // 删除用户角色的关系
            roleUserService.delete(roleId);
            // 添加用户角色的关系
            if (StringUtils.isNoneBlank(userIds)) {
                List<SysRoleUser> list = new ArrayList<SysRoleUser>();
                String[] userIdsStr = userIds.trim().split(",");
                for (String userId : userIdsStr) {
                    list.add(new SysRoleUser(Long.valueOf(userId), roleId));
                }
                roleUserService.batchInsert(list);
            }
            j = new ResponseData(true, "操作成功,用户重新登录即可生效", "");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }

    @Transactional
    @Override
    public ResponseData distributeUserRoles(String userIds, String roleIds) {
        if (StringUtils.isBlank(userIds) || StringUtils.isBlank(roleIds)) {
            throw new IllegalArgumentException("用户id、角色id不能为空");
        }
        String[] userIdsArray = userIds.trim().split(",");

        for (String userId : userIdsArray) {
            // 删除该用户与角色的关系
            Long[] userIdArr = {Long.valueOf(userId)};
            roleUserService.deleteBatch(userIdArr);
            // 建立该用户与角色的关系
            List<SysRoleUser> list = new ArrayList<>();
            String[] roleIdsArray = roleIds.trim().split(",");
            for (String roleId : roleIdsArray) {
                list.add(new SysRoleUser(Long.valueOf(userId), Long.valueOf(roleId)));
            }
            roleUserService.batchInsert(list);
        }

        return new ResponseData(true, "分配角色成功", "");
    }



    @Transactional
    @Override
    public ResponseData distributeSource(Long roleId, String pageIds, String dataIds) {

        ResponseData j = new ResponseData();
        try {

            List<SysRoleDataType> sysRoleDataTypes = new ArrayList<>();

            //添加角色   页面资源
            if (StringUtils.isNotBlank(pageIds)) {
                String[] pageIdsArray = pageIds.trim().split(",");
                for (String pageId : pageIdsArray) {
                    SysRoleDataType sysRoleDataType = new SysRoleDataType();
                    sysRoleDataType.setDataId(Integer.parseInt(pageId));
                    sysRoleDataType.setRoleId(Integer.parseInt(roleId.toString().toString()));
                    sysRoleDataType.setDataLevel(SysRoleDataType.DataLevelStatus.PAGE);
                    sysRoleDataTypes.add(sysRoleDataType);
                }
            }

            //添加角色   文档类型
            if (StringUtils.isNotBlank(dataIds)) {
                String[] dataIdsArray = dataIds.trim().split(",");
                for (String dataId : dataIdsArray) {
                    SysRoleDataType sysRoleDataType = new SysRoleDataType();
                    sysRoleDataType.setDataId(Integer.parseInt(dataId));
                    sysRoleDataType.setRoleId(Integer.parseInt(roleId.toString().toString()));
                    sysRoleDataType.setDataLevel(SysRoleDataType.DataLevelStatus.DOCTYPE);
                    sysRoleDataTypes.add(sysRoleDataType);
                }
            }


            j = new ResponseData(true, "权限分配完成", "");
        } catch (Exception e) {
            e.printStackTrace();
            throw new IllegalArgumentException("分配权限异常！");
        }
        return j;
    }


}
