package com.swxa.prp.demo.entity;

import java.util.Date;

/**
 * <AUTHOR> @Date 2025-08-05
 */
public class BussProdConfigManagePO {
    /**
     * 
     */
    private Integer id;

    /**
     * 
     */
    private String configType;

    /**
     * 
     */
    private String configCode;

    /**
     * 
     */
    private String chassisName;

    /**
     * 
     */
    private String powerName;

    /**
     * 
     */
    private String mainBoardName;

    /**
     * 
     */
    private String cpuName;

    /**
     * 
     */
    private String memoryName;

    /**
     * 
     */
    private String storageDevice;

    /**
     * 
     */
    private String electDisk;

    /**
     * 
     */
    private String pwdCard;

    /**
     * 
     */
    private Integer projectId;

    /**
     * 
     */
    private Date createTime;

    /**
     * 
     */
    private String networkCard;

    /**
     * 
     */
    private String otherInfo;

    /**
     * 
     */
    private String otherDesc;

    /**
     * 
     */
    private String infoAll;

    /**
     * 
     */
    private String attachFileIds;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_buss_prod_config_manage.id
     *
     * @return the value of t_buss_prod_config_manage.id
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_buss_prod_config_manage.id
     *
     * @param id the value for t_buss_prod_config_manage.id
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_buss_prod_config_manage.config_type
     *
     * @return the value of t_buss_prod_config_manage.config_type
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public String getConfigType() {
        return configType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_buss_prod_config_manage.config_type
     *
     * @param configType the value for t_buss_prod_config_manage.config_type
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public void setConfigType(String configType) {
        this.configType = configType == null ? null : configType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_buss_prod_config_manage.config_code
     *
     * @return the value of t_buss_prod_config_manage.config_code
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public String getConfigCode() {
        return configCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_buss_prod_config_manage.config_code
     *
     * @param configCode the value for t_buss_prod_config_manage.config_code
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public void setConfigCode(String configCode) {
        this.configCode = configCode == null ? null : configCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_buss_prod_config_manage.chassis_name
     *
     * @return the value of t_buss_prod_config_manage.chassis_name
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public String getChassisName() {
        return chassisName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_buss_prod_config_manage.chassis_name
     *
     * @param chassisName the value for t_buss_prod_config_manage.chassis_name
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public void setChassisName(String chassisName) {
        this.chassisName = chassisName == null ? null : chassisName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_buss_prod_config_manage.power_name
     *
     * @return the value of t_buss_prod_config_manage.power_name
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public String getPowerName() {
        return powerName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_buss_prod_config_manage.power_name
     *
     * @param powerName the value for t_buss_prod_config_manage.power_name
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public void setPowerName(String powerName) {
        this.powerName = powerName == null ? null : powerName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_buss_prod_config_manage.main_board_name
     *
     * @return the value of t_buss_prod_config_manage.main_board_name
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public String getMainBoardName() {
        return mainBoardName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_buss_prod_config_manage.main_board_name
     *
     * @param mainBoardName the value for t_buss_prod_config_manage.main_board_name
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public void setMainBoardName(String mainBoardName) {
        this.mainBoardName = mainBoardName == null ? null : mainBoardName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_buss_prod_config_manage.cpu_name
     *
     * @return the value of t_buss_prod_config_manage.cpu_name
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public String getCpuName() {
        return cpuName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_buss_prod_config_manage.cpu_name
     *
     * @param cpuName the value for t_buss_prod_config_manage.cpu_name
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public void setCpuName(String cpuName) {
        this.cpuName = cpuName == null ? null : cpuName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_buss_prod_config_manage.memory_name
     *
     * @return the value of t_buss_prod_config_manage.memory_name
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public String getMemoryName() {
        return memoryName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_buss_prod_config_manage.memory_name
     *
     * @param memoryName the value for t_buss_prod_config_manage.memory_name
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public void setMemoryName(String memoryName) {
        this.memoryName = memoryName == null ? null : memoryName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_buss_prod_config_manage.storage_device
     *
     * @return the value of t_buss_prod_config_manage.storage_device
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public String getStorageDevice() {
        return storageDevice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_buss_prod_config_manage.storage_device
     *
     * @param storageDevice the value for t_buss_prod_config_manage.storage_device
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public void setStorageDevice(String storageDevice) {
        this.storageDevice = storageDevice == null ? null : storageDevice.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_buss_prod_config_manage.elect_disk
     *
     * @return the value of t_buss_prod_config_manage.elect_disk
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public String getElectDisk() {
        return electDisk;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_buss_prod_config_manage.elect_disk
     *
     * @param electDisk the value for t_buss_prod_config_manage.elect_disk
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public void setElectDisk(String electDisk) {
        this.electDisk = electDisk == null ? null : electDisk.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_buss_prod_config_manage.pwd_card
     *
     * @return the value of t_buss_prod_config_manage.pwd_card
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public String getPwdCard() {
        return pwdCard;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_buss_prod_config_manage.pwd_card
     *
     * @param pwdCard the value for t_buss_prod_config_manage.pwd_card
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public void setPwdCard(String pwdCard) {
        this.pwdCard = pwdCard == null ? null : pwdCard.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_buss_prod_config_manage.project_id
     *
     * @return the value of t_buss_prod_config_manage.project_id
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public Integer getProjectId() {
        return projectId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_buss_prod_config_manage.project_id
     *
     * @param projectId the value for t_buss_prod_config_manage.project_id
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public void setProjectId(Integer projectId) {
        this.projectId = projectId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_buss_prod_config_manage.create_time
     *
     * @return the value of t_buss_prod_config_manage.create_time
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_buss_prod_config_manage.create_time
     *
     * @param createTime the value for t_buss_prod_config_manage.create_time
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_buss_prod_config_manage.network_card
     *
     * @return the value of t_buss_prod_config_manage.network_card
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public String getNetworkCard() {
        return networkCard;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_buss_prod_config_manage.network_card
     *
     * @param networkCard the value for t_buss_prod_config_manage.network_card
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public void setNetworkCard(String networkCard) {
        this.networkCard = networkCard == null ? null : networkCard.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_buss_prod_config_manage.other_info
     *
     * @return the value of t_buss_prod_config_manage.other_info
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public String getOtherInfo() {
        return otherInfo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_buss_prod_config_manage.other_info
     *
     * @param otherInfo the value for t_buss_prod_config_manage.other_info
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public void setOtherInfo(String otherInfo) {
        this.otherInfo = otherInfo == null ? null : otherInfo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_buss_prod_config_manage.other_desc
     *
     * @return the value of t_buss_prod_config_manage.other_desc
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public String getOtherDesc() {
        return otherDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_buss_prod_config_manage.other_desc
     *
     * @param otherDesc the value for t_buss_prod_config_manage.other_desc
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public void setOtherDesc(String otherDesc) {
        this.otherDesc = otherDesc == null ? null : otherDesc.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_buss_prod_config_manage.info_all
     *
     * @return the value of t_buss_prod_config_manage.info_all
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public String getInfoAll() {
        return infoAll;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_buss_prod_config_manage.info_all
     *
     * @param infoAll the value for t_buss_prod_config_manage.info_all
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public void setInfoAll(String infoAll) {
        this.infoAll = infoAll == null ? null : infoAll.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_buss_prod_config_manage.attach_file_ids
     *
     * @return the value of t_buss_prod_config_manage.attach_file_ids
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public String getAttachFileIds() {
        return attachFileIds;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_buss_prod_config_manage.attach_file_ids
     *
     * @param attachFileIds the value for t_buss_prod_config_manage.attach_file_ids
     *
     * @mbg.generated Tue Aug 05 16:38:41 CST 2025
     */
    public void setAttachFileIds(String attachFileIds) {
        this.attachFileIds = attachFileIds == null ? null : attachFileIds.trim();
    }
}