package com.swxa.prp.business.createnum.enums;

import static com.swxa.prp.business.createnum.constant.CreateNumConstant.DATE_FORMAT;
import static com.swxa.prp.business.createnum.constant.CreateNumConstant.DATE_FORMAT_SEP;

public enum BussTypeEnum {
    ANNOUNCEMENT("announcement","ANC","","%02d","公告"),
    QUOTATION("hwswServiceOper","ZDYBD75-","","%03d","软硬件/服务组合报价说明"),
    KNOWLEDGE_MANAGE("knowledgeManage","DOC","","%02d","知识管理"),
    CASE_SUMMARY("caseSummary","SWXA-XXX-XXXXX","","%05d","案例汇总"),
    ATTACHMENT("attachment","ATT","","%05d","附件管理"),
    WORK_REPORT("workReport","WRPT","","%05d","工作汇报列表"),
    BUSINESS_CUSTOMER_MANAGE("businessCustomerManage","ACC","","%05d","客户档案"),
    FOLLOW_PROJECT_OPPORTUNITY("businessOpportunity","MKT","yyMM","%04d","项目机会跟进"),
    CONTACT_PERSON("businessContact","SWXA-CON","","%04d","联系人"),
    ADVANCE_ORDER("advanceOrder","SWXA-DZ-",DATE_FORMAT,"%02d","垫资订单通知"),
    CONTRACT("contractOrder","SWXA-XXX-XXXXX","","%05d","合同订单"),
    CONTRACT_SALE_INVOICE("contractSaleInvoice","INV","","%05d","销售发票"),
    RECEIPT("contractReceipt","GR","","%05d","收款单/回款单"),
    CONTRACT_INTERNAL("contractInternal","SWXA-", DATE_FORMAT_SEP,"%02d","内部关联合同"),
    TRANSFER_ORDER("transferOrder","SWXA-DHZ-", DATE_FORMAT_SEP,"%05d","调货单"),
    CONTRACT_PAYMENT_PLAN("contractPaymentPlan","GP", "","%05d","回款计划"),
    SHIPPING_NOTICE("shippingNotice","SWXA-", DATE_FORMAT_SEP,"%05d","发货通知单"),
    SPECIAL_SUPPLY("SupplySpecial","TQJY-","","%04d","特殊供货申请"),
    RETURN_PRODUCT("returnProduct","SWXA-FHD-", DATE_FORMAT_SEP,"%04d","返回产品跟进"),
    PROJECT_AUTH("projectAuth","PA", DATE_FORMAT_SEP,"%04d","项目授权"),
    DEVICE_RECORD("deviceRecord","EQFILE", DATE_FORMAT_SEP,"%05d","设备档案"),
    PMO("PMO","SWXA-PMO-", DATE_FORMAT_SEP,"%04d","PMO"),
    ;

    private String type;

    private String prefix;

    private String dateFormat;

    private String strFormat;

    private String zhMean;

    public static BussTypeEnum getByType(String type) {
        BussTypeEnum[] bussTypeEnums = values();
        for (BussTypeEnum bussTypeEnum : bussTypeEnums) {
            if (type.equals(bussTypeEnum.getType())) {
                return bussTypeEnum;
            }
        }
        return null;
    }

    BussTypeEnum(String type, String prefix, String dateFormat, String strFormat, String zhMean) {
        this.type = type;
        this.prefix = prefix;
        this.dateFormat = dateFormat;
        this.strFormat = strFormat;
        this.zhMean = zhMean;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public String getDateFormat() {
        return dateFormat;
    }

    public void setDateFormat(String dateFormat) {
        this.dateFormat = dateFormat;
    }

    public String getStrFormat() {
        return strFormat;
    }

    public void setStrFormat(String strFormat) {
        this.strFormat = strFormat;
    }

    public String getZhMean() {
        return zhMean;
    }

    public void setZhMean(String zhMean) {
        this.zhMean = zhMean;
    }
}
