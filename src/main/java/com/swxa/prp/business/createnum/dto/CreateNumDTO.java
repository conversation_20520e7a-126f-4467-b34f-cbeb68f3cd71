package com.swxa.prp.business.createnum.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

import static com.swxa.prp.business.createnum.constant.CreateNumErrorCodeConstant.ERROR_CODE_1010100000;

/**
 * @ Date：2025-08-14-11:09
 * @ Version：1.0
 * @ Description：
 */
@Data
public class CreateNumDTO {

    // 业务类型名称
    @NotNull(message = ERROR_CODE_1010100000)
    private String type;

}
