package com.swxa.prp.business.createnum.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.swxa.prp.business.createnum.dto.CreateNumDTO;
import com.swxa.prp.business.createnum.entity.BussCreateNumPO;
import com.swxa.prp.business.createnum.enums.BussTypeEnum;
import com.swxa.prp.business.createnum.mapper.BussCreateNumMapper;
import com.swxa.prp.business.createnum.service.BussCreateNumService;
import com.swxa.prp.exception.SwPrpException;
import com.swxa.prp.util.MyDateUtil;
import com.swxa.prp.util.MyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.swxa.prp.business.createnum.constant.CreateNumConstant.*;
import static com.swxa.prp.business.createnum.constant.CreateNumErrorCodeConstant.ERROR_CODE_1010100001;

/**
 * @ Date：2025-08-14-11:04
 * @ Version：1.0
 * @ Description：
 */
@Service
@Slf4j
public class BussCreateNumServiceImpl implements BussCreateNumService {

    @Autowired
    private BussCreateNumMapper bussCreateNumMapper;

    /**
     * 生成业务编号
     * @param dto
     * @return
     */
    @Override
    public synchronized String createNum(CreateNumDTO dto) {
        BussTypeEnum bussType = BussTypeEnum.getByType(dto.getType());
        if (bussType == null) {
            throw new SwPrpException(ERROR_CODE_1010100001);
        }
        String result = bussType.getPrefix() + MyDateUtil.getNowDateNum(bussType.getDateFormat());
        QueryWrapper<BussCreateNumPO> queryWrapper = Wrappers.query();
        queryWrapper.lambda().eq(BussCreateNumPO::getBussType, bussType.getType());
        BussCreateNumPO bussCreateNumPO = bussCreateNumMapper.selectOne(queryWrapper);
        if (bussCreateNumPO == null) {
            // 新增
            BussCreateNumPO bussCreateNumPO1 = new BussCreateNumPO();
            bussCreateNumPO1.setId(MyUtil.getRandomID());
            bussCreateNumPO1.setBussType(bussType.getType());
            bussCreateNumPO1.setBussName(bussType.getZhMean());
            bussCreateNumPO1.setLatestNum(START_NUM);
            bussCreateNumMapper.insertSelective(bussCreateNumPO1);
            result = result + String.format(bussType.getStrFormat(), START_NUM);
        } else {
            Integer newNum = bussCreateNumPO.getLatestNum() + 1;
            bussCreateNumPO.setLatestNum(newNum);
            bussCreateNumMapper.updateByPrimaryKeySelective(bussCreateNumPO);
            result = result + String.format(bussType.getStrFormat(), bussCreateNumPO.getLatestNum());
        }
        return result;
    }

    /**
     * 更新编码
     * @param type
     */
    @Override
    public void updateNum(String type) {
        UpdateWrapper<BussCreateNumPO> updateWrapper = Wrappers.update();
        updateWrapper.lambda().eq(BussCreateNumPO::getBussType, type).setSql("latest_num = CAST(latest_num AS UNSIGNED) + 1");
        bussCreateNumMapper.update(null, updateWrapper);
    }
}
