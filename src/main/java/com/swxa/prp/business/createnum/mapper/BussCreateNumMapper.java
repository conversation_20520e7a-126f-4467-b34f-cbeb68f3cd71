package com.swxa.prp.business.createnum.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.swxa.prp.business.createnum.entity.BussCreateNumPO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface BussCreateNumMapper extends BaseMapper<BussCreateNumPO> {
    int deleteByPrimaryKey(String id);

    int insertSelective(BussCreateNumPO row);

    BussCreateNumPO selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(BussCreateNumPO row);

    int updateByPrimaryKey(BussCreateNumPO row);
}