package com.swxa.prp.business.createnum.entity;

import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <AUTHOR> @Date 2025-08-14
 */
@TableName("t_buss_create_num")
public class BussCreateNumPO {
    /**
     * 主键id
     */
    private String id;

    /**
     * 业务类型
     */
    private String bussType;

    /**
     * 业务类型名称
     */
    private String bussName;

    /**
     * 最新编号
     */
    private Integer latestNum;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_buss_create_num.id
     *
     * @return the value of t_buss_create_num.id
     *
     * @mbg.generated Thu Aug 14 11:34:32 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_buss_create_num.id
     *
     * @param id the value for t_buss_create_num.id
     *
     * @mbg.generated Thu Aug 14 11:34:32 CST 2025
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_buss_create_num.buss_type
     *
     * @return the value of t_buss_create_num.buss_type
     *
     * @mbg.generated Thu Aug 14 11:34:32 CST 2025
     */
    public String getBussType() {
        return bussType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_buss_create_num.buss_type
     *
     * @param bussType the value for t_buss_create_num.buss_type
     *
     * @mbg.generated Thu Aug 14 11:34:32 CST 2025
     */
    public void setBussType(String bussType) {
        this.bussType = bussType == null ? null : bussType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_buss_create_num.buss_name
     *
     * @return the value of t_buss_create_num.buss_name
     *
     * @mbg.generated Thu Aug 14 11:34:32 CST 2025
     */
    public String getBussName() {
        return bussName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_buss_create_num.buss_name
     *
     * @param bussName the value for t_buss_create_num.buss_name
     *
     * @mbg.generated Thu Aug 14 11:34:32 CST 2025
     */
    public void setBussName(String bussName) {
        this.bussName = bussName == null ? null : bussName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_buss_create_num.latest_num
     *
     * @return the value of t_buss_create_num.latest_num
     *
     * @mbg.generated Thu Aug 14 11:34:32 CST 2025
     */
    public Integer getLatestNum() {
        return latestNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_buss_create_num.latest_num
     *
     * @param latestNum the value for t_buss_create_num.latest_num
     *
     * @mbg.generated Thu Aug 14 11:34:32 CST 2025
     */
    public void setLatestNum(Integer latestNum) {
        this.latestNum = latestNum;
    }
}