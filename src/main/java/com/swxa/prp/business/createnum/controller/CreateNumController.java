package com.swxa.prp.business.createnum.controller;

import com.swxa.prp.business.createnum.dto.CreateNumDTO;
import com.swxa.prp.business.createnum.service.BussCreateNumService;
import com.swxa.prp.util.ResponseData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ Date：2025-08-14-11:00
 * @ Version：1.0
 * @ Description：
 */
@RestController
@RequestMapping("/create")
public class CreateNumController {

    @Autowired
    private BussCreateNumService bussCreateNumService;

    /**
     * 生成业务单据编号，规则：业务编码-日期-编号
     * @param dto
     * @return
     */
    @PostMapping("/num")
    public ResponseData createNum(@Validated @RequestBody CreateNumDTO dto) {
        String num = bussCreateNumService.createNum(dto);
        return ResponseData.ok(true, num);
    }
    /**
     * 更新编号测试
     * @return
     */
    @PostMapping("/update")
    public ResponseData updateNum(String type) {
        bussCreateNumService.updateNum(type);
        return ResponseData.ok();
    }
}
