package com.swxa.prp.business.contract.service;

import com.swxa.prp.business.contract.dto.ContractPersonPercentAddDTO;
import com.swxa.prp.business.contract.dto.*;
import com.swxa.prp.business.contract.vo.ContractPersonPercentVO;
import com.swxa.prp.util.TableResultUtil;

import java.util.List;

/**
 * @Description: 合同订单人员成分占比业务逻辑层
 * @Author: zhangweicheng
 * @Date: 2025/7/19
 */
public interface ContractPersonPercentService {

    /**
     * @Description: 添加
     * @Param: [contractPersonPercentAddDTO]
     * @return: void
     * @Author: zhangweicheng
     * @Date: 2025/7/19
     */

    void insert(ContractPersonPercentAddDTO contractPersonPercentAddDTO);

    /**
     * @Description: 列表查询
     * @Param: [contractPersonPercentQueryListPO]
     * @return: java.util.List<com.swxa.prp.business.contract.vo.ContractPersonPercentVO>
     * @Author: zhangweicheng
     * @Date: 2025/7/19
     */


    List<ContractPersonPercentVO> queryList(ContractPersonPercentQueryListDTO contractPersonPercentQueryListPO);

    /**
     * @Description: 更新
     * @Param: [contractPersonPercentEditDTO]
     * @return: void
     * @Author: zhangweicheng
     * @Date: 2025/7/19
     */

    void updateById(ContractPersonPercentEditDTO contractPersonPercentEditDTO);

    /**
     * @Description: 删除
     * @Param: [contractPersonPercentDeleteDTO]
     * @return: void
     * @Author: zhangweicheng
     * @Date: 2025/7/19
     */

    void deleteById(ContractPersonPercentDeleteDTO contractPersonPercentDeleteDTO);


    /**
     * @Description: 分页查询
     * @Param: [contractPersonPercentQueryPageDTO]
     * @return: com.swxa.prp.util.TableResultUtil
     * @Author: zhangweicheng
     * @Date: 2025/7/19
     */

    TableResultUtil queryToPage(ContractPersonPercentQueryPageDTO contractPersonPercentQueryPageDTO);


    /**
     * @Description: 根据合同的id删除人员成分占比信息
     * @Param: [contractId]
     * @return: void
     * @Author: zhangweicheng
     * @Date: 2025/7/20
     */

    void deleteByContractId(String contractId);
}
