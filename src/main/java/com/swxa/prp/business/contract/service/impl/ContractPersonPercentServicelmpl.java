package com.swxa.prp.business.contract.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.ISelect;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.swxa.prp.business.contract.dao.ContractPersonPercentMapper;
import com.swxa.prp.business.contract.dto.*;
import com.swxa.prp.business.contract.model.ContractPersonPercentPO;
import com.swxa.prp.business.contract.service.ContractPersonPercentService;
import com.swxa.prp.business.contract.vo.ContractPersonPercentVO;
import com.swxa.prp.business.contract.vo.InternalContractServiceVO;
import com.swxa.prp.constant.SwErrorCodeConstant;
import com.swxa.prp.exception.SwPrpException;
import com.swxa.prp.util.MyUtil;
import com.swxa.prp.util.TableResultUtil;
import com.swxa.prp.util.UserUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 合同订单人员分成占比业务逻辑实现
 * @Author: zhangweicheng
 * @Date: 2025/7/19
 */

@Service
public class ContractPersonPercentServicelmpl implements ContractPersonPercentService {

    @Autowired
    private ContractPersonPercentMapper contractPersonPercentMapper;

    private static final Logger log = LoggerFactory.getLogger("dayLogger");


    @Transactional
    @Override
    public void insert(ContractPersonPercentAddDTO contractPersonPercentAddDTO) {

        log.info("添加人员分成占比，{}", contractPersonPercentAddDTO);

        ContractPersonPercentPO contractPersonPercentPO = new ContractPersonPercentPO();

        BeanUtils.copyProperties(contractPersonPercentAddDTO, contractPersonPercentPO);

        //设置参数
        contractPersonPercentPO.setId(MyUtil.getRandomID());
        contractPersonPercentPO.setCreateId(UserUtil.getLoginUserId());
        contractPersonPercentPO.setCreateTime(new Date());
        contractPersonPercentPO.setUpdateTime(new Date());

        //添加
        contractPersonPercentMapper.insertSelective(contractPersonPercentPO);

    }


    @Override
    @Transactional
    public void deleteById(ContractPersonPercentDeleteDTO contractPersonPercentDeleteDTO) {


        //获取用户id
        String id = contractPersonPercentDeleteDTO.getId();
        if (Objects.isNull(id)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_ID_NULL);
        }

        //删除
        contractPersonPercentMapper.deleteByPrimaryKey(id);

    }


    @Override
    @Transactional
    public void updateById(ContractPersonPercentEditDTO contractPersonPercentEditDTO) {


        ContractPersonPercentPO contractPersonPercentPO = new ContractPersonPercentPO();

        String id = contractPersonPercentEditDTO.getId();
        if (Objects.isNull(id)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_ID_NULL);
        }
        //赋值
        BeanUtils.copyProperties(contractPersonPercentEditDTO, contractPersonPercentPO);

        contractPersonPercentPO.setId(id);

        contractPersonPercentMapper.updateByPrimaryKeySelective(contractPersonPercentPO);
    }


    @Override
    public List<ContractPersonPercentVO> queryList(ContractPersonPercentQueryListDTO contractPersonPercentQueryListPO) {

        //列表查询
        List<ContractPersonPercentPO> contractOrderPOS = contractPersonPercentMapper.selectList(contractPersonPercentQueryListPO);

        List<ContractPersonPercentVO> resListVOS = new ArrayList<>();

        //不为空处理返回结果
        if (CollUtil.isNotEmpty(contractOrderPOS)) {
            for (ContractPersonPercentPO contractPersonPercentPO : contractOrderPOS) {
                ContractPersonPercentVO contractSupportServicePO = new ContractPersonPercentVO();
                BeanUtils.copyProperties(contractPersonPercentPO, contractSupportServicePO);
                resListVOS.add(contractSupportServicePO);
            }
        }

        return resListVOS;
    }


    @Override
    public TableResultUtil queryToPage(ContractPersonPercentQueryPageDTO contractPersonPercentQueryPageDTO) {

        //分页查询
        PageInfo<InternalContractServiceVO> pageInfo = PageHelper.startPage(contractPersonPercentQueryPageDTO.getPageNum(), contractPersonPercentQueryPageDTO.getPageSize()).doSelectPageInfo(new ISelect() {

            @Override
            public void doSelect() {
                ContractPersonPercentQueryListDTO contractPersonPercentQueryListPO = new ContractPersonPercentQueryListDTO();

                //设置各种赋值条件
                queryList(contractPersonPercentQueryListPO);
            }
        });

        return TableResultUtil.buildTableResult(pageInfo);
    }

    @Override
    public void deleteByContractId(String contractId) {

        if (StringUtils.isBlank(contractId)){
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_ID_NULL);
        }

        //2.查询出该合同的所有人员占比信息
        ContractPersonPercentQueryListDTO contractPersonPercentQueryListPO = new ContractPersonPercentQueryListDTO();
        contractPersonPercentQueryListPO.setContractId(contractId);
        List<ContractPersonPercentVO> contractPersonPercentVOS = queryList(contractPersonPercentQueryListPO);
        //删除人员分成占比
        if (CollUtil.isNotEmpty(contractPersonPercentVOS)) {
            for (ContractPersonPercentVO contractPersonPercentVO : contractPersonPercentVOS) {
                contractPersonPercentMapper.deleteByPrimaryKey(contractPersonPercentVO.getId());
            }
        }
    }


}
