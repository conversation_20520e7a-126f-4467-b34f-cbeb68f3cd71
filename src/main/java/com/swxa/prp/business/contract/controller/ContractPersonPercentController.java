package com.swxa.prp.business.contract.controller;

import com.swxa.prp.business.contract.dto.*;
import com.swxa.prp.business.contract.service.ContractPersonPercentService;
import com.swxa.prp.business.contract.vo.ContractPersonPercentVO;
import com.swxa.prp.util.ResponseData;
import com.swxa.prp.util.TableResultUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * @Description: 合同订单人员分成占比
 * @Author: zhangweicheng
 * @Date: 2025/7/18
 */

@Controller
@RequestMapping("/contract/person/percent")
public class ContractPersonPercentController {


    @Autowired
    private ContractPersonPercentService contractPersonPercentService;

    private static final Logger log = LoggerFactory.getLogger("dayLogger");


    /**
     * @Description: 新增
     * @Param: [contractPersonPercentAddDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/19
     */


    @ResponseBody
    @PostMapping(value = "/insert")
    public ResponseData insert(@Validated @RequestBody ContractPersonPercentAddDTO contractPersonPercentAddDTO) {

        log.info("执行新增操作");

        contractPersonPercentService.insert(contractPersonPercentAddDTO);

        return ResponseData.ok();
    }

    /**
     * @Description: 列表查询
     * @Param: [contractPersonPercentQueryListDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/19
     */


    @ResponseBody
    @PostMapping(value = "/queryList")
    public ResponseData queryList(@Validated @RequestBody ContractPersonPercentQueryListDTO contractPersonPercentQueryListDTO) {

        List<ContractPersonPercentVO> contractSupportServiceVOS = contractPersonPercentService.queryList(contractPersonPercentQueryListDTO);

        return ResponseData.ok(true, contractSupportServiceVOS);
    }


    /**
     * @Description: 分页查询
     * @Param: [contractPersonPercentQueryPageDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/19
     */


    @ResponseBody
    @PostMapping(value = "/queryToPage")
    public ResponseData queryToPage(@Validated @RequestBody ContractPersonPercentQueryPageDTO contractPersonPercentQueryPageDTO) {

        TableResultUtil resultUtil = contractPersonPercentService.queryToPage(contractPersonPercentQueryPageDTO);

        return ResponseData.ok(true, resultUtil);
    }


    /**
     * @Description: 根据ID删除关系
     * @Param: [contractPersonPercentDeleteDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/19
     */


    @ResponseBody
    @PostMapping(value = "/deleteById")
    public ResponseData deletePersonById(@Validated @RequestBody ContractPersonPercentDeleteDTO contractPersonPercentDeleteDTO) {
        contractPersonPercentService.deleteById(contractPersonPercentDeleteDTO);
        return ResponseData.ok();
    }


    /**
     * @Description: 更新
     * @Param: [contractPersonPercentEditDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/19
     */


    @ResponseBody
    @PostMapping(value = "/updateById")
    public ResponseData updateById(@Validated @RequestBody ContractPersonPercentEditDTO contractPersonPercentEditDTO) {

        log.info("更新");
        contractPersonPercentService.updateById(contractPersonPercentEditDTO);
        return ResponseData.ok();
    }


}
