package com.swxa.prp.business.contract.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.swxa.prp.business.act.conditionFields.dto.BussProcessStatusDTO;
import com.swxa.prp.business.contract.dao.ContractCollectPaymentPlanMapper;
import com.swxa.prp.business.contract.dao.ContractOrderMapper;
import com.swxa.prp.business.contract.dto.*;
import com.swxa.prp.business.contract.model.ContractCollectPaymentPlanPO;
import com.swxa.prp.business.contract.model.ContractOrderPO;
import com.swxa.prp.business.contract.service.ContractCollectPaymentPlanService;
import com.swxa.prp.business.contract.service.ContractOrderService;
import com.swxa.prp.business.contract.service.PaymentReceiptOrderPlanService;
import com.swxa.prp.business.contract.vo.*;
import com.swxa.prp.business.dataauth._enum.CrmBussTypeEnum;
import com.swxa.prp.business.dataauth.dataauthrules.service.DataAuthService;
import com.swxa.prp.constant.SwErrorCodeConstant;
import com.swxa.prp.exception.SwPrpException;
import com.swxa.prp.util.MyUtil;
import com.swxa.prp.util.SwValidatorUtil;
import com.swxa.prp.util.TableResultUtil;
import com.swxa.prp.util.UserUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 合同订单付款计划业务逻辑实现
 * @Author: zhangweicheng
 * @Date: 2025/7/19
 */

@Service
public class ContractCollectPaymentPlanServiceImpl implements ContractCollectPaymentPlanService {

    @Autowired
    private ContractCollectPaymentPlanMapper contractCollectPaymentPlanMapper;

    @Autowired
    private ContractOrderMapper contractOrderMapper;
    @Autowired
    private ContractOrderService contractOrderService;


    @Lazy
    @Autowired
    private DataAuthService dataAuthService;


    @Autowired
    private PaymentReceiptOrderPlanService paymentReceiptOrderPlanService;


    private static final Logger log = LoggerFactory.getLogger("dayLogger");

    /**
     * @Description: 插入数据
     * @Param: [addContractCollectPaymentPlan]
     * @return: void
     * @Author: lwei
     * @Date: 2025/7/25
     */

    @Transactional
    @Override
    public void insert(ContractCollectPaymentPlanAddDTO addContractCollectPaymentPlan) {

        log.info("ContractCollectPaymentPlanServiceImpl.addContractPaymentConditio {}", addContractCollectPaymentPlan);

        String paymentPlanCode = addContractCollectPaymentPlan.getPaymentPlanCode();
        if (StringUtils.isBlank(paymentPlanCode)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_PAYMENT_PLAN_CODE_NULL);
        }

        ContractCollectPaymentPlanQueryListDTO contractCollectPaymentPlanQueryListDTO = new ContractCollectPaymentPlanQueryListDTO();
        contractCollectPaymentPlanQueryListDTO.setPaymentPlanCode(paymentPlanCode);

        List<ContractCollectPaymentPlanVO> contractCollectPaymentPlanVOList = queryList(contractCollectPaymentPlanQueryListDTO);
        if (CollUtil.isNotEmpty(contractCollectPaymentPlanVOList)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_PAYMENT_PLAN_EXISTED);
        }

        ContractCollectPaymentPlanPO contractCollectPaymentPlanPO = new ContractCollectPaymentPlanPO();

        //设置属性值
        BeanUtils.copyProperties(addContractCollectPaymentPlan, contractCollectPaymentPlanPO);

        contractCollectPaymentPlanPO.setId(MyUtil.getRandomID());
        //设置用户ID
        contractCollectPaymentPlanPO.setCreateId(UserUtil.getLoginUserId());
        //设置时间
        contractCollectPaymentPlanPO.setCreateTime(new Date());
        contractCollectPaymentPlanPO.setUpdateTime(new Date());

        contractCollectPaymentPlanMapper.insertSelective(contractCollectPaymentPlanPO);

    }


    /**
     * @Description: 根据id删除数据
     * @Param: [deleteCollectPaymentPlan]
     * @return: void
     * @Author: lwei
     * @Date: 2025/7/25
     */
    @Transactional
    @Override
    public void deleteById(ContractCollectPaymentPlanDeleteDTO deleteCollectPaymentPlan) {
        log.info("ContractCollectPaymentPlanServiceImpl.deleteContractCollectPaymentPlan {}", deleteCollectPaymentPlan);
        String id = deleteCollectPaymentPlan.getId();
        if (Objects.isNull(id)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_PAYMENT_PLAN_ID_NULL);
        }

        ContractCollectPaymentPlanPO contractCollectPaymentPlanPO = contractCollectPaymentPlanMapper.selectByPrimaryKey(id);
        if (Objects.nonNull(contractCollectPaymentPlanPO)) {
            String paymentPlanCode = contractCollectPaymentPlanPO.getPaymentPlanCode();
            if (StringUtils.isNotBlank(paymentPlanCode)) {
                //删除回款计划的时候，删除回款单里面与回款计划相关的汇款钱信息
                PaymentReceiptOrderPlanQueryDTO queryDTO = new PaymentReceiptOrderPlanQueryDTO();
                queryDTO.setPaymentPlanCode(paymentPlanCode);
                List<PaymentReceiptOrderPlanVO> paymentReceiptOrderPlanVOS = paymentReceiptOrderPlanService.queryList(queryDTO);
                if (CollUtil.isNotEmpty(paymentReceiptOrderPlanVOS)) {
                    for (PaymentReceiptOrderPlanVO paymentReceiptOrderPlanVO : paymentReceiptOrderPlanVOS) {
                        String planId = paymentReceiptOrderPlanVO.getId();
                        if (StringUtils.isNotBlank(planId)) {
                            PaymentReceiptOrderPlanDeleteDTO deleteDTO = new PaymentReceiptOrderPlanDeleteDTO();
                            deleteDTO.setId(planId);
                            paymentReceiptOrderPlanService.deleteByID(deleteDTO);
                        }
                    }
                }
            }
        }


        //删除回款计划
        contractCollectPaymentPlanMapper.deleteByPrimaryKey(id);

    }


    /**
     * @Description: 根据id修改数据
     * @Param: [updateContractCollectPaymentPlan]
     * @return: void
     * @Author: lwei
     * @Date: 2025/7/25
     */
    @Transactional
    @Override
    public void updateById(ContractCollectPaymentPlanUpdateDTO updateContractCollectPaymentPlan) {

        log.info("ContractCollectPaymentPlanServiceImpl.updateContractCollectPaymentPlanById {}", updateContractCollectPaymentPlan);

        ContractCollectPaymentPlanPO contractCollectPaymentPlanPO = new ContractCollectPaymentPlanPO();

        String id = updateContractCollectPaymentPlan.getId();
        if (Objects.isNull(id)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_PAYMENT_PLAN_ID_NULL);
        }

        //设置值
        BeanUtils.copyProperties(updateContractCollectPaymentPlan, contractCollectPaymentPlanPO);

        contractCollectPaymentPlanPO.setId(id);
        //设置更新时间
        contractCollectPaymentPlanPO.setUpdateTime(new Date());

        contractCollectPaymentPlanMapper.updateByPrimaryKeySelective(contractCollectPaymentPlanPO);
    }

    /**
     * @Description: 根据id查询数据，注意此处查询会查询合同的一些基本信息
     * @Param: [queryContractCollectPaymentPlan]
     * @return: com.swxa.prp.business.contract.vo.ContractCollectPaymentPlanAndBaseVO
     * @Author: lwei
     * @Date: 2025/7/25
     */

    @Override
    public ContractCollectPaymentPlanAndBaseVO queryById(ContractCollectPaymentPlanQueryDTO queryContractCollectPaymentPlan) {
        log.info("ContractCollectPaymentPlanServiceImpl.queryContractCollectPaymentPlanById {}", queryContractCollectPaymentPlan);

        //返回结果
        ContractCollectPaymentPlanAndBaseVO contractCollectPaymentPlanAndBaseVO = new ContractCollectPaymentPlanAndBaseVO();

        String id = queryContractCollectPaymentPlan.getId();
        if (Objects.isNull(id)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_PAYMENT_PLAN_ID_NULL);
        }

        //根据id查询回款计划信息
        ContractCollectPaymentPlanPO contractCollectPaymentPlanPO = contractCollectPaymentPlanMapper.selectByPrimaryKey(id);

        if (Objects.nonNull(contractCollectPaymentPlanPO)) {

            // 根据contractid查询基本信息
            ContractOrderQueryDTO contractOrderQueryDTO = new ContractOrderQueryDTO();
            contractOrderQueryDTO.setId(contractCollectPaymentPlanPO.getContractId());
            ContractOrderAllLinkInfoVO contractOrderAllLinkInfoVO = contractOrderService.queryById(contractOrderQueryDTO);
            ContractOrderVO contractOrderPO = contractOrderAllLinkInfoVO.getBaseData();

            if (Objects.nonNull(contractOrderPO)) {
                //先拷贝合同信息，避免合同表中重复属性将vo属性修改
                BeanUtils.copyProperties(contractOrderPO, contractCollectPaymentPlanAndBaseVO);
                //清空合同里面的最新回款日期
                contractCollectPaymentPlanAndBaseVO.setLastPaymentDate("");
            }

            //覆盖回款计划信息
            BeanUtils.copyProperties(contractCollectPaymentPlanPO, contractCollectPaymentPlanAndBaseVO);

            //处理当前回款计划的已收总额
            String paymentPlanCode = contractCollectPaymentPlanAndBaseVO.getPaymentPlanCode();
            if (StringUtils.isBlank(paymentPlanCode)) {
                contractCollectPaymentPlanAndBaseVO.setPaymentSum(new BigDecimal("0"));
            } else {

                //处理回款计划里面的回款总额，如果不存在设置为0
                BigDecimal paymentCountSum = contractCollectPaymentPlanAndBaseVO.getPaymentCount();
                if (Objects.isNull(paymentCountSum)) {
                    paymentCountSum = BigDecimal.ZERO;
                }

                //就一个已收总额
                BigDecimal sumBigDecimal = BigDecimal.ZERO;
                PaymentReceiptOrderPlanQueryDTO paymentReceiptOrderPlanQueryDTO = new PaymentReceiptOrderPlanQueryDTO();
                paymentReceiptOrderPlanQueryDTO.setPaymentPlanCode(paymentPlanCode);
                List<PaymentReceiptOrderPlanVO> paymentReceiptOrderPlanVOS = paymentReceiptOrderPlanService.queryList(paymentReceiptOrderPlanQueryDTO);
                if (CollUtil.isNotEmpty(paymentReceiptOrderPlanVOS)) {
                    //计算回款单里的回款计划的钱信息
                    for (PaymentReceiptOrderPlanVO paymentReceiptOrderPlanVO : paymentReceiptOrderPlanVOS) {
                        //根据此关系查询初具体的回款计划的详情信息
                        BigDecimal paymentCount = paymentReceiptOrderPlanVO.getPaymentCount();
                        if (Objects.nonNull(paymentCount)) {
                            //求和
                            sumBigDecimal = sumBigDecimal.add(paymentCount);
                        }
                    }
                }
                //设置金额   已收金额
                contractCollectPaymentPlanAndBaseVO.setPaymentSum(sumBigDecimal);
                //设置未收金额
                contractCollectPaymentPlanAndBaseVO.setCurrentPaymentCount(paymentCountSum.subtract(sumBigDecimal));
            }
        }

        return contractCollectPaymentPlanAndBaseVO;
    }

    /**
     * @Description: 根据id或编码查询详情
     * @Param: [queryContractCollectPaymentPlan]
     * @return: com.swxa.prp.business.contract.vo.ContractCollectPaymentPlanAndBaseVO
     * @Author: lwei
     * @Date: 2025/9/4
     */

    @Override
    public ContractCollectPaymentPlanAndBaseVO queryByIdCode(ContractCollectPaymentPlanQueryDTO queryContractCollectPaymentPlan) {
        //返回结果
        ContractCollectPaymentPlanAndBaseVO contractCollectPaymentPlanAndBaseVO = new ContractCollectPaymentPlanAndBaseVO();

        String id = queryContractCollectPaymentPlan.getId();
        String code = queryContractCollectPaymentPlan.getPaymentPlanCode();
        if (StringUtils.isBlank(id) && StringUtils.isBlank(code)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_PAYMENT_PLAN_ID_NULL);
        }

        ContractCollectPaymentPlanQueryListDTO contractCollectPaymentPlanQueryList = new ContractCollectPaymentPlanQueryListDTO();
        BeanUtils.copyProperties(queryContractCollectPaymentPlan, contractCollectPaymentPlanQueryList);

        List<ContractCollectPaymentPlanPO> contractCollectPaymentPlanPOS = contractCollectPaymentPlanMapper.queryList(contractCollectPaymentPlanQueryList);
        if (CollUtil.isEmpty(contractCollectPaymentPlanPOS)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_PAYMENT_PLAN_NOT_EXIST);
        }

        //根据id查询回款计划信息
        ContractCollectPaymentPlanPO contractCollectPaymentPlanPO = contractCollectPaymentPlanPOS.get(0);

        if (Objects.nonNull(contractCollectPaymentPlanPO)) {

            // 根据contractid查询基本信息
            ContractOrderQueryDTO contractOrderQueryDTO = new ContractOrderQueryDTO();
            contractOrderQueryDTO.setId(contractCollectPaymentPlanPO.getContractId());
            ContractOrderAllLinkInfoVO contractOrderAllLinkInfoVO = contractOrderService.queryById(contractOrderQueryDTO);
            ContractOrderVO contractOrderPO = contractOrderAllLinkInfoVO.getBaseData();

            if (Objects.nonNull(contractOrderPO)) {
                //先拷贝合同信息，避免合同表中重复属性将vo属性修改
                BeanUtils.copyProperties(contractOrderPO, contractCollectPaymentPlanAndBaseVO);
                //清空合同里面的最新回款日期
                contractCollectPaymentPlanAndBaseVO.setLastPaymentDate("");
            }

            //覆盖回款计划信息
            BeanUtils.copyProperties(contractCollectPaymentPlanPO, contractCollectPaymentPlanAndBaseVO);

            //处理当前回款计划的已收总额
            String paymentPlanCode = contractCollectPaymentPlanAndBaseVO.getPaymentPlanCode();
            if (StringUtils.isBlank(paymentPlanCode)) {
                contractCollectPaymentPlanAndBaseVO.setPaymentSum(new BigDecimal("0"));
            } else {

                //处理回款计划里面的回款总额，如果不存在设置为0
                BigDecimal paymentCountSum = contractCollectPaymentPlanAndBaseVO.getPaymentCount();
                if (Objects.isNull(paymentCountSum)) {
                    paymentCountSum = BigDecimal.ZERO;
                }

                //就一个已收总额
                BigDecimal sumBigDecimal = BigDecimal.ZERO;
                PaymentReceiptOrderPlanQueryDTO paymentReceiptOrderPlanQueryDTO = new PaymentReceiptOrderPlanQueryDTO();
                paymentReceiptOrderPlanQueryDTO.setPaymentPlanCode(paymentPlanCode);
                List<PaymentReceiptOrderPlanVO> paymentReceiptOrderPlanVOS = paymentReceiptOrderPlanService.queryList(paymentReceiptOrderPlanQueryDTO);
                if (CollUtil.isNotEmpty(paymentReceiptOrderPlanVOS)) {
                    //计算回款单里的回款计划的钱信息
                    for (PaymentReceiptOrderPlanVO paymentReceiptOrderPlanVO : paymentReceiptOrderPlanVOS) {
                        //根据此关系查询初具体的回款计划的详情信息
                        BigDecimal paymentCount = paymentReceiptOrderPlanVO.getPaymentCount();
                        if (Objects.nonNull(paymentCount)) {
                            //求和
                            sumBigDecimal = sumBigDecimal.add(paymentCount);
                        }
                    }
                }
                //设置金额   已收金额
                contractCollectPaymentPlanAndBaseVO.setPaymentSum(sumBigDecimal);
                //设置未收金额
                contractCollectPaymentPlanAndBaseVO.setCurrentPaymentCount(paymentCountSum.subtract(sumBigDecimal));
            }
        }

        return contractCollectPaymentPlanAndBaseVO;
    }


    /**
     * @Description: 分页查询，也会查询关联合同的基本信息
     * @Param: [queryContractCollectPaymentPlanToPageDTO]
     * @return: com.swxa.prp.util.TableResultUtil
     * @Author: lwei
     * @Date: 2025/7/25
     */
    @Override
    public TableResultUtil queryToPage(ContractCollectPaymentPlanQueryTOPageDTO queryContractCollectPaymentPlanToPageDTO) {


        //追加数据授权条件
        List<String> bussIds = dataAuthService.getBussIds(CrmBussTypeEnum.contractPaymentPlan.getCode());



        log.info("ContractCollectPaymentPlanServiceImpl.queryContractPaymentConditionToPage {}", queryContractCollectPaymentPlanToPageDTO);
        // 分页
        Page<Object> page = PageHelper.startPage(queryContractCollectPaymentPlanToPageDTO.getPageNum(), queryContractCollectPaymentPlanToPageDTO.getPageSize());
        // todo -查询条件待定，拼接查询条件
        QueryWrapper<ContractCollectPaymentPlanPO> queryWrapper = Wrappers.query();
        if (StringUtils.isNotEmpty(queryContractCollectPaymentPlanToPageDTO.getStageName())) {
            queryWrapper.lambda().eq(ContractCollectPaymentPlanPO::getStageName, queryContractCollectPaymentPlanToPageDTO.getStageName());
        }
        if (StringUtils.isNotEmpty(queryContractCollectPaymentPlanToPageDTO.getStageNameLike())) {
            queryWrapper.lambda().like(ContractCollectPaymentPlanPO::getStageName, queryContractCollectPaymentPlanToPageDTO.getStageNameLike());
        }
        if (StringUtils.isNotEmpty(queryContractCollectPaymentPlanToPageDTO.getContractId())) {
            queryWrapper.lambda().eq(ContractCollectPaymentPlanPO::getContractId, queryContractCollectPaymentPlanToPageDTO.getContractId());
        }

        if (StringUtils.isNotEmpty(queryContractCollectPaymentPlanToPageDTO.getPaymentPlanCode())) {
            queryWrapper.lambda().eq(ContractCollectPaymentPlanPO::getPaymentPlanCode, queryContractCollectPaymentPlanToPageDTO.getPaymentPlanCode());
        }
        if (StringUtils.isNotEmpty(queryContractCollectPaymentPlanToPageDTO.getPaymentPlanCodeLike())) {
            queryWrapper.lambda().like(ContractCollectPaymentPlanPO::getPaymentPlanCode, queryContractCollectPaymentPlanToPageDTO.getPaymentPlanCodeLike());
        }


        //设置  可访问的业务ids  contractOrderQueryListDTO
        if (CollUtil.isNotEmpty(bussIds)) {
            queryWrapper.lambda().in(ContractCollectPaymentPlanPO::getId, bussIds);
        }


        // 按更新时间倒序查询
        queryWrapper.lambda().orderByDesc(ContractCollectPaymentPlanPO::getCreateTime);
        List<ContractCollectPaymentPlanPO> contractPaymentConditionIPage = contractCollectPaymentPlanMapper.selectList(queryWrapper);

        List<ContractCollectPaymentPlanAndBaseVO> result = new ArrayList<>();
        for (ContractCollectPaymentPlanPO contractCollectPaymentPlan : contractPaymentConditionIPage) {
            ContractCollectPaymentPlanAndBaseVO contractCollectPaymentPlanAndBaseVO = new ContractCollectPaymentPlanAndBaseVO();
            // 根据contractid查询基本信息
            ContractOrderQueryDTO contractOrderQueryDTO = new ContractOrderQueryDTO();
            contractOrderQueryDTO.setId(contractCollectPaymentPlan.getContractId());
            ContractOrderAllLinkInfoVO contractOrderAllLinkInfoVO = contractOrderService.queryById(contractOrderQueryDTO);
            ContractOrderVO contractOrderPO = contractOrderAllLinkInfoVO.getBaseData();

            if (Objects.nonNull(contractOrderPO)) {
                //先拷贝合同信息，避免合同表中重复属性将vo属性修改
                BeanUtils.copyProperties(contractOrderPO, contractCollectPaymentPlanAndBaseVO);
                //清空合同里面的最新回款日期
                contractCollectPaymentPlanAndBaseVO.setLastPaymentDate("");
            }

            //覆盖回款计划信息
            BeanUtils.copyProperties(contractCollectPaymentPlan, contractCollectPaymentPlanAndBaseVO);

            //处理当前回款计划的已收总额
            String paymentPlanCode = contractCollectPaymentPlanAndBaseVO.getPaymentPlanCode();
            if (StringUtils.isBlank(paymentPlanCode)) {
                contractCollectPaymentPlanAndBaseVO.setPaymentSum(new BigDecimal("0"));
            } else {

                //处理回款计划里面的回款总额，如果不存在设置为0
                BigDecimal paymentCountSum = contractCollectPaymentPlanAndBaseVO.getPaymentCount();
                if (Objects.isNull(paymentCountSum)) {
                    paymentCountSum = BigDecimal.ZERO;
                }

                //就一个已收总额
                BigDecimal sumBigDecimal = BigDecimal.ZERO;
                PaymentReceiptOrderPlanQueryDTO paymentReceiptOrderPlanQueryDTO = new PaymentReceiptOrderPlanQueryDTO();
                paymentReceiptOrderPlanQueryDTO.setPaymentPlanCode(paymentPlanCode);
                List<PaymentReceiptOrderPlanVO> paymentReceiptOrderPlanVOS = paymentReceiptOrderPlanService.queryList(paymentReceiptOrderPlanQueryDTO);
                if (CollUtil.isNotEmpty(paymentReceiptOrderPlanVOS)) {
                    //计算回款单里的回款计划的钱信息
                    for (PaymentReceiptOrderPlanVO paymentReceiptOrderPlanVO : paymentReceiptOrderPlanVOS) {
                        //根据此关系查询初具体的回款计划的详情信息
                        BigDecimal paymentCount = paymentReceiptOrderPlanVO.getPaymentCount();
                        if (Objects.nonNull(paymentCount)) {
                            //求和
                            sumBigDecimal = sumBigDecimal.add(paymentCount);
                        }
                    }
                }
                //设置金额   已收金额
                contractCollectPaymentPlanAndBaseVO.setPaymentSum(sumBigDecimal);
                //设置未收金额
                contractCollectPaymentPlanAndBaseVO.setCurrentPaymentCount(paymentCountSum.subtract(sumBigDecimal));
            }

            result.add(contractCollectPaymentPlanAndBaseVO);
        }

        PageInfo<ContractCollectPaymentPlanAndBaseVO> pageInfo = new PageInfo<>(result);
        pageInfo.setTotal(page.getTotal());

        return TableResultUtil.buildTableResult(pageInfo);
    }

    /**
     * @Description: 根据合同id，删除关联的付款计划数据
     * @Param: [contractId]
     * @return: void
     * @Author: lwei
     * @Date: 2025/7/25
     */
    @Override
    public void deleteByContractId(String contractId) {
        if (StringUtils.isBlank(contractId)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_ID_NULL);
        }
        //查询该合同的回款计划
        ContractCollectPaymentPlanQueryListDTO contractCollectPaymentPlanQueryListDTO = new ContractCollectPaymentPlanQueryListDTO();
        contractCollectPaymentPlanQueryListDTO.setContractId(contractId);
        List<ContractCollectPaymentPlanVO> contractCollectPaymentPlanVOS = queryList(contractCollectPaymentPlanQueryListDTO);
        if (CollUtil.isNotEmpty(contractCollectPaymentPlanVOS)) {
            //删除合同的回款计划
            for (ContractCollectPaymentPlanVO contractCollectPaymentPlanVO : contractCollectPaymentPlanVOS) {
                ContractCollectPaymentPlanDeleteDTO deleteCollectPaymentPlan = new ContractCollectPaymentPlanDeleteDTO();
                deleteCollectPaymentPlan.setId(contractCollectPaymentPlanVO.getId());
                //删除
                deleteById(deleteCollectPaymentPlan);
            }
        }
    }

    /**
     * @Description: 列表查询，根据合同id
     * @Param: [contractCollectPaymentPlanQueryListDTO]
     * @return: java.util.List<com.swxa.prp.business.contract.vo.ContractCollectPaymentPlanVO>
     * @Author: lwei
     * @Date: 2025/7/25
     */
    @Override
    public List<ContractCollectPaymentPlanVO> queryList(ContractCollectPaymentPlanQueryListDTO contractCollectPaymentPlanQueryListDTO) {

        if (contractCollectPaymentPlanQueryListDTO.getIsAuth()) {
            //追加数据授权条件
            List<String> bussIds = dataAuthService.getBussIds(CrmBussTypeEnum.contractPaymentPlan.getCode());
            //设置  可访问的业务ids  contractOrderQueryListDTO
            if (CollUtil.isNotEmpty(bussIds)) {
                contractCollectPaymentPlanQueryListDTO.setBussIds(bussIds);
            }
        }


        List<ContractCollectPaymentPlanPO> contractCollectPaymentPlanPOS = contractCollectPaymentPlanMapper.queryList(contractCollectPaymentPlanQueryListDTO);
        if (CollUtil.isNotEmpty(contractCollectPaymentPlanPOS)) {
            List<ContractCollectPaymentPlanVO> results = new ArrayList<>();
            for (ContractCollectPaymentPlanPO contractCollectPaymentPlanPO : contractCollectPaymentPlanPOS) {
                ContractCollectPaymentPlanVO contractCollectPaymentPlanVO = new ContractCollectPaymentPlanVO();
                BeanUtils.copyProperties(contractCollectPaymentPlanPO, contractCollectPaymentPlanVO);
                results.add(contractCollectPaymentPlanVO);
            }
            return results;
        }
        return new ArrayList<>();
    }

    /**
     * @Description: 更新回款计划信息
     * @Param: [updatePaymentPlanInfoDTO]
     * @return: void
     * @Author: lwei
     * @Date: 2025/8/21
     */

    @Override
    public void updateContracePaymentPlanInfo(ContractOrderUpdatePaymentPlanInfoDTO updatePaymentPlanInfoDTO) {


        //1.校验参数不为空
        SwValidatorUtil.checkObjectParam(updatePaymentPlanInfoDTO);

        //2.更新回款计划  最新回款日期
        String planCode = updatePaymentPlanInfoDTO.getPaymentPlanCode();
        if (StringUtils.isBlank(planCode)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_RECEIPT_PAYMENT_PLAN_DATE_ERROR);
        }

        BigDecimal payment = updatePaymentPlanInfoDTO.getPaymentSum();
        if (Objects.isNull(payment)) {
            //要设置的值为空，不操作
            return;
        }

        ContractCollectPaymentPlanQueryListDTO contractCollectPaymentPlanQueryListDTO = new ContractCollectPaymentPlanQueryListDTO();
        contractCollectPaymentPlanQueryListDTO.setPaymentPlanCode(updatePaymentPlanInfoDTO.getPaymentPlanCode());
        List<ContractCollectPaymentPlanPO> paymentPlanList = contractCollectPaymentPlanMapper.queryList(contractCollectPaymentPlanQueryListDTO);
        if (CollUtil.isEmpty(paymentPlanList)) {
            // 没有找到对应的回款计划记录
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_BUSS_PAYMENT_PLAN_NOT_FOUND);
        }

        ContractCollectPaymentPlanPO contractCollectPaymentPlanPO = paymentPlanList.get(0);


        contractCollectPaymentPlanPO.setLastPaymentDate(updatePaymentPlanInfoDTO.getLastPaymentDate());
        contractCollectPaymentPlanPO.setUpdateTime(new Date());
        contractCollectPaymentPlanMapper.updateByPrimaryKeySelective(contractCollectPaymentPlanPO);


        //3.更新合同的最新回款日期
        ContractOrderPO contractOrderPO = new ContractOrderPO();
        contractOrderPO.setId(contractCollectPaymentPlanPO.getContractId());
        //更新回款时间
        contractOrderPO.setLastPaymentDate(updatePaymentPlanInfoDTO.getLastPaymentDate());
        contractOrderPO.setUpdateTime(new Date());
        //更新值
        contractOrderMapper.updateByPrimaryKeySelective(contractOrderPO);


        //4. 更新回款单 汇款计划编码  钱的关系表,相同的合同，相同的回款计划编码，相同时间的钱直接覆盖，否则新增
        //这个地方得有个调整  就是回款单里面的回款计划一定是只有一个的，如果这次换了回款计划  那么要把原来的回款计划删掉

        PaymentReceiptOrderPlanQueryDTO queryDTO = new PaymentReceiptOrderPlanQueryDTO();
        queryDTO.setReceiptNo(updatePaymentPlanInfoDTO.getReceiptNo());
        queryDTO.setSourceOrderNo(updatePaymentPlanInfoDTO.getSourceOrderNo());
        queryDTO.setSourceOrderType(updatePaymentPlanInfoDTO.getSourceOrderType());
        queryDTO.setPaymentPlanCode(updatePaymentPlanInfoDTO.getPaymentPlanCode());
        //queryDTO.setPaymentCount(updatePaymentPlanInfoDTO.getPaymentSum());
        //queryDTO.setLastPaymentDate(updatePaymentPlanInfoDTO.getLastPaymentDate());
        List<PaymentReceiptOrderPlanVO> paymentReceiptOrderPlanVOS = paymentReceiptOrderPlanService.queryList(queryDTO);
        if (CollUtil.isNotEmpty(paymentReceiptOrderPlanVOS)) {//不为空的时候就去更新价格
            PaymentReceiptOrderPlanVO paymentReceiptOrderPlanVO = paymentReceiptOrderPlanVOS.get(0);
            PaymentReceiptOrderPlanUpdateDTO updateDTO = new PaymentReceiptOrderPlanUpdateDTO();
            updateDTO.setId(paymentReceiptOrderPlanVO.getId());
            updateDTO.setPaymentCount(updatePaymentPlanInfoDTO.getPaymentSum());
            paymentReceiptOrderPlanService.updateByID(updateDTO);
        } else {//为空说明不存在直接添加
            PaymentReceiptOrderPlanInsertDTO insertDTO = new PaymentReceiptOrderPlanInsertDTO();
            insertDTO.setPaymentPlanCode(updatePaymentPlanInfoDTO.getPaymentPlanCode());
            insertDTO.setLastPaymentDate(updatePaymentPlanInfoDTO.getLastPaymentDate());
            insertDTO.setPaymentCount(updatePaymentPlanInfoDTO.getPaymentSum());
            insertDTO.setReceiptNo(updatePaymentPlanInfoDTO.getReceiptNo());
            insertDTO.setSourceOrderType(updatePaymentPlanInfoDTO.getSourceOrderType());
            insertDTO.setSourceOrderNo(updatePaymentPlanInfoDTO.getSourceOrderNo());
            paymentReceiptOrderPlanService.insertData(insertDTO);
        }
    }

    /**
     * @Description: 根据负责人id列表查询对应的记录id列表
     * @Param: [saleUserId]
     * @return: java.util.List<java.lang.String>
     * @Author: lwei
     * @Date: 2025/9/18
     */
    @Override
    public List<String> findBySaleUserIds(List<String> saleUserId) {

        if (CollUtil.isNotEmpty(saleUserId)) {
            /**
             * @Author: lwei
             * @Date: 2025/9/18
             * 注意：此次处理跟其他模块不一样，因为单独的回款计划表中没有销售负责人id信息，处理逻辑不能直接查询
             * 先根据销售负责人id，在合同订单列表中查询到合同订单id列表
             * 再根据合同订单id列表，在回款计划表中查找对应的回款计划id列表
             */
            List<String> contractIds = contractOrderMapper.selectListBySaleUserId(saleUserId);
            if (CollUtil.isNotEmpty(contractIds)) {
                List<String> ids = contractCollectPaymentPlanMapper.selectListByContractId(contractIds);
                if (CollUtil.isNotEmpty(ids)) {
                    return ids;
                }
            }

        }
        return null;
    }

    /**
     * @Description: 根据创建者id列表，查询对应的数据id列表
     * @Param: [createId]
     * @return: java.util.List<java.lang.String>
     * @Author: lwei
     * @Date: 2025/9/18
     */
    @Override
    public List<String> findByCreateIds(List<String> createId) {
        if (CollUtil.isNotEmpty(createId)) {
            List<String> ids = contractCollectPaymentPlanMapper.selectListByCreateId(createId);
            if (CollUtil.isNotEmpty(ids)) {
                return ids;
            }
        }
        return null;
    }

    /**
     * @Description: 根据id，更新审批状态
     * @Param: [processStatusDTO]
     * @return: void
     * @Author: lwei
     * @Date: 2025/9/19
     */
    @Override
    public void updateProcessStatusById(BussProcessStatusDTO processStatusDTO) {
        String id = processStatusDTO.getId();
        if (StringUtils.isNotBlank(id)) {
            ContractCollectPaymentPlanPO bussContactInfo = new ContractCollectPaymentPlanPO();
            bussContactInfo.setId(id);
            bussContactInfo.setBussProcessStatus(processStatusDTO.getBussProcessStatus());

            contractCollectPaymentPlanMapper.updateByPrimaryKeySelective(bussContactInfo);
        }
    }
}
