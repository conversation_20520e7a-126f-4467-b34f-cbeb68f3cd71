package com.swxa.prp.business.contract.model;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ContractProductDataPO {

    /**
     *
     */
    private String id;

    /**
     * 合同id字符串
     */
    private String contractId;

    /**
     * U9编码
     */
    private String unineCode;

    private String productCode;

    /**
     * 合同产品名称
     */
    private String contractProductName;

    /**
     * 合同规格型号
     */
    private String contractProductType;

    /**
     * 数量
     */
    private Integer productNum;

    /**
     * 单位
     */
    private String productUnit;

    /**
     * 税
     */
    private Double productTax;

    /**
     * 应付金额
     */
    private BigDecimal paymentCount;

    /**
     * 含税单价
     */
    private BigDecimal unitTaxPrice;

    /**
     * 不含税单价
     */
    private BigDecimal noUnitTaxPrice;

    /**
     * 价税合计
     */
    private BigDecimal totalAmountPrice;

    /**
     * 不含税金额
     */
    private BigDecimal noTotalAmountPrice;

    /**
     * 税额
     */
    private BigDecimal taxPrice;

    /**
     * 软件是否可退税
     */
    private String softwareTaxRefund;

    //生产批号
    private  String  productionBatchNumber;
    //序列号
    private  String  productionSerialNumber;

    /**
     * 合同已发货数量
     */
    private Integer deliverNum;

    /**
     * 合同未发货数量
     */
    private Integer noDeliverNum;

    /**
     * 实际供货数量
     */
    private Integer supplyNum;

    /**
     * 业务难度系数
     */
    private Double bussComplexIndex;

    /**
     * 折算后合同金额
     */
    private BigDecimal convertContractAmount;

    /**
     * 创建者
     */
    private String createId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     *备注
     */
    private String remark;


}