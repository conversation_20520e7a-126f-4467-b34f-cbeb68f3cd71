package com.swxa.prp.business.contract.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.ISelect;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.swxa.prp.business.act.conditionFields.dto.BussProcessStatusDTO;
import com.swxa.prp.business.attach.dto.AttachmentIdRelationInsertDTO;
import com.swxa.prp.business.attach.dto.AttachmentIdRelationQueryListDTO;
import com.swxa.prp.business.attach.model.Attachment;
import com.swxa.prp.business.attach.service.AttachmentIdRelationService;
import com.swxa.prp.business.attach.service.AttachmentService;
import com.swxa.prp.business.attach.vo.AttachmentRelationServiceVO;
import com.swxa.prp.business.contract.dao.ContractOrderMapper;
import com.swxa.prp.business.contract.dao.InternalcontractPOMapper;
import com.swxa.prp.business.contract.dto.*;
import com.swxa.prp.business.contract.model.BussInternalContractProductPO;
import com.swxa.prp.business.contract.model.ContractOrderPO;
import com.swxa.prp.business.contract.model.InternalcontractPO;
import com.swxa.prp.business.contract.service.BussInteralContractProductService;
import com.swxa.prp.business.contract.service.InternalContractService;
import com.swxa.prp.business.contract.vo.*;
import com.swxa.prp.business.dataauth._enum.CrmBussTypeEnum;
import com.swxa.prp.business.dataauth.dataauthrules.service.DataAuthService;
import com.swxa.prp.business.supplymanage.dto.ProductSerialNumBatchDTO;
import com.swxa.prp.business.usermanagement.service.UserManagementService;
import com.swxa.prp.constant.SwErrorCodeConstant;
import com.swxa.prp.exception.SwPrpException;
import com.swxa.prp.model.SysUserVO;
import com.swxa.prp.model.dto.UserQueryDTO;
import com.swxa.prp.util.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;

@Service
public class InternalContractServicelmpl implements InternalContractService {

    @Autowired
    private InternalcontractPOMapper internalcontractPOMapper;

    @Autowired
    private AttachmentIdRelationService attachmentIdRelationService;

    @Autowired
    private AttachmentService attachmentService;

    @Lazy
    @Autowired
    private DataAuthService dataAuthService;

    @Autowired
    private BussInteralContractProductService bussInteralContractProductService;

    @Autowired
    private ContractOrderMapper contractOrderMapper;

    @Autowired
    private UserManagementService userManagementService = null;

    private static final Logger log = LoggerFactory.getLogger("dayLogger");


    /**
     * 前端删除
     *
     * @param
     */

    @Override
    @Transactional
    public void deleteInternalContract(InternalContractDeleteFrontDTO internalContractDeleteFrontDTO) {

        String deleteId = internalContractDeleteFrontDTO.getId();
        if (StringUtils.isBlank(deleteId)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_ID_NULL);
        }

        //基本信息
        internalcontractPOMapper.deleteByPrimaryKey(deleteId);

        //产品管理
        bussInteralContractProductService.deleteByContractId(deleteId);

        //附件
        attachmentIdRelationService.deleteByAttachBussId(deleteId);

    }


    /**
     * 增加一条人员记录
     *
     * @param
     */
    @Transactional
    @Override
    public InternalcontractPO insert(InternalContractnInsertDTO internalContractnInsertDTO) {

        log.info("打印log样例，{}");

        InternalcontractPO internalcontractPO = new InternalcontractPO();

        //设置信息
        BeanUtils.copyProperties(internalContractnInsertDTO, internalcontractPO);
        internalcontractPO.setId(MyUtil.getRandomID());

        if (StringUtils.isNotBlank(internalcontractPO.getContractNum())) {
            String contractNum = internalcontractPO.getContractNum();

            ContractOrderPO contractOrderPO = contractOrderMapper.selectByPrimaryKey(contractNum);
            if (Objects.nonNull(contractOrderPO)) {
                //从合同订单中获取客户信息
                internalcontractPO.setCustomerCode(contractOrderPO.getCustomerCode());
                internalcontractPO.setCustomerId(contractOrderPO.getCustomerId());
                internalcontractPO.setCustomerName(contractOrderPO.getCustomerName());
            }
        }

        internalcontractPO.setCreateId(UserUtil.getLoginUserId());
        internalcontractPO.setCreateTime(new Date());
        internalcontractPO.setUpdateTime(new Date());

        //添加
        internalcontractPOMapper.insertSelective(internalcontractPO);
        return internalcontractPO;

    }


    /**
     * 删除指定id的数据记录
     * lidi
     *
     * @param
     */

    @Override
    @Transactional
    public void deleteById(InternalContractDeleteDTO internalContractDeleteDTO) {


        //获取内部合同id
        String id = internalContractDeleteDTO.getId();
        if (Objects.isNull(id)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_ID_NULL);
        }

        //删除关联产品信息
        bussInteralContractProductService.deleteByContractId(id);

        //删除
        internalcontractPOMapper.deleteByPrimaryKey(id);

    }


    /**
     * 根据id更新人员信息
     * lidi
     *
     * @param
     */
    @Override
    @Transactional
    public void updateById(InternalContractUpdateDTO internalContractUpdateDTO) {

        String id = internalContractUpdateDTO.getId();
        if (Objects.isNull(id)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_ID_NULL);
        }
        InternalcontractPO internalcontractPO = internalcontractPOMapper.selectByPrimaryKey(id);
        if (Objects.isNull(internalcontractPO)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_BUSS_INTERNAL_PRODUCT_NOT_FOUND);
        }

        if (StringUtils.isNotBlank(internalContractUpdateDTO.getContractNum()) &&
                !internalContractUpdateDTO.getContractNum().equals(internalcontractPO.getContractNum())) {
            // 合同订单号改变了，客户信息也要变
            String contractNum = internalcontractPO.getContractNum();

            ContractOrderPO contractOrderPO = contractOrderMapper.selectByPrimaryKey(contractNum);
            if (Objects.nonNull(contractOrderPO)) {
                //从合同订单中获取客户信息
                internalcontractPO.setCustomerCode(contractOrderPO.getCustomerCode());
                internalcontractPO.setCustomerId(contractOrderPO.getCustomerId());
                internalcontractPO.setCustomerName(contractOrderPO.getCustomerName());
            }
        }

        //赋值
        BeanUtils.copyProperties(internalContractUpdateDTO, internalcontractPO);
        internalcontractPO.setId(id);
        internalcontractPO.setUpdateTime(new Date());

        internalcontractPOMapper.updateByPrimaryKeySelective(internalcontractPO);
    }


    /**
     * 查询列表
     * lidi
     *
     * @param
     * @return
     */

    @Override
    public List<InternalContractServiceVO> queryList(InternalContractQueryListDTO internalContractQueryListDTO) {


        //列表查询
        if (internalContractQueryListDTO.getIsAuth()) {
            //追加数据授权条件
            List<String> bussIds = dataAuthService.getBussIds(CrmBussTypeEnum.contractInternal.getCode());
            //设置  可访问的业务ids  contractOrderQueryListDTO
            if (CollUtil.isNotEmpty(bussIds)) {
                internalContractQueryListDTO.setBussIds(bussIds);
            }
        }


        //列表查询
        List<InternalcontractPO> internalcontractPOS = internalcontractPOMapper.selectlist(internalContractQueryListDTO);

        List<InternalContractServiceVO> resListVOS = new ArrayList<>();

        //不为空处理返回结果
        if (CollUtil.isNotEmpty(internalcontractPOS)) {
            for (InternalcontractPO dict : internalcontractPOS) {
                InternalContractServiceVO internalContractServiceVO = new InternalContractServiceVO();
                BeanUtils.copyProperties(dict, internalContractServiceVO);
                resListVOS.add(internalContractServiceVO);
            }
        }


        return resListVOS;
    }

    /**
     * 在service层实现分页查询 contractParty
     * lidi
     *
     * @param
     * @return
     */
    @Override
    public TableResultUtil queryToPage(InternalContractQueryPageDTO internalContractQueryPageDTO) {


        //追加数据授权条件
        List<String> bussIds = dataAuthService.getBussIds(CrmBussTypeEnum.contractInternal.getCode());


        //分页查询
        PageInfo<InternalContractServiceVO> pageInfo = PageHelper.startPage(internalContractQueryPageDTO.getPageNum(), internalContractQueryPageDTO.getPageSize())
                .doSelectPageInfo(new ISelect() {
                    @Override
                    public void doSelect() {
                        InternalContractQueryListDTO internalContractQueryListDTO = new InternalContractQueryListDTO();
                        internalContractQueryListDTO.setContractNum(internalContractQueryPageDTO.getContractNum());

                        if (CollUtil.isNotEmpty(internalContractQueryPageDTO.getAdvanceQueryGroups())) {
                            String sql = SqlToolsUtil.constructFields(internalContractQueryPageDTO.getAdvanceQueryGroups());
                            if (StringUtils.isNotBlank(sql)) {
                                log.info("sqlWhere = {}", sql);
                                internalContractQueryListDTO.setAdvanceQuerySql(sql);
                            }
                        }


                        //设置  可访问的业务ids  contractOrderQueryListDTO
                        if (CollUtil.isNotEmpty(bussIds)) {
                            internalContractQueryListDTO.setBussIds(bussIds);
                            internalContractQueryListDTO.setIsAuth(false);
                        }

                        queryList(internalContractQueryListDTO);
                    }
                });

        return TableResultUtil.buildTableResult(pageInfo);
    }


    @Transactional
    @Override
    public void addInternalContractn(InternalContractnAddFrontDTO internalContractnAddFrontDTO) {

        //1.基本信
        InternalContractnInsertDTO baseData = internalContractnAddFrontDTO.getBaseData();
        if (Objects.isNull(baseData)) {
            //内部合同基本信息不能为空
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_BUSS_INTERNAL_CONTRACT_DATA_NULL);
        }
        //判断内部合同编码是否重复
        String contractNum = baseData.getContractNum();
        if (StringUtils.isBlank(contractNum)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_BUSS_INTERNAL_CONTRACT_NUM_NULL);
        }
        InternalContractQueryListDTO internalContractQueryListDTO = new InternalContractQueryListDTO();
        internalContractQueryListDTO.setContractNum(contractNum);
        List<InternalContractServiceVO> internalContractServiceVOS = queryList(internalContractQueryListDTO);
        if (CollUtil.isNotEmpty(internalContractServiceVOS)) {
            //内部合同编码已经存在
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_BUSS_INTERNAL_CONTRACT_NUM_EXISTS_ERROR);
        }

        //添加
        InternalcontractPO internalcontractPO = insert(baseData);
        if (Objects.isNull(internalcontractPO)) {
            //添加合同失败
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_BUSS_INTERNAL_CONTRACT_ADD_ERROR);
        }

        //获取内部合同的id
        String contractId = internalcontractPO.getId();

        //2.处理附件关系
        List<String> attachIds = internalContractnAddFrontDTO.getAttachIds();
        if (CollUtil.isNotEmpty(attachIds)) {
            for (String attId : attachIds) {
                AttachmentIdRelationInsertDTO attachmentIdRelationInsertDTO = new AttachmentIdRelationInsertDTO();
                attachmentIdRelationInsertDTO.setCurrBussId(contractId);
                attachmentIdRelationInsertDTO.setAttachBussId(attId);
                attachmentIdRelationService.insert(attachmentIdRelationInsertDTO);
            }
        }

        //3.处理合同订单产品信息
        List<AddBussInternalContractProductDTO> productDatas = internalContractnAddFrontDTO.getProductDatas();
        if (CollUtil.isEmpty(productDatas)) {
            //合同产品信息不存在
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_BUSS_INTERNAL_CONTRACT_PRODUCT_DATA_NULL);
        }

        for (AddBussInternalContractProductDTO contractProductDataAddDTO : productDatas) {
            contractProductDataAddDTO.setInternalContractId(contractId);
            bussInteralContractProductService.add(contractProductDataAddDTO);
        }


    }

    @Transactional
    @Override
    public void updateInternalContractn(InternalContractnUpdateFrontDTO internalContractnUpdateFrontDTO) {
        //1.基本信
        InternalContractUpdateDTO baseData = internalContractnUpdateFrontDTO.getBaseData();
        if (Objects.isNull(baseData)) {
            //内部合同基本信息不能为空
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_BUSS_INTERNAL_CONTRACT_DATA_NULL);
        }

        //判断内部合同编码是否重复
        String contractNum = baseData.getContractNum();
        if (StringUtils.isBlank(contractNum)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_BUSS_INTERNAL_CONTRACT_NUM_NULL);
        }
        InternalContractQueryListDTO internalContractQueryListDTO = new InternalContractQueryListDTO();
        internalContractQueryListDTO.setContractNum(contractNum);
        List<InternalContractServiceVO> internalContractServiceVOS = queryList(internalContractQueryListDTO);
        if (CollUtil.isNotEmpty(internalContractServiceVOS)) {

            if ((internalContractServiceVOS.size() > 1)) {
                //合同编码已经存在
                throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_BUSS_INTERNAL_CONTRACT_NUM_EXISTS_ERROR);
            } else {//判断是不是自己,非自己抛出异常
                InternalContractServiceVO internalContractServiceVO = internalContractServiceVOS.get(0);
                if (!baseData.getId().equals(internalContractServiceVO.getId())) {
                    throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_BUSS_INTERNAL_CONTRACT_NUM_EXISTS_ERROR);
                }
            }
        }

        //更新
        updateById(baseData);


        //获取内部合同的id
        String contractId = baseData.getId();

        //2.处理附件关系
        List<String> attachIds = internalContractnUpdateFrontDTO.getAttachIds();
        if (CollUtil.isNotEmpty(attachIds)) {

            //查询当前合同对应的附件关系
            AttachmentIdRelationQueryListDTO attachmentIdRelationQueryListDTO = new AttachmentIdRelationQueryListDTO();
            attachmentIdRelationQueryListDTO.setCurrBussId(contractId);
            List<AttachmentRelationServiceVO> attachmentRelationServiceVOS = attachmentIdRelationService.queryList(attachmentIdRelationQueryListDTO);
            if (CollUtil.isEmpty(attachmentRelationServiceVOS)) {
                //新附件直接添加
                for (String attId : attachIds) {
                    AttachmentIdRelationInsertDTO attachmentIdRelationInsertDTO = new AttachmentIdRelationInsertDTO();
                    attachmentIdRelationInsertDTO.setCurrBussId(contractId);
                    attachmentIdRelationInsertDTO.setAttachBussId(attId);
                    attachmentIdRelationService.insert(attachmentIdRelationInsertDTO);
                }
            } else {
                //暂存已经存在的附件ID
                List<String> existsAttIds = new ArrayList<>();
                for (AttachmentRelationServiceVO attachmentRelationServiceVO : attachmentRelationServiceVOS) {
                    String attachBussId = attachmentRelationServiceVO.getAttachBussId();
                    //不为空
                    if (attachIds.contains(attachBussId)) {
                        existsAttIds.add(attachBussId);
                    } else {
                        //说明附件被删除了，需要删除附件
                        attachmentIdRelationService.deleteByAttachBussId(attachBussId);
                    }
                }


                //遍历编辑时提交的附件ID，不存在的就执行添加操作
                for (String aid : attachIds) {
                    //不存在新增
                    if (!existsAttIds.contains(aid)) {
                        AttachmentIdRelationInsertDTO attachmentIdRelationInsertDTO = new AttachmentIdRelationInsertDTO();
                        attachmentIdRelationInsertDTO.setCurrBussId(contractId);
                        attachmentIdRelationInsertDTO.setAttachBussId(aid);
                        attachmentIdRelationService.insert(attachmentIdRelationInsertDTO);
                    }
                }
            }

        } else {
            attachmentIdRelationService.deleteByBussId(contractId);
        }

        //3.处理合同订单产品信息
        //删除产品信息
        bussInteralContractProductService.deleteByContractId(contractId);

        //新增新的产品信息
        List<AddBussInternalContractProductDTO> productDatas = internalContractnUpdateFrontDTO.getProductDatas();
        if (CollUtil.isEmpty(productDatas)) {
            //合同产品信息不存在
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_BUSS_INTERNAL_CONTRACT_PRODUCT_DATA_NULL);
        }

        for (AddBussInternalContractProductDTO contractProductDataAddDTO : productDatas) {
            contractProductDataAddDTO.setInternalContractId(contractId);
            bussInteralContractProductService.add(contractProductDataAddDTO);
        }
    }

    @Override
    public InternalContractDataInfoVO queryById(InternalContractQueryIdDTO internalContractQueryIDDTO) {


        //定义返回结果
        InternalContractDataInfoVO result = new InternalContractDataInfoVO();

        //1.获取合同id
        String contractId = internalContractQueryIDDTO.getId();
        if (StringUtils.isBlank(contractId)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_ID_NULL);
        }

        //查询基本信息
        InternalcontractPO internalcontractPO = internalcontractPOMapper.selectByPrimaryKey(contractId);
        if (Objects.isNull(internalcontractPO)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_BUSS_INTERNAL_CONTRACT_NOT_EXISTS_ERROR);
        }

        InternalContractServiceVO baseData = new InternalContractServiceVO();
        BeanUtils.copyProperties(internalcontractPO, baseData);

        //赋值
        result.setBaseData(baseData);


        //2.查询附件
        //查询当前合同对应的附件关系
        AttachmentIdRelationQueryListDTO attachmentIdRelationQueryListDTO = new AttachmentIdRelationQueryListDTO();
        attachmentIdRelationQueryListDTO.setCurrBussId(contractId);
        List<AttachmentRelationServiceVO> attachmentRelationServiceVOS = attachmentIdRelationService.queryList(attachmentIdRelationQueryListDTO);
        if (CollUtil.isNotEmpty(attachmentRelationServiceVOS)) {
            //存储附件的id
            List<Attachment> attIds = new ArrayList<>();
            for (AttachmentRelationServiceVO attachmentRelationServiceVO : attachmentRelationServiceVOS) {
                String attachBussId = attachmentRelationServiceVO.getAttachBussId();
                if (StringUtils.isBlank(attachBussId)) {
                    continue;
                }
                Attachment attachment = attachmentService.selectById(Integer.parseInt(attachBussId));
                attIds.add(attachment);
            }
            //赋值
            result.setAttachIds(attIds);
        }


        //3.查询产品信息
        InternalContractProductQueryListDTO internalContractQueryListDTO = new InternalContractProductQueryListDTO();
        internalContractQueryListDTO.setContractId(contractId);
        List<BussInternalContractProductVO> bussInternalContractProductVOList = bussInteralContractProductService.queryList(internalContractQueryListDTO);
        if (CollUtil.isNotEmpty(bussInternalContractProductVOList)) {
            result.setProductDatas(bussInternalContractProductVOList);
        }


        return result;
    }

    /**
     * @Description: 根据id或编码查询详情
     * @Param: [internalContractQueryIDDTO]
     * @return: com.swxa.prp.business.contract.vo.InternalContractDataInfoVO
     * @Author: lwei
     * @Date: 2025/9/4
     */
    @Override
    public InternalContractDataInfoVO queryByIdCode(InternalContractQueryIdDTO internalContractQueryIDDTO) {
        //定义返回结果
        InternalContractDataInfoVO result = new InternalContractDataInfoVO();

        //1.获取合同id
        String contractId = internalContractQueryIDDTO.getId();
        String contractNum = internalContractQueryIDDTO.getContractNum();
        if (StringUtils.isBlank(contractId) && StringUtils.isBlank(contractNum)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_ID_NULL);
        }
        InternalContractQueryListDTO internalContractQueryList = new InternalContractQueryListDTO();
        BeanUtils.copyProperties(internalContractQueryIDDTO, internalContractQueryList);
        //列表查询
        List<InternalcontractPO> internalcontractPOS = internalcontractPOMapper.selectlist(internalContractQueryList);
        if (CollUtil.isEmpty(internalcontractPOS)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_BUSS_INTERNAL_CONTRACT_NOT_EXISTS_ERROR);
        }

        //查询基本信息
        InternalcontractPO internalcontractPO = internalcontractPOS.get(0);
        if (Objects.isNull(internalcontractPO)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_BUSS_INTERNAL_CONTRACT_NOT_EXISTS_ERROR);
        }

        InternalContractServiceVO baseData = new InternalContractServiceVO();
        BeanUtils.copyProperties(internalcontractPO, baseData);

        //赋值
        result.setBaseData(baseData);


        //2.查询附件
        //查询当前合同对应的附件关系
        AttachmentIdRelationQueryListDTO attachmentIdRelationQueryListDTO = new AttachmentIdRelationQueryListDTO();
        attachmentIdRelationQueryListDTO.setCurrBussId(internalcontractPO.getId());
        List<AttachmentRelationServiceVO> attachmentRelationServiceVOS = attachmentIdRelationService.queryList(attachmentIdRelationQueryListDTO);
        if (CollUtil.isNotEmpty(attachmentRelationServiceVOS)) {
            //存储附件的id
            List<Attachment> attIds = new ArrayList<>();
            for (AttachmentRelationServiceVO attachmentRelationServiceVO : attachmentRelationServiceVOS) {
                String attachBussId = attachmentRelationServiceVO.getAttachBussId();
                if (StringUtils.isBlank(attachBussId)) {
                    continue;
                }
                Attachment attachment = attachmentService.selectById(Integer.parseInt(attachBussId));
                attIds.add(attachment);
            }
            //赋值
            result.setAttachIds(attIds);
        }


        //3.查询产品信息
        InternalContractProductQueryListDTO internalContractQueryListDTO = new InternalContractProductQueryListDTO();
        internalContractQueryListDTO.setContractId(internalcontractPO.getId());
        List<BussInternalContractProductVO> bussInternalContractProductVOList = bussInteralContractProductService.queryList(internalContractQueryListDTO);
        if (CollUtil.isNotEmpty(bussInternalContractProductVOList)) {
            result.setProductDatas(bussInternalContractProductVOList);
        }

        return result;
    }


    @Override
    public InternalcontractPO selectInternalcontractById(InternalContractQueryIdDTO internalContractQueryIDDTO) {


        //1.获取合同id
        String contractId = internalContractQueryIDDTO.getId();
        if (StringUtils.isBlank(contractId)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_ID_NULL);
        }

        //查询基本信息
        InternalcontractPO internalcontractPO = internalcontractPOMapper.selectByPrimaryKey(contractId);
        if (Objects.isNull(internalcontractPO)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_BUSS_INTERNAL_CONTRACT_NOT_EXISTS_ERROR);
        }
        return internalcontractPO;
    }

    @Override
    public TableResultUtil queryBaseAndProdToPage(InternalContractQueryPageDTO internalContractQueryPageDTO) {


        //追加数据授权条件
        List<String> bussIds = dataAuthService.getBussIds(CrmBussTypeEnum.contractInternal.getCode());


        log.info("queryBaseAndProdToPage param={}", JSONObject.toJSONString(internalContractQueryPageDTO));
        // 分页
        Page<Object> page = PageHelper.startPage(internalContractQueryPageDTO.getPageNum(), internalContractQueryPageDTO.getPageSize());
        // todo -查询条件待定，拼接查询条件
        QueryWrapper<InternalcontractPO> queryWrapper = Wrappers.query();
        if (StringUtils.isNotEmpty(internalContractQueryPageDTO.getContractNum())) {
            queryWrapper.lambda().eq(InternalcontractPO::getContractNum, internalContractQueryPageDTO.getContractNum());
        }
        if (StringUtils.isNotEmpty(internalContractQueryPageDTO.getContractNumLike())) {
            queryWrapper.lambda().like(InternalcontractPO::getContractNum, internalContractQueryPageDTO.getContractNumLike());
        }

        if (CollUtil.isNotEmpty(internalContractQueryPageDTO.getAdvanceQueryGroups())) {
            String sql = SqlToolsUtil.constructFields(internalContractQueryPageDTO.getAdvanceQueryGroups());
            if (StringUtils.isNotBlank(sql)) {
                log.info("sqlWhere = {}", sql);
                queryWrapper.apply(sql);
            }
        }

        if (CollUtil.isNotEmpty(bussIds)) {
            queryWrapper.lambda().in(InternalcontractPO::getId, bussIds);
        }

        // 按更新时间倒序查询
        queryWrapper.lambda().orderByDesc(InternalcontractPO::getUpdateTime);

        List<InternalcontractPO> internalcontractPOList = internalcontractPOMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(internalcontractPOList)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_BUSS_INTERNAL_CONTRACT_NOT_EXISTS_ERROR);
        }

        List<InternalContractAndProdServiceVO> result = new ArrayList<>();

        for (InternalcontractPO internalcontract : internalcontractPOList) {
            InternalContractAndProdServiceVO resp = new InternalContractAndProdServiceVO();
            BeanUtils.copyProperties(internalcontract, resp);

            //还要查询产品信息
            //根据合同id查询关联产品信息
            log.info("查询关联产品信息");
            InternalContractProductQueryListDTO internalContractProductQueryListDTO = new InternalContractProductQueryListDTO();
            internalContractProductQueryListDTO.setContractId(internalcontract.getId());
            List<BussInternalContractProductVO> bussInternalContractProductVOList = bussInteralContractProductService.queryList(internalContractProductQueryListDTO);
            if (CollUtil.isNotEmpty(bussInternalContractProductVOList)) {
                resp.setProductDatas(bussInternalContractProductVOList);
            }

            result.add(resp);
        }

        PageInfo<InternalContractAndProdServiceVO> pageInfo = new PageInfo<>(result);
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        return TableResultUtil.buildTableResult(pageInfo);
    }


    /**
     * @Description: 根据负责人id列表查询对应的记录id列表
     * @Param: [saleUserId]
     * @return: java.util.List<java.lang.String>
     * @Author: lwei
     * @Date: 2025/9/18
     */
    @Override
    public List<String> findBySaleUserIds(List<String> saleUserId) {

        if (CollUtil.isNotEmpty(saleUserId)) {
            List<String> ids = internalcontractPOMapper.selectListBySaleUserId(saleUserId);
            if (CollUtil.isNotEmpty(ids)) {
                return ids;
            }
        }
        return null;
    }

    /**
     * @Description: 根据创建者id列表，查询对应的数据id列表
     * @Param: [createId]
     * @return: java.util.List<java.lang.String>
     * @Author: lwei
     * @Date: 2025/9/18
     */
    @Override
    public List<String> findByCreateIds(List<String> createId) {
        if (CollUtil.isNotEmpty(createId)) {
            List<String> ids = internalcontractPOMapper.selectListByCreateId(createId);
            if (CollUtil.isNotEmpty(ids)) {
                return ids;
            }
        }
        return null;
    }

    /**
     * @Description: 根据id，更新审批状态
     * @Param: [processStatusDTO]
     * @return: void
     * @Author: lwei
     * @Date: 2025/9/19
     */
    @Override
    public void updateProcessStatusById(BussProcessStatusDTO processStatusDTO) {
        String id = processStatusDTO.getId();
        if (StringUtils.isNotBlank(id)) {
            InternalcontractPO bussContactInfo = new InternalcontractPO();
            bussContactInfo.setId(id);
            bussContactInfo.setBussProcessStatus(processStatusDTO.getBussProcessStatus());

            internalcontractPOMapper.updateByPrimaryKeySelective(bussContactInfo);
        }

    }

    /** 
    * @Description: 导入数据从excel文件
    * @Param: [file]
    * @return: void
    * @Author: lwei
    * @Date: 2025/9/25
    */
    @Override
    public void importDataFromExcelFile(MultipartFile file) {

        List<InternalcontractPO> contractList = ExcelUtils.readFromFile(file,"合同信息",InternalcontractPO.class);
        List<BussInternalContractProductPO> productList = ExcelUtils.readFromFile(file,"关联产品信息",BussInternalContractProductPO.class);

        if (CollUtil.isEmpty(contractList)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_BUSS_INTERNAL_CONTRACT_DATA_NULL);
        }
        if (CollUtil.isEmpty(productList)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_BUSS_INTERNAL_CONTRACT_PRODUCT_DATA_NULL);
        }

        InternalContractQueryListDTO internalContractQueryListDTO = new InternalContractQueryListDTO();
        List<InternalcontractPO> internalcontractPOS = internalcontractPOMapper.selectlist(internalContractQueryListDTO);
        Boolean bNumExist = false;

        for (Object contract : contractList) {
            bNumExist = false;
            InternalcontractPO internalcontract = (InternalcontractPO)contract;
            //判断合同编码是否存在，不存在则增加，存在则更新
            if (CollUtil.isNotEmpty(internalcontractPOS)) {
                for (InternalcontractPO internalcontractPO : internalcontractPOS) {
                    if (internalcontractPO.getContractNum().equals(internalcontract.getContractNum())) {
                        //存在编码
                        bNumExist = true;
                        internalcontract.setId(internalcontractPO.getId());
                        break;
                    }
                }
            }


            InternalContractnInsertDTO basedata = new InternalContractnInsertDTO();
            BeanUtils.copyProperties(internalcontract,basedata);
            //此时销售负责人只有名称跟部门，没有id，需要通过名称跟部门获取信息
            if (StringUtils.isBlank(basedata.getSalesManager()) || StringUtils.isBlank(basedata.getSalesManagerDept())) {
                throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_DEPARTMENT_OR_USER_NAME_NULL);
            }
            UserQueryDTO userQueryDTO = new UserQueryDTO();
            userQueryDTO.setNicknameEqual(basedata.getSalesManager());
            userQueryDTO.setDepName(basedata.getSalesManagerDept());

            List<SysUserVO> sysUserVOList = userManagementService.queryUserList(userQueryDTO);
            if (CollUtil.isEmpty(sysUserVOList)) {
                throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_DEPARTMENT_USER_NOT_FOUND);
            }
            if (sysUserVOList.size() > 1) {
                throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_DEPARTMENT_USER_TO_MUCH);
            }
            SysUserVO userinfo = sysUserVOList.get(0);

            basedata.setSalesManagerId(userinfo.getId().toString());

            //找对应的产品列表
            List<AddBussInternalContractProductDTO> productDatas = new ArrayList<>();
            for (Object product : productList) {

                BussInternalContractProductPO internalContractProduct = (BussInternalContractProductPO)product;
                if (basedata.getContractNum().equals(((BussInternalContractProductPO) product).getInternalContractId())) {
                    AddBussInternalContractProductDTO bussInternalContractProductDTO = new AddBussInternalContractProductDTO();
                    BeanUtils.copyProperties(internalContractProduct,bussInternalContractProductDTO);
                    productDatas.add(bussInternalContractProductDTO);
                }
            }



            if (bNumExist) {
                //更新数据
                InternalContractnUpdateFrontDTO internalContractnUpdateFrontDTO = new InternalContractnUpdateFrontDTO();
                InternalContractUpdateDTO updatebaseData = new InternalContractUpdateDTO();
                BeanUtils.copyProperties(internalcontract,updatebaseData);
                internalContractnUpdateFrontDTO.setBaseData(updatebaseData);
                internalContractnUpdateFrontDTO.setProductDatas(productDatas);
                updateInternalContractn(internalContractnUpdateFrontDTO);
                return;
            }
            //增加数据
            InternalContractnAddFrontDTO internalContractnAddFrontDTO = new InternalContractnAddFrontDTO();
            internalContractnAddFrontDTO.setBaseData(basedata);
            internalContractnAddFrontDTO.setProductDatas(productDatas);
            addInternalContractn(internalContractnAddFrontDTO);
        }


    }

}
