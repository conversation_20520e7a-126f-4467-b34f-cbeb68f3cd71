package com.swxa.prp.business.contract.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.swxa.prp._enum.RpcAuthConstant;
import com.swxa.prp.business.contract.dto.*;
import com.swxa.prp.business.contract.dao.BussInternalContractProductDBMapper;
import com.swxa.prp.business.contract.model.BussInternalContractProductPO;
import com.swxa.prp.business.contract.model.InternalcontractPO;
import com.swxa.prp.business.contract.service.BussInteralContractProductService;
import com.swxa.prp.business.contract.service.InternalContractService;
import com.swxa.prp.business.contract.vo.BussInternalContractProductVO;
import com.swxa.prp.business.contract.vo.InternalContractDataInfoVO;
import com.swxa.prp.business.contract.vo.InternalContractServiceVO;
import com.swxa.prp.business.supplymanage.dto.ProductSerialNumBatchDTO;
import com.swxa.prp.business.supplymanage.service.PaymentReceiptOrderProductSerialService;
import com.swxa.prp.constant.SwErrorCodeConstant;
import com.swxa.prp.exception.SwPrpException;
import com.swxa.prp.util.MyUtil;
import com.swxa.prp.util.TableResultUtil;
import com.swxa.prp.util.UserUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
public class BussInteralContractProductServiceImpl implements BussInteralContractProductService {

    @Autowired
    private BussInternalContractProductDBMapper bussInternalContractProductDBMapper;
    @Autowired
    private InternalContractService internalContractService;
    private static final Logger log = LoggerFactory.getLogger("dayLogger");


    @Autowired
    private PaymentReceiptOrderProductSerialService paymentReceiptOrderProductSerialService;

    /**
     * 增加内部合同产品信息
     *
     * @param addBussInternalContractProductDTO
     */
    @Transactional
    @Override
    public void add(AddBussInternalContractProductDTO addBussInternalContractProductDTO) {

        BussInternalContractProductPO bussInternalContractProductDB = new BussInternalContractProductPO();
        //设置数据信息
        BeanUtils.copyProperties(addBussInternalContractProductDTO, bussInternalContractProductDB);
        bussInternalContractProductDB.setId(MyUtil.getRandomID());
        bussInternalContractProductDB.setCreateId(UserUtil.getLoginUserId());
        bussInternalContractProductDB.setCreateTime(new Date());
        bussInternalContractProductDB.setUpdateTime(new Date());


        bussInternalContractProductDBMapper.insertSelective(bussInternalContractProductDB);

    }

    /**
     * 删除指定id的内部合同产品信息
     *
     * @param deleteBussInternalContractProductDTO
     */
    @Override
    public void deleteById(DeleteBussInternalContractProductDTO deleteBussInternalContractProductDTO) {
        String id = deleteBussInternalContractProductDTO.getId();
        if (Objects.isNull(id)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_BUSS_INTERNAL_PRODUCT_ID_NULL);
        }

        bussInternalContractProductDBMapper.deleteByPrimaryKey(id);
    }

    /**
     * 根据id修改内部合同产品信息
     *
     * @param updateBussInternalContractProductDTO
     */
    @Transactional
    @Override
    public void updateById(UpdateBussInternalContractProductDTO updateBussInternalContractProductDTO) {
        String id = updateBussInternalContractProductDTO.getId();
        if (StringUtils.isBlank(id)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_BUSS_INTERNAL_PRODUCT_ID_NULL);
        }

        BussInternalContractProductPO bussInternalContractProductDB = bussInternalContractProductDBMapper.selectByPrimaryKey(id);
        if (Objects.isNull(bussInternalContractProductDB)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_BUSS_INTERNAL_PRODUCT_NOT_FOUND);
        }

        BeanUtils.copyProperties(updateBussInternalContractProductDTO, bussInternalContractProductDB);
        bussInternalContractProductDB.setUpdateTime(new Date());

        bussInternalContractProductDBMapper.updateByPrimaryKeySelective(bussInternalContractProductDB);
    }

    /**
     * 根据ID查询内部合同产品信息
     *
     * @param queryBussInternalContractProductDTO
     */
    @Override
    public BussInternalContractProductVO queryById(QueryBussInternalContractProductDTO queryBussInternalContractProductDTO) {

        BussInternalContractProductVO bussInternalContractProductVO = new BussInternalContractProductVO();

        String id = queryBussInternalContractProductDTO.getId();
        if (Objects.isNull(id)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_BUSS_INTERNAL_PRODUCT_CONTRACT_ID_NULL);
        }

        BussInternalContractProductPO bussInternalContractProductDB = bussInternalContractProductDBMapper.selectByPrimaryKey(id);
        if (!Objects.isNull(bussInternalContractProductDB)) {
            BeanUtils.copyProperties(bussInternalContractProductDB, bussInternalContractProductVO);
        }

        return bussInternalContractProductVO;
    }


    /**
     * 分页查询
     *
     * @param queryBussInternalContractProductToPageDTO
     * @return
     */
    @Override
    public TableResultUtil queryToPage(QueryBussInternalContractProductToPageDTO queryBussInternalContractProductToPageDTO) {

        log.info("queryToPage param={}", JSONObject.toJSONString(queryBussInternalContractProductToPageDTO));
        // 分页
        Page<Object> page = PageHelper.startPage(queryBussInternalContractProductToPageDTO.getPageNum(), queryBussInternalContractProductToPageDTO.getPageSize());
        // todo -查询条件待定，拼接查询条件
        QueryWrapper<BussInternalContractProductPO> queryWrapper = Wrappers.query();
        if (StringUtils.isNotEmpty(queryBussInternalContractProductToPageDTO.getInternalContractId())) {
            queryWrapper.lambda().eq(BussInternalContractProductPO::getInternalContractId, queryBussInternalContractProductToPageDTO.getInternalContractId());
        }
        if (StringUtils.isNotEmpty(queryBussInternalContractProductToPageDTO.getUnineCode())) {
            queryWrapper.lambda().eq(BussInternalContractProductPO::getUnineCode, queryBussInternalContractProductToPageDTO.getUnineCode());
        }
        if (StringUtils.isNotEmpty(queryBussInternalContractProductToPageDTO.getProductName())) {
            queryWrapper.lambda().eq(BussInternalContractProductPO::getProductName, queryBussInternalContractProductToPageDTO.getProductName());
        }
        if (StringUtils.isNotEmpty(queryBussInternalContractProductToPageDTO.getProductCode())) {
            queryWrapper.lambda().eq(BussInternalContractProductPO::getProductCode, queryBussInternalContractProductToPageDTO.getProductCode());
        }
        if (StringUtils.isNotEmpty(queryBussInternalContractProductToPageDTO.getProductModel())) {
            queryWrapper.lambda().eq(BussInternalContractProductPO::getProductModel, queryBussInternalContractProductToPageDTO.getProductModel());
        }
        // 按更新时间倒序查询
        queryWrapper.lambda().orderByDesc(BussInternalContractProductPO::getUpdateTime);
        List<BussInternalContractProductPO> bussInternalContractProductIPage = bussInternalContractProductDBMapper.selectList(queryWrapper);

        List<BussInternalContractProductVO> result = new ArrayList<>();
        for (BussInternalContractProductPO bussInternalContractProduct : bussInternalContractProductIPage) {
            BussInternalContractProductVO resp = new BussInternalContractProductVO();
            BeanUtils.copyProperties(bussInternalContractProduct, resp);
            result.add(resp);
        }
        PageInfo<BussInternalContractProductVO> pageInfo = new PageInfo<>(result);
        pageInfo.setTotal(page.getTotal());

        return TableResultUtil.buildTableResult(pageInfo);
    }

    @Override
    public void deleteByContractId(String contractId) {
        if (StringUtils.isBlank(contractId)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_BUSS_INTERNAL_PRODUCT_CONTRACT_ID_NULL);
        }
        //查询列表信息
        QueryWrapper<BussInternalContractProductPO> queryWrapper = Wrappers.query();
        queryWrapper.lambda().eq(BussInternalContractProductPO::getInternalContractId, contractId);
        List<BussInternalContractProductPO> bussInternalContractProductIPage = bussInternalContractProductDBMapper.selectList(queryWrapper);
        if (CollUtil.isNotEmpty(bussInternalContractProductIPage)) {
            //删除
            for (BussInternalContractProductPO bussInternalContractProductDB : bussInternalContractProductIPage) {
                bussInternalContractProductDBMapper.deleteByPrimaryKey(bussInternalContractProductDB.getId());
            }
        }


    }

    @Override
    public List<BussInternalContractProductVO> queryList(InternalContractProductQueryListDTO internalContractQueryListDTO) {


        //列表查询
        List<BussInternalContractProductPO> internalcontractPOS = bussInternalContractProductDBMapper.queryList(internalContractQueryListDTO);

        List<BussInternalContractProductVO> resListVOS = new ArrayList<>();

        //不为空处理返回结果
        if (CollUtil.isNotEmpty(internalcontractPOS)) {
            for (BussInternalContractProductPO dict : internalcontractPOS) {
                BussInternalContractProductVO bussInternalContractProductVO = new BussInternalContractProductVO();
                BeanUtils.copyProperties(dict, bussInternalContractProductVO);


                //查询产品序列号和生产批号
                //获取生产批号和序列号
                String internalContractId = dict.getInternalContractId();
                InternalContractQueryIdDTO internalContractQueryIDDTO = new InternalContractQueryIdDTO();
                internalContractQueryIDDTO.setId(internalContractId);
                InternalcontractPO internalcontractPO = internalContractService.selectInternalcontractById(internalContractQueryIDDTO);

                if (Objects.nonNull(internalcontractPO)) {
                    ProductSerialNumBatchDTO productSerialNumBatchDTO = paymentReceiptOrderProductSerialService.queryProductSerialNumBatch(RpcAuthConstant.INTERNAL_CONTRACT_ORDER, internalcontractPO.getContractNum(), bussInternalContractProductVO.getUnineCode());
                    bussInternalContractProductVO.setProductionSerialNumber(productSerialNumBatchDTO.getProductionSerialNumber());
                    bussInternalContractProductVO.setProductionBatchNumber(productSerialNumBatchDTO.getProductionBatchNumber());
                } else {
                    bussInternalContractProductVO.setProductionSerialNumber("");
                    bussInternalContractProductVO.setProductionBatchNumber("");
                }


                resListVOS.add(bussInternalContractProductVO);
            }
        }

        return resListVOS;

    }
}
