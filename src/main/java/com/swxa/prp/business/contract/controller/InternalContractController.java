package com.swxa.prp.business.contract.controller;

import com.swxa.prp.business.contract.dto.*;
import com.swxa.prp.business.contract.service.InternalContractService;
import com.swxa.prp.business.contract.vo.InternalContractDataInfoVO;
import com.swxa.prp.business.contract.vo.InternalContractServiceVO;
import com.swxa.prp.util.MyUtil;
import com.swxa.prp.util.ResponseData;
import com.swxa.prp.util.TableResultUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @Description: 内部合同订单控制层
 * @Author: zhangweicheng
 * @Date: 2025/7/21
 */

@Controller
@RequestMapping("/internal/contract/")
public class InternalContractController {


    @Autowired
    private InternalContractService internalContractService;

    private static final Logger log = LoggerFactory.getLogger("dayLogger");




    /**
    * @Description: 前端删除
    * @Param:**********************************************************这个添加api
    * @return:
    * @Author: lidi
    * @Date: 2025/8/5
    */
    @ResponseBody
    @PostMapping(value = "/deleteFront")
    public ResponseData deleteFront (@Validated @RequestBody InternalContractDeleteFrontDTO internalContractDeleteFrontDTO) {

        internalContractService.deleteInternalContract(internalContractDeleteFrontDTO);

        return ResponseData.ok();
    }




    /** 
    * @Description: 查询内部合同，前端查询
    * @Param: [internalContractQueryIDDTO]
    * @return: com.swxa.prp.util.ResponseData
    * @Author: zhangweicheng**********************************************************这个添加api
    * @Date: 2025/7/21
    */
    
    @ResponseBody
    @PostMapping(value = "/queryById")
    public ResponseData queryById(@Validated @RequestBody InternalContractQueryIdDTO internalContractQueryIDDTO) {

        InternalContractDataInfoVO internalContractDataInfoVO = internalContractService.queryByIdCode(internalContractQueryIDDTO);

        return ResponseData.ok(true, internalContractDataInfoVO);
    }


    /**
     * @Description: 增加内部合同，前段添加
     * @Param: [contractOrderFrontAddDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng**********************************************************这个添加api
     * @Date: 2025/7/20
     */

    @ResponseBody
    @PostMapping(value = "/add")
    public ResponseData add(@Validated @RequestBody InternalContractnAddFrontDTO internalContractnAddFrontDTO) {
        internalContractService.addInternalContractn(internalContractnAddFrontDTO);
        return ResponseData.ok();
    }


    /**
     * @Description: 编辑内部合同，前端调用
     * @Param: [internalContractnUpdateFrontDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng**********************************************************这个添加api
     * @Date: 2025/7/21
     */

    @ResponseBody
    @PostMapping(value = "/update")
    public ResponseData update(@Validated @RequestBody InternalContractnUpdateFrontDTO internalContractnUpdateFrontDTO) {
        internalContractService.updateInternalContractn(internalContractnUpdateFrontDTO);
        return ResponseData.ok();
    }


    /**
     * @Description: 新增
     * @Param: [internalContractnInsertDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/21
     */

    @ResponseBody
    @PostMapping(value = "/insert")
    public ResponseData insert(@Validated @RequestBody InternalContractnInsertDTO internalContractnInsertDTO) {
        log.info("执行新增操作");
        internalContractService.insert(internalContractnInsertDTO);
        return ResponseData.ok();
    }


    /**
     * 查询列
     *
     * @param
     * @return
     */

    @ResponseBody
    @PostMapping(value = "/queryList")
    public ResponseData queryList(@Validated @RequestBody InternalContractQueryListDTO internalContractQueryListDTO) {

        List<InternalContractServiceVO> internalContractServiceVOS = internalContractService.queryList(internalContractQueryListDTO);

        return ResponseData.ok(true, internalContractServiceVOS);
    }


    /**
     * 分页查询
     *
     * @param
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/queryToPage")
    public ResponseData queryToPage(@Validated @RequestBody InternalContractQueryPageDTO internalContractQueryPageDTO) {

        TableResultUtil resultUtil = internalContractService.queryToPage(internalContractQueryPageDTO);

        return ResponseData.ok(true, resultUtil);
    }

    /**
    * @Description: 分页查询，返回基本信息及产品列表
    * @Param: [internalContractQueryPageDTO]
    * @return: com.swxa.prp.util.ResponseData
    * @Author: lwei
    * @Date: 2025/8/7
    */
    @ResponseBody
    @PostMapping(value = "/queryBaseAndProdToPage")
    public ResponseData queryBaseAndProdToPage(@Validated @RequestBody InternalContractQueryPageDTO internalContractQueryPageDTO) {

        TableResultUtil resultUtil = internalContractService.queryBaseAndProdToPage(internalContractQueryPageDTO);

        return ResponseData.ok(true, resultUtil);
    }
    /**
     * 根据ID删除关系
     * lidi
     *
     * @param
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/deleteById")
    public ResponseData deletePersonById(@Validated @RequestBody InternalContractDeleteDTO internalContractDeleteDTO) {

        internalContractService.deleteById(internalContractDeleteDTO);

        return ResponseData.ok();
    }


    /**
     * 根据用户ID修改用户信息
     * lidi
     *
     * @param
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/updateById")
    public ResponseData updateById(@Validated @RequestBody InternalContractUpdateDTO InternalContractUpdateDTO) {


        log.info("更新");
        internalContractService.updateById(InternalContractUpdateDTO);

        return ResponseData.ok();
    }

    /** 
    * @Description: 通过excel文件导入数据
    * @Param: [file]
    * @return: com.swxa.prp.util.ResponseData
    * @Author: lwei
    * @Date: 2025/9/25
    */
    @ResponseBody
    @PostMapping(value = "/importDataToExcelFile")
    public ResponseData importDataToExcelFile(@Validated @RequestBody MultipartFile file) {

        internalContractService.importDataFromExcelFile(file);

        return ResponseData.ok();
    }


    @ResponseBody
    @PostMapping(value = "/exportDataToExcelFile")
    public ResponseData exportDataToExcelFile(@Validated @RequestBody InternalContractExportDataToExcelFileDTO internalContractExportDataToExcelFileDTO){

        //ContractExportDataToExcelFileVO contractExportDataToExcelFileVO = contractOrderService.exportDataToExcelFile(contractExportDataToExcelFileDTO);

        return ResponseData.ok();
    }

}
