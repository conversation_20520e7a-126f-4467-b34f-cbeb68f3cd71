package com.swxa.prp.business.contract.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.ISelect;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.swxa.prp._enum.RpcAuthConstant;
import com.swxa.prp.business.act.conditionFields.dto.BussProcessStatusDTO;
import com.swxa.prp.business.attach.dto.AttachmentIdRelationInsertDTO;
import com.swxa.prp.business.attach.dto.AttachmentIdRelationQueryListDTO;
import com.swxa.prp.business.attach.model.Attachment;
import com.swxa.prp.business.attach.service.AttachmentIdRelationService;
import com.swxa.prp.business.attach.service.AttachmentService;
import com.swxa.prp.business.attach.vo.AttachmentRelationServiceVO;
import com.swxa.prp.business.contract.dao.ContractOrderMapper;
import com.swxa.prp.business.contract.dto.*;
import com.swxa.prp.business.contract.model.ContractOrderPO;
import com.swxa.prp.business.contract.service.*;
import com.swxa.prp.business.contract.vo.*;
import com.swxa.prp.business.customer.dto.QueryCustomerIdDTO;
import com.swxa.prp.business.customer.service.CustomerService;
import com.swxa.prp.business.customer.vo.BussCustomerVO;
import com.swxa.prp.business.dataauth._enum.BussOperateDDLTypeEnum;
import com.swxa.prp.business.dataauth._enum.CrmBussTypeEnum;
import com.swxa.prp.business.dataauth.dataauthrules.service.DataAuthService;
import com.swxa.prp.business.productmanage.service.ProductManageService;
import com.swxa.prp.business.productmanage.vo.ProductDataInfoVO;
import com.swxa.prp.business.projectopportunity.dto.FollowProjectOpportunityTeamPersonDTO;
import com.swxa.prp.business.shippingnotice.bo.DealProductDeliverNumBO;
import com.swxa.prp.business.shippingnotice.bo.ProductDeliverNumBO;
import com.swxa.prp.business.supplymanage.dto.ProductSerialNumBatchDTO;
import com.swxa.prp.business.supplymanage.service.PaymentReceiptOrderProductSerialService;
import com.swxa.prp.business.userinfo.dto.QueryUserByIdNameDTO;
import com.swxa.prp.business.userinfo.service.UserInfoService;
import com.swxa.prp.constant.SwErrorCodeConstant;
import com.swxa.prp.datamask.SwDeSensitive;
import com.swxa.prp.exception.SwPrpException;
import com.swxa.prp.model.SysUserVO;
import com.swxa.prp.util.MyUtil;
import com.swxa.prp.util.SqlToolsUtil;
import com.swxa.prp.util.TableResultUtil;
import com.swxa.prp.util.UserUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * @Description: 合同订单模块业务处理
 * @Author: zhangweicheng
 * @Date: 2025/7/20
 */

@Service
public class ContractOrderServicelmpl implements ContractOrderService {

    @Autowired
    private ContractOrderMapper contractOrderMapper;
    @Autowired
    private ContractProductDataService contractProductDataService;
    @Autowired
    private ContractPersonPercentService contractPersonPercentService;
    @Autowired
    private ContractSupportServiceService contractSupportServiceService;
    @Autowired
    private ContractPaymentConditionService contractPaymentConditionService;
    @Autowired
    private ContractCollectPaymentPlanService contractCollectPaymentPlanService;
    @Autowired
    private AttachmentIdRelationService attachmentIdRelationService;

    @Autowired
    private AttachmentService attachmentService;

    @Lazy
    @Autowired
    private DataAuthService dataAuthService;

    @Autowired
    private PaymentReceiptOrderPlanService paymentReceiptOrderPlanService;

    @Autowired
    private ProductManageService productManageService;
    @Autowired
    private PaymentReceiptOrderProductSerialService paymentReceiptOrderProductSerialService;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private UserInfoService userInfoService;

    private static final Logger log = LoggerFactory.getLogger("dayLogger");


    @Transactional
    @Override
    public ContractOrderPO insert(ContractOrderAddDTO contractOrderAddDTO) {

        log.info("添加合同订单，{}", contractOrderAddDTO);

        ContractOrderPO contractOrderPO = new ContractOrderPO();
        BeanUtils.copyProperties(contractOrderAddDTO, contractOrderPO);

        contractOrderPO.setId(MyUtil.getRandomID());
        contractOrderPO.setCreateId(UserUtil.getLoginUserId());
        contractOrderPO.setCreateTime(new Date());
        contractOrderPO.setUpdateTime(new Date());


        //处理合同成员
        List<FollowProjectOpportunityTeamPersonDTO> projectPersonDatas = contractOrderAddDTO.getProjectPersonDatas();
        if (CollUtil.isNotEmpty(projectPersonDatas)) {
            List<String> names = new ArrayList<>();
            List<String> ids = new ArrayList<>();
            for (FollowProjectOpportunityTeamPersonDTO followProjectOpportunityTeamPersonDTO : projectPersonDatas) {
                names.add(followProjectOpportunityTeamPersonDTO.getNickname());
                ids.add(followProjectOpportunityTeamPersonDTO.getId());
            }

            if (CollUtil.isEmpty(ids)) {
                contractOrderPO.setTeamPersonIds("");
                contractOrderPO.setTeamPersonNames("");
            } else {
                contractOrderPO.setTeamPersonIds(String.join(",", ids));
                contractOrderPO.setTeamPersonNames(String.join(",", names));
            }
        }

        //添加
        contractOrderMapper.insertSelective(contractOrderPO);

        return contractOrderPO;

    }


    @Override
    @Transactional
    public void deleteById(ContractOrderDeleteDTO contractOrderDeleteDTO) {

        //获取用户id
        String id = contractOrderDeleteDTO.getId();
        if (Objects.isNull(id)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_ID_NULL);
        }

        ContractOrderPO contractOrderPO = contractOrderMapper.selectByPrimaryKey(id);
        if (Objects.isNull(contractOrderPO)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_ORDER_BASE_DATA_NULL);
        }

        //先判断是否可以编辑
        dataAuthService.doEditOrDelete(CrmBussTypeEnum.contractOrder.getCode(), id, BussOperateDDLTypeEnum.DELETE);


        //1.删除合同订单基本信息
        contractOrderMapper.deleteByPrimaryKey(id);

        //2.删除人员成分占比
        contractPersonPercentService.deleteByContractId(id);

        //3.删除该合同的所有维保服务
        contractSupportServiceService.deleteByContractId(id);

        //4.该合同的付款条件
        contractPaymentConditionService.deleteByContractId(id);

        //5.删除该合同回款计划
        contractCollectPaymentPlanService.deleteByContractId(id);

        //6.删除该合同的产品信息
        contractProductDataService.deleteByContractId(id);

        //删除附件
        attachmentIdRelationService.deleteByBussId(id);
    }


    @Override
    @Transactional
    public void updateById(ContractOrderUpdateDTO contractOrderUpdateDTO) {


        ContractOrderPO contractOrderPO = new ContractOrderPO();
        String id = contractOrderUpdateDTO.getId();
        if (Objects.isNull(id)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_ID_NULL);
        }
        //赋值
        BeanUtils.copyProperties(contractOrderUpdateDTO, contractOrderPO);
        contractOrderPO.setId(id);

        //处理合同成员
        List<FollowProjectOpportunityTeamPersonDTO> projectPersonDatas = contractOrderUpdateDTO.getProjectPersonDatas();
        if (CollUtil.isNotEmpty(projectPersonDatas)) {
            List<String> names = new ArrayList<>();
            List<String> ids = new ArrayList<>();
            for (FollowProjectOpportunityTeamPersonDTO followProjectOpportunityTeamPersonDTO : projectPersonDatas) {
                names.add(followProjectOpportunityTeamPersonDTO.getNickname());
                ids.add(followProjectOpportunityTeamPersonDTO.getId());
            }

            if (CollUtil.isEmpty(ids)) {
                contractOrderPO.setTeamPersonIds("");
                contractOrderPO.setTeamPersonNames("");
            } else {
                contractOrderPO.setTeamPersonIds(String.join(",", ids));
                contractOrderPO.setTeamPersonNames(String.join(",", names));
            }
        } else {
            contractOrderPO.setTeamPersonIds("");
            contractOrderPO.setTeamPersonNames("");
        }

        //更新
        contractOrderMapper.updateByPrimaryKeySelective(contractOrderPO);
    }


    /**
     * 查询列表
     * lidi
     *
     * @param
     * @return
     */

    @Override
    public List<ContractOrderVO> queryList(ContractOrderQueryListDTO contractOrderQueryListDTO) {

        //列表查询
        if (contractOrderQueryListDTO.getIsAuth()) {
            //追加数据授权条件
            List<String> bussIds = dataAuthService.getBussIds(CrmBussTypeEnum.contractOrder.getCode());
            //设置  可访问的业务ids  contractOrderQueryListDTO
            if (CollUtil.isNotEmpty(bussIds)) {
                contractOrderQueryListDTO.setBussIds(bussIds);
            }
        }


        List<ContractOrderPO> contractOrderPOS = contractOrderMapper.selectlists(contractOrderQueryListDTO);

        List<ContractOrderVO> resListVOS = new ArrayList<>();

        //不为空处理返回结果
        if (CollUtil.isNotEmpty(contractOrderPOS)) {
            for (ContractOrderPO contractOrderPO : contractOrderPOS) {

                //处理合同金额 如果金额为null,设置为0
                BigDecimal contractSum = contractOrderPO.getContractSum();
                if (Objects.isNull(contractSum)) {
                    contractSum = BigDecimal.ZERO;
                }
                contractOrderPO.setContractSum(contractSum);


                //赋值信息到VO
                ContractOrderVO contractOrderVO = new ContractOrderVO();
                BeanUtils.copyProperties(contractOrderPO, contractOrderVO);

                //处理合同的已发货金额和未发货金额
                updateContractDeliverCount(contractOrderVO);


                //处理下项目团队成员
                String teamIds = contractOrderPO.getTeamPersonIds();
                String teamNames = contractOrderPO.getTeamPersonNames();
                if (StringUtils.isNotBlank(teamIds) && StringUtils.isNotBlank(teamNames)) {
                    String[] ids = teamIds.trim().split(",");
                    String[] names = teamNames.trim().split(",");
                    if (ids.length == names.length) {
                        List<FollowProjectOpportunityTeamPersonDTO> projectPersonDatas = new ArrayList<>();

                        for (Integer i = 0; i < ids.length; i++) {
                            FollowProjectOpportunityTeamPersonDTO followProjectOpportunityTeamPersonDTO = new FollowProjectOpportunityTeamPersonDTO();
                            followProjectOpportunityTeamPersonDTO.setId(ids[i]);
                            followProjectOpportunityTeamPersonDTO.setNickname(names[i]);
                            projectPersonDatas.add(followProjectOpportunityTeamPersonDTO);
                        }
                        contractOrderVO.setProjectPersonDatas(projectPersonDatas);
                    }
                }


                /***处理合同的总额和回款总额和应收余额**/
                ContractCollectPaymentPlanQueryListDTO contractCollectPaymentPlanQueryListDTO = new ContractCollectPaymentPlanQueryListDTO();
                contractCollectPaymentPlanQueryListDTO.setContractId(contractOrderPO.getId());
                //合同回款总金额
                BigDecimal contractSumBigDecimal = BigDecimal.ZERO;
                List<ContractCollectPaymentPlanVO> contractCollectPaymentPlanVOS = contractCollectPaymentPlanService.queryList(contractCollectPaymentPlanQueryListDTO);
                if (CollUtil.isNotEmpty(contractCollectPaymentPlanVOS)) {

                    for (ContractCollectPaymentPlanVO contractCollectPaymentPlanVO : contractCollectPaymentPlanVOS) {
                        //就一个已收总额
                        PaymentReceiptOrderPlanQueryDTO paymentReceiptOrderPlanQueryDTO = new PaymentReceiptOrderPlanQueryDTO();
                        paymentReceiptOrderPlanQueryDTO.setPaymentPlanCode(contractCollectPaymentPlanVO.getPaymentPlanCode());
                        List<PaymentReceiptOrderPlanVO> paymentReceiptOrderPlanVOS = paymentReceiptOrderPlanService.queryList(paymentReceiptOrderPlanQueryDTO);
                        if (CollUtil.isNotEmpty(paymentReceiptOrderPlanVOS)) {
                            //计算回款单里的回款计划的钱信息
                            for (PaymentReceiptOrderPlanVO paymentReceiptOrderPlanVO : paymentReceiptOrderPlanVOS) {
                                //根据此关系查询初具体的回款计划的详情信息
                                BigDecimal paymentCount = paymentReceiptOrderPlanVO.getPaymentCount();
                                if (Objects.nonNull(paymentCount)) {
                                    //求和
                                    contractSumBigDecimal = contractSumBigDecimal.add(paymentCount);
                                }
                            }
                        }
                    }
                }

                //设置合同回款总金额和合同应收余额
                contractOrderVO.setContractPaymentCount(contractSumBigDecimal);
                contractOrderVO.setContractNoPaymentCount(contractSum.subtract(contractSumBigDecimal));
                /***处理合同的总额和回款总额和应收余额**/

                resListVOS.add(contractOrderVO);
            }
        }

        return resListVOS;
    }

    /**
     * @Description: 处理合同的已发货金额和未发货金额：根据发货数量计算出来的俩个值
     * @Param: [contractOrderVO]
     * @return: void
     * @Author: zhangweicheng
     * @Date: 2025/8/25
     */
    private void updateContractDeliverCount(ContractOrderAndProductsVO contractOrderAndProductsVO) {
        String id = contractOrderAndProductsVO.getId();
        if (StringUtils.isBlank(id)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_ID_NULL);
        }

        //查询产品信息
        ContractProductQueryListDTO contractProductQueryListDTO = new ContractProductQueryListDTO();
        contractProductQueryListDTO.setContractId(id);
        List<ContractProductDataVO> contractProductDataVOS = contractProductDataService.queryList(contractProductQueryListDTO);
        if (CollUtil.isNotEmpty(contractProductDataVOS)) {
            //已发货金额
            BigDecimal contractDeliverSumDecimal = BigDecimal.ZERO;
            for (ContractProductDataVO contractProductDataVO : contractProductDataVOS) {
                //计算每个产品的  已发货钱=已发货数量*含税单价
                Integer deliverNum = contractProductDataVO.getDeliverNum();
                //含税单价
                BigDecimal unitTaxPrice = contractProductDataVO.getUnitTaxPrice();
                if (Objects.nonNull(deliverNum) && Objects.nonNull(unitTaxPrice)) {
                    //只有数量和含税单价都不为空才回去计算已发货的价
                    BigDecimal multiply = unitTaxPrice.multiply(new BigDecimal(deliverNum));
                    contractDeliverSumDecimal = contractDeliverSumDecimal.add(multiply);
                }
            }
            //设置已发货金钱和未发货金钱
            contractOrderAndProductsVO.setContractDeliveryCount(contractDeliverSumDecimal);
            contractOrderAndProductsVO.setContractNoPaymentCount(contractOrderAndProductsVO.getContractSum().subtract(contractDeliverSumDecimal));
        }
    }


    /**
     * 在service层实现分页查询 contractParty
     * lidi
     *
     * @param
     * @return
     */
    @Override
    public TableResultUtil queryToPage(ContractOrderQueryPageDTO contractOrderQueryPageDTO) {


        //追加数据授权条件
        List<String> bussIds = dataAuthService.getBussIds(CrmBussTypeEnum.contractOrder.getCode());


        //分页查询
        PageInfo<ContractOrderVO> pageInfo = PageHelper.startPage(contractOrderQueryPageDTO.getPageNum(), contractOrderQueryPageDTO.getPageSize()).doSelectPageInfo(new ISelect() {

            @Override
            public void doSelect() {
                ContractOrderQueryListDTO contractOrderQueryListDTO = new ContractOrderQueryListDTO();
                BeanUtils.copyProperties(contractOrderQueryPageDTO, contractOrderQueryListDTO);
                //设置各种赋值条件
                if (CollUtil.isNotEmpty(contractOrderQueryPageDTO.getAdvanceQueryGroups())) {
                    String sql = SqlToolsUtil.constructFields(contractOrderQueryPageDTO.getAdvanceQueryGroups());
                    if (StringUtils.isNotBlank(sql)) {
                        log.info("sqlWhere = {}", sql);
                        contractOrderQueryListDTO.setAdvanceQuerySql(sql);
                    }
                }

                //设置  可访问的业务ids  contractOrderQueryListDTO
                if (CollUtil.isNotEmpty(bussIds)) {
                    contractOrderQueryListDTO.setBussIds(bussIds);
                    contractOrderQueryListDTO.setIsAuth(false);
                }

                queryList(contractOrderQueryListDTO);
            }
        });

        //处理合同里面的合同回款金额和合同应收余额
        List<ContractOrderVO> list = pageInfo.getList();
        if (CollUtil.isNotEmpty(list)) {
            List<ContractOrderVO> contractOrderVOS = JSONObject.parseArray(JSONObject.toJSONString(list), ContractOrderVO.class);
            for (ContractOrderVO contractOrderVO : contractOrderVOS) {

                //处理合同金额问题
                BigDecimal contractSum = contractOrderVO.getContractSum();
                if (Objects.isNull(contractSum)) {
                    contractSum = BigDecimal.ZERO;
                }
                contractOrderVO.setContractSum(contractSum);


                ContractCollectPaymentPlanQueryListDTO contractCollectPaymentPlanQueryListDTO = new ContractCollectPaymentPlanQueryListDTO();
                contractCollectPaymentPlanQueryListDTO.setContractId(contractOrderVO.getId());

                //合同回款总金额
                BigDecimal contractSumBigDecimal = BigDecimal.ZERO;

                List<ContractCollectPaymentPlanVO> contractCollectPaymentPlanVOS = contractCollectPaymentPlanService.queryList(contractCollectPaymentPlanQueryListDTO);
                if (CollUtil.isNotEmpty(contractCollectPaymentPlanVOS)) {

                    for (ContractCollectPaymentPlanVO contractCollectPaymentPlanVO : contractCollectPaymentPlanVOS) {
                        /**处理回款单里面的回款计划信息，将钱设置到胡款计划里面**/
                        //就一个已收总额

                        PaymentReceiptOrderPlanQueryDTO paymentReceiptOrderPlanQueryDTO = new PaymentReceiptOrderPlanQueryDTO();
                        paymentReceiptOrderPlanQueryDTO.setPaymentPlanCode(contractCollectPaymentPlanVO.getPaymentPlanCode());
                        List<PaymentReceiptOrderPlanVO> paymentReceiptOrderPlanVOS = paymentReceiptOrderPlanService.queryList(paymentReceiptOrderPlanQueryDTO);
                        if (CollUtil.isNotEmpty(paymentReceiptOrderPlanVOS)) {
                            //计算回款单里的回款计划的钱信息
                            for (PaymentReceiptOrderPlanVO paymentReceiptOrderPlanVO : paymentReceiptOrderPlanVOS) {
                                //根据此关系查询初具体的回款计划的详情信息
                                BigDecimal paymentCount = paymentReceiptOrderPlanVO.getPaymentCount();
                                if (Objects.nonNull(paymentCount)) {
                                    //求和
                                    contractSumBigDecimal = contractSumBigDecimal.add(paymentCount);
                                }
                            }
                        }
                    }
                }

                //设置合同回款总金额和合同应收余额
                contractOrderVO.setContractPaymentCount(contractSumBigDecimal);
                contractOrderVO.setContractNoPaymentCount(contractSum.subtract(contractSumBigDecimal));


                //设置合同的已发货金额和未发货金额
                updateContractDeliverCount(contractOrderVO);

            }
            pageInfo.setList(contractOrderVOS);
        }


        return TableResultUtil.buildTableResult(pageInfo);
    }


    @Override
    public TableResultUtil queryCaseToPage(ContractOrderQueryPageDTO contractOrderQueryPageDTO) {


        //分页查询
        PageInfo<Object> pageInfo = PageHelper.startPage(contractOrderQueryPageDTO.getPageNum(), contractOrderQueryPageDTO.getPageSize()).doSelectPageInfo(new ISelect() {

            @Override
            public void doSelect() {
                ContractOrderQueryListDTO contractOrderQueryListDTO = new ContractOrderQueryListDTO();
                BeanUtils.copyProperties(contractOrderQueryPageDTO, contractOrderQueryListDTO);
                //设置各种赋值条件
                if (CollUtil.isNotEmpty(contractOrderQueryPageDTO.getAdvanceQueryGroups())) {
                    String sql = SqlToolsUtil.constructFields(contractOrderQueryPageDTO.getAdvanceQueryGroups());
                    if (StringUtils.isNotBlank(sql)) {
                        log.info("sqlWhere = {}", sql);
                        contractOrderQueryListDTO.setAdvanceQuerySql(sql);
                    }
                }

                //不进行数据授权查询
                contractOrderQueryListDTO.setIsAuth(false);
                //合同案例库查询
                contractOrderQueryListDTO.setConfigContractCase("是");

                queryList(contractOrderQueryListDTO);
            }
        });

        //处理合同里面的合同回款金额和合同应收余额
        List<Object> list = pageInfo.getList();
        if (CollUtil.isNotEmpty(list)) {
            List<ContractOrderVO> contractOrderVOS = JSONObject.parseArray(JSONObject.toJSONString(list), ContractOrderVO.class);
            for (ContractOrderVO contractOrderVO : contractOrderVOS) {

                //处理合同金额问题
                BigDecimal contractSum = contractOrderVO.getContractSum();
                if (Objects.isNull(contractSum)) {
                    contractSum = BigDecimal.ZERO;
                }
                contractOrderVO.setContractSum(contractSum);


                ContractCollectPaymentPlanQueryListDTO contractCollectPaymentPlanQueryListDTO = new ContractCollectPaymentPlanQueryListDTO();
                contractCollectPaymentPlanQueryListDTO.setContractId(contractOrderVO.getId());

                //合同回款总金额
                BigDecimal contractSumBigDecimal = BigDecimal.ZERO;

                List<ContractCollectPaymentPlanVO> contractCollectPaymentPlanVOS = contractCollectPaymentPlanService.queryList(contractCollectPaymentPlanQueryListDTO);
                if (CollUtil.isNotEmpty(contractCollectPaymentPlanVOS)) {

                    for (ContractCollectPaymentPlanVO contractCollectPaymentPlanVO : contractCollectPaymentPlanVOS) {
                        /**处理回款单里面的回款计划信息，将钱设置到胡款计划里面**/
                        //就一个已收总额

                        PaymentReceiptOrderPlanQueryDTO paymentReceiptOrderPlanQueryDTO = new PaymentReceiptOrderPlanQueryDTO();
                        paymentReceiptOrderPlanQueryDTO.setPaymentPlanCode(contractCollectPaymentPlanVO.getPaymentPlanCode());
                        List<PaymentReceiptOrderPlanVO> paymentReceiptOrderPlanVOS = paymentReceiptOrderPlanService.queryList(paymentReceiptOrderPlanQueryDTO);
                        if (CollUtil.isNotEmpty(paymentReceiptOrderPlanVOS)) {
                            //计算回款单里的回款计划的钱信息
                            for (PaymentReceiptOrderPlanVO paymentReceiptOrderPlanVO : paymentReceiptOrderPlanVOS) {
                                //根据此关系查询初具体的回款计划的详情信息
                                BigDecimal paymentCount = paymentReceiptOrderPlanVO.getPaymentCount();
                                if (Objects.nonNull(paymentCount)) {
                                    //求和
                                    contractSumBigDecimal = contractSumBigDecimal.add(paymentCount);
                                }
                            }
                        }
                    }
                }

                //设置合同回款总金额和合同应收余额
                contractOrderVO.setContractPaymentCount(contractSumBigDecimal);
                contractOrderVO.setContractNoPaymentCount(contractSum.subtract(contractSumBigDecimal));


                //设置合同的已发货金额和未发货金额
                updateContractDeliverCount(contractOrderVO);

            }

            pageInfo.setList((List<Object>) SwDeSensitive.removeSensitive(contractOrderVOS));
        }


        return TableResultUtil.buildTableResult(pageInfo);
    }


    @Transactional
    @Override
    public void addContractOrder(ContractOrderFrontAddDTO contractOrderFrontAddDTO) {

        //1.基本信息
        ContractOrderAddDTO baseData = contractOrderFrontAddDTO.getBaseData();
        if (Objects.isNull(baseData)) {
            //合同基本信息不能为空
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_ORDER_BASE_DATA_NULL);
        }

        //判断合同内部编号是否重复
        String contractNum = baseData.getContractNum();
        ContractOrderQueryListDTO contractOrderQueryListDTO = new ContractOrderQueryListDTO();
        contractOrderQueryListDTO.setContractNum(contractNum);
        List<ContractOrderVO> contractOrderVOS = queryList(contractOrderQueryListDTO);
        if (CollUtil.isNotEmpty(contractOrderVOS)) {//说明合同编码已经存在
            //合同编码已经存在
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_ORDER_NUM_EXISTS_ERROR);
        }

        //处理脱敏数据，客户名称
        if (StringUtils.isNotBlank(baseData.getCustomerId())) {
            QueryCustomerIdDTO requestParam = new QueryCustomerIdDTO();
            requestParam.setId(baseData.getCustomerId());
            BussCustomerVO bussCustomerVO = customerService.queryById(requestParam);

            baseData.setCustomerName(bussCustomerVO.getCustomerName());
        }
        //// TODO: 2025/9/12 处理脱敏数据，最终用户名称，由于前端此字段可编辑，此处暂时不处理

        ContractOrderPO contractOrderPO = insert(baseData);

        if (Objects.isNull(contractOrderPO)) {
            //添加合同失败
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_ORDER_ADD_ERROR);
        }

        //获取合同的id
        String contractId = contractOrderPO.getId();


        //2.处理合同订单产品信息
        //合同订单总金额计算=每个产品的价税合计*数量
        BigDecimal contractSum = BigDecimal.ZERO;
        List<ContractProductDataAddDTO> productDatas = contractOrderFrontAddDTO.getProductDatas();
        if (CollUtil.isEmpty(productDatas)) {
            //合同产品信息不存在
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_ORDER_PRODUCT_DATA_NULL);
        }


        for (ContractProductDataAddDTO contractProductDataAddDTO : productDatas) {

            //计算每个产品的价税合计
            //含税单价
            BigDecimal unitTaxPrice = contractProductDataAddDTO.getUnitTaxPrice();
            //数量
            Integer productNum = contractProductDataAddDTO.getProductNum();
            if (Objects.nonNull(unitTaxPrice) && Objects.nonNull(productNum)) {
                contractSum = contractSum.add(unitTaxPrice.multiply(new BigDecimal(productNum + "")));
            }
            //添加
            contractProductDataAddDTO.setContractId(contractId);
            contractProductDataService.insert(contractProductDataAddDTO);
        }


        //更新合同总价
        ContractOrderUpdateDTO contractOrderUpdateDTO = new ContractOrderUpdateDTO();
        contractOrderUpdateDTO.setId(contractId);
        contractOrderUpdateDTO.setContractSum(contractSum);
        updateById(contractOrderUpdateDTO);


        //3.处理人员分成占比
        List<ContractPersonPercentAddDTO> personPercents = contractOrderFrontAddDTO.getPersonPercents();
        if (CollUtil.isNotEmpty(personPercents)) {
            //不为空，填写的数据必须符合要求
            //校验参数
            contractOrderFrontAddDTO.checkPersonPercents();

            //增加
            for (ContractPersonPercentAddDTO contractPersonPercentAddDTO : personPercents) {
                contractPersonPercentAddDTO.setContractId(contractId);
                contractPersonPercentService.insert(contractPersonPercentAddDTO);
            }
        }


        //4.处理维保服务
        List<ContractSupportServiceAddDTO> supportServices = contractOrderFrontAddDTO.getSupportServices();
        if (CollUtil.isNotEmpty(supportServices)) {
            //不为空，填写的数据必须符合要求
            //校验参数
            contractOrderFrontAddDTO.checkSupportServices();

            //增加
            for (ContractSupportServiceAddDTO contractSupportServiceAddDTO : supportServices) {
                contractSupportServiceAddDTO.setContractId(contractId);
                contractSupportServiceService.insert(contractSupportServiceAddDTO);
            }
        }

        //5.处理付款条件
        List<ContractPaymentConditionAddDTO> paymentConditions = contractOrderFrontAddDTO.getPaymentConditions();
        if (CollUtil.isNotEmpty(paymentConditions)) {
            //不为空，填写的数据必须符合要求
            //校验参数
            contractOrderFrontAddDTO.checkPaymentConditions();

            //增加
            for (ContractPaymentConditionAddDTO contractPaymentConditionAddDTO : paymentConditions) {
                contractPaymentConditionAddDTO.setContractId(contractId);
                contractPaymentConditionService.insert(contractPaymentConditionAddDTO);
            }
        }

        //6.处理回款计划
        List<ContractCollectPaymentPlanAddDTO> paymentPlans = contractOrderFrontAddDTO.getPaymentPlans();
        if (CollUtil.isNotEmpty(paymentPlans)) {
            //不为空，填写的数据必须符合要求
            //校验参数
            contractOrderFrontAddDTO.checkPaymentPlans();

            //增加
            for (ContractCollectPaymentPlanAddDTO contractCollectPaymentPlanAddDTO : paymentPlans) {
                contractCollectPaymentPlanAddDTO.setContractId(contractId);
                contractCollectPaymentPlanService.insert(contractCollectPaymentPlanAddDTO);
            }
        }

        //7.处理附件关系
        List<String> attachIds = contractOrderFrontAddDTO.getAttachIds();
        if (CollUtil.isNotEmpty(attachIds)) {
            for (String attId : attachIds) {
                AttachmentIdRelationInsertDTO attachmentIdRelationInsertDTO = new AttachmentIdRelationInsertDTO();
                attachmentIdRelationInsertDTO.setCurrBussId(contractId);
                attachmentIdRelationInsertDTO.setAttachBussId(attId);
                attachmentIdRelationService.insert(attachmentIdRelationInsertDTO);
            }
        }
    }


    @Override
    public void updateContractOrder(ContractOrderFrontUpdateDTO contractOrderFrontUpdateDTO) {


        //1.基本信息
        ContractOrderUpdateDTO baseData = contractOrderFrontUpdateDTO.getBaseData();
        if (Objects.isNull(baseData)) {
            //合同基本信息不能为空
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_ORDER_BASE_DATA_NULL);
        }
        //合同iD不能为空
        String contractId = baseData.getId();

        //先判断是否可以编辑
        dataAuthService.doEditOrDelete(CrmBussTypeEnum.contractOrder.getCode(), contractId, BussOperateDDLTypeEnum.EDIT);


        if (StringUtils.isBlank(contractId)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_ID_NULL);
        }
        //查询合同是不是真的存在
        ContractOrderPO contractOrderPO = contractOrderMapper.selectByPrimaryKey(contractId);
        if (Objects.isNull(contractOrderPO)) {
            //合同订单不存在
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_ORDER_NULL);
        }


        //判断合同内部编号是否重复
        String contractNum = baseData.getContractNum();
        ContractOrderQueryListDTO contractOrderQueryListDTO = new ContractOrderQueryListDTO();
        contractOrderQueryListDTO.setContractNum(contractNum);
        List<ContractOrderVO> contractOrderVOS = queryList(contractOrderQueryListDTO);
        if (CollUtil.isNotEmpty(contractOrderVOS)) {//说明合同编码已经存在
            if ((contractOrderVOS.size() > 1)) {
                //合同编码已经存在
                throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_ORDER_NUM_EXISTS_ERROR);
            } else {//判断是不是自己,非自己抛出异常
                ContractOrderVO contractOrderVO = contractOrderVOS.get(0);
                if (!baseData.getId().equals(contractOrderVO.getId())) {
                    throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_ORDER_NUM_EXISTS_ERROR);
                }
            }
        }
        //处理脱敏数据，客户名称
        if (StringUtils.isNotBlank(baseData.getCustomerId())) {
            QueryCustomerIdDTO requestParam = new QueryCustomerIdDTO();
            requestParam.setId(baseData.getCustomerId());
            BussCustomerVO bussCustomerVO = customerService.queryById(requestParam);

            baseData.setCustomerName(bussCustomerVO.getCustomerName());
        }
        //更新
        updateById(baseData);

        //2.处理合同订单产品信息
        //删除产品信息
        contractProductDataService.deleteByContractId(contractId);

        //新增产品信息
        List<ContractProductDataAddDTO> productDatas = contractOrderFrontUpdateDTO.getProductDatas();
        if (CollUtil.isNotEmpty(productDatas)) {
            for (ContractProductDataAddDTO contractProductDataAddDTO : productDatas) {
                contractProductDataAddDTO.setContractId(contractId);
                contractProductDataService.insert(contractProductDataAddDTO);
            }
        }


        //3.处理人员分成占比
        //删除人员占比
        contractPersonPercentService.deleteByContractId(contractId);
        //新增人员分成占比
        List<ContractPersonPercentAddDTO> personPercents = contractOrderFrontUpdateDTO.getPersonPercents();
        if (CollUtil.isNotEmpty(personPercents)) {
            //不为空，填写的数据必须符合要求
            //校验参数
            contractOrderFrontUpdateDTO.checkPersonPercents();

            //增加
            for (ContractPersonPercentAddDTO contractPersonPercentAddDTO : personPercents) {
                contractPersonPercentAddDTO.setContractId(contractId);
                contractPersonPercentService.insert(contractPersonPercentAddDTO);
            }
        }


        //4.处理维保服务
        //删除维保服务
        contractSupportServiceService.deleteByContractId(contractId);
        //新增维保服务
        List<ContractSupportServiceAddDTO> supportServices = contractOrderFrontUpdateDTO.getSupportServices();
        if (CollUtil.isNotEmpty(supportServices)) {
            //不为空，填写的数据必须符合要求
            //校验参数
            contractOrderFrontUpdateDTO.checkSupportServices();

            //增加
            for (ContractSupportServiceAddDTO contractSupportServiceAddDTO : supportServices) {
                contractSupportServiceAddDTO.setContractId(contractId);
                contractSupportServiceService.insert(contractSupportServiceAddDTO);
            }
        }

        //5.处理付款条件
        //删除
        contractPaymentConditionService.deleteByContractId(contractId);
        //新增
        List<ContractPaymentConditionAddDTO> paymentConditions = contractOrderFrontUpdateDTO.getPaymentConditions();
        if (CollUtil.isNotEmpty(paymentConditions)) {
            //不为空，填写的数据必须符合要求
            //校验参数
            contractOrderFrontUpdateDTO.checkPaymentConditions();
            //增加
            for (ContractPaymentConditionAddDTO contractPaymentConditionAddDTO : paymentConditions) {
                contractPaymentConditionAddDTO.setContractId(contractId);
                contractPaymentConditionService.insert(contractPaymentConditionAddDTO);
            }
        }

        //6.处理回款计划

        //新增
        List<ContractCollectPaymentPlanAddDTO> paymentPlans = contractOrderFrontUpdateDTO.getPaymentPlans();
        if (CollUtil.isNotEmpty(paymentPlans)) {//不为空的时候，需要查看当前的回款计划编码是不是已经存在了，存在则更新否则添加
            //校验参数
            contractOrderFrontUpdateDTO.checkPaymentPlans();

            //获取已存在的回款计划id和编码的信息
            Map<String, String> codeId = new HashMap<>();

            //查询已经存在的回款计划信息
            ContractCollectPaymentPlanQueryListDTO contractCollectPaymentPlanQueryListDTO = new ContractCollectPaymentPlanQueryListDTO();
            contractCollectPaymentPlanQueryListDTO.setContractId(contractId);
            List<ContractCollectPaymentPlanVO> contractCollectPaymentPlanVOS = contractCollectPaymentPlanService.queryList(contractCollectPaymentPlanQueryListDTO);
            if (CollUtil.isNotEmpty(contractCollectPaymentPlanVOS)) {
                for (ContractCollectPaymentPlanVO contractCollectPaymentPlanVO : contractCollectPaymentPlanVOS) {
                    String paymentPlanCode = contractCollectPaymentPlanVO.getPaymentPlanCode();
                    if (StringUtils.isNotBlank(paymentPlanCode)) {
                        codeId.put(contractCollectPaymentPlanVO.getPaymentPlanCode(), contractCollectPaymentPlanVO.getId());
                    }
                }
            }

            //增加
            //存在的编码，不存在的最后删除
            List<String> existCode = new ArrayList<>();
            for (ContractCollectPaymentPlanAddDTO contractCollectPaymentPlanAddDTO : paymentPlans) {
                String paymentPlanCode = contractCollectPaymentPlanAddDTO.getPaymentPlanCode();
                if (codeId.containsKey(paymentPlanCode)) {//更新
                    //设置存在的值
                    existCode.add(paymentPlanCode);
                    ContractCollectPaymentPlanUpdateDTO updateContractCollectPaymentPlan = new ContractCollectPaymentPlanUpdateDTO();
                    BeanUtils.copyProperties(contractCollectPaymentPlanAddDTO, updateContractCollectPaymentPlan);
                    updateContractCollectPaymentPlan.setId(codeId.get(paymentPlanCode));
                    //更新
                    contractCollectPaymentPlanService.updateById(updateContractCollectPaymentPlan);

                } else {//添加
                    contractCollectPaymentPlanAddDTO.setContractId(contractId);
                    contractCollectPaymentPlanService.insert(contractCollectPaymentPlanAddDTO);
                }
            }

            //已经删除回款机会信息删除掉
            Iterator<Map.Entry<String, String>> iterator = codeId.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, String> entry = iterator.next();
                if (!existCode.contains(entry.getKey())) {
                    String planId = entry.getValue();
                    // 删除不满足条件的条目
                    iterator.remove();

                    //删除回款计划
                    if (StringUtils.isNotBlank(planId)) {
                        ContractCollectPaymentPlanDeleteDTO deleteCollectPaymentPlan = new ContractCollectPaymentPlanDeleteDTO();
                        deleteCollectPaymentPlan.setId(planId);
                        contractCollectPaymentPlanService.deleteById(deleteCollectPaymentPlan);
                    }
                }
            }

        } else {//如果为空，那么删除所有的回款计划信息
            //删除
            contractCollectPaymentPlanService.deleteByContractId(contractId);
        }


        //7.处理附件关系，附件的处理跟数据的处理不一样，附件需要判断是不是没变化，或者新增的，减少的
        List<String> attachIds = contractOrderFrontUpdateDTO.getAttachIds();
        if (CollUtil.isNotEmpty(attachIds)) {

            //查询当前合同对应的附件关系
            AttachmentIdRelationQueryListDTO attachmentIdRelationQueryListDTO = new AttachmentIdRelationQueryListDTO();
            attachmentIdRelationQueryListDTO.setCurrBussId(contractId);
            List<AttachmentRelationServiceVO> attachmentRelationServiceVOS = attachmentIdRelationService.queryList(attachmentIdRelationQueryListDTO);
            if (CollUtil.isEmpty(attachmentRelationServiceVOS)) {
                //新附件直接添加
                for (String attId : attachIds) {
                    AttachmentIdRelationInsertDTO attachmentIdRelationInsertDTO = new AttachmentIdRelationInsertDTO();
                    attachmentIdRelationInsertDTO.setCurrBussId(contractId);
                    attachmentIdRelationInsertDTO.setAttachBussId(attId);
                    attachmentIdRelationService.insert(attachmentIdRelationInsertDTO);
                }
            } else {
                //暂存已经存在的附件ID
                List<String> existsAttIds = new ArrayList<>();
                for (AttachmentRelationServiceVO attachmentRelationServiceVO : attachmentRelationServiceVOS) {
                    String attachBussId = attachmentRelationServiceVO.getAttachBussId();
                    //不为空
                    if (attachIds.contains(attachBussId)) {
                        existsAttIds.add(attachBussId);
                    } else {
                        //说明附件被删除了，需要删除附件
                        attachmentIdRelationService.deleteByAttachBussId(attachBussId);
                    }
                }


                //遍历编辑时提交的附件ID，不存在的就执行添加操作
                for (String aid : attachIds) {
                    //不存在新增
                    if (!existsAttIds.contains(aid)) {
                        AttachmentIdRelationInsertDTO attachmentIdRelationInsertDTO = new AttachmentIdRelationInsertDTO();
                        attachmentIdRelationInsertDTO.setCurrBussId(contractId);
                        attachmentIdRelationInsertDTO.setAttachBussId(aid);
                        attachmentIdRelationService.insert(attachmentIdRelationInsertDTO);
                    }
                }
            }

        } else {
            attachmentIdRelationService.deleteByBussId(contractId);
        }

    }

    /**
     * @Description: 根据id查询合同信息及关联信息
     * @Param: [contractOrderQueryDTO]
     * @return: com.swxa.prp.business.contract.vo.ContractOrderAllLinkInfoVO
     * @Author: lwei
     * @Date: 2025/7/21
     */

    @Override
    public ContractOrderAllLinkInfoVO queryById(ContractOrderQueryDTO contractOrderQueryDTO) {

        ContractOrderAllLinkInfoVO contractOrderAllLinkInfoVO = new ContractOrderAllLinkInfoVO();
        String id = contractOrderQueryDTO.getId();
        if (Objects.isNull(id)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_ID_NULL);
        }

        //1. 查询合同是不是真的存在
        log.info("1-查询基本信息");
        ContractOrderPO contractOrderPO = contractOrderMapper.selectByPrimaryKey(id);
        if (Objects.isNull(contractOrderPO)) {
            //合同订单不存在
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_ORDER_NULL);
        }

        //处理合同总金额 如果不存在设置为0
        BigDecimal contractSum = contractOrderPO.getContractSum();
        if (Objects.isNull(contractSum)) {
            contractSum = BigDecimal.ZERO;
            contractOrderPO.setContractSum(contractSum);
        }


        ContractOrderVO contractOrderVO = new ContractOrderVO();

        BeanUtils.copyProperties(contractOrderPO, contractOrderVO);


        //处理团队人员
        String teamIds = contractOrderPO.getTeamPersonIds();
        String teamNames = contractOrderPO.getTeamPersonNames();
        if (StringUtils.isNotBlank(teamIds) && StringUtils.isNotBlank(teamNames)) {
            String[] ids = teamIds.trim().split(",");
            String[] names = teamNames.trim().split(",");
            if (ids.length == names.length) {
                List<FollowProjectOpportunityTeamPersonDTO> projectPersonDatas = new ArrayList<>();

                for (Integer i = 0; i < ids.length; i++) {
                    FollowProjectOpportunityTeamPersonDTO followProjectOpportunityTeamPersonDTO = new FollowProjectOpportunityTeamPersonDTO();
                    followProjectOpportunityTeamPersonDTO.setId(ids[i]);
                    followProjectOpportunityTeamPersonDTO.setNickname(names[i]);
                    projectPersonDatas.add(followProjectOpportunityTeamPersonDTO);
                }
                contractOrderVO.setProjectPersonDatas(projectPersonDatas);
            }
        }

        //注意：基本信息的设置 在后面处理


        //2.根据合同id查询人员分成信息
        log.info("2-查询人员分成信息");
        ContractPersonPercentQueryListDTO contractPersonPercentQueryListDTO = new ContractPersonPercentQueryListDTO();
        contractPersonPercentQueryListDTO.setContractId(id);

        List<ContractPersonPercentVO> contractPersonPercentVOS = contractPersonPercentService.queryList(contractPersonPercentQueryListDTO);
        if (CollUtil.isNotEmpty(contractPersonPercentVOS)) {
            contractOrderAllLinkInfoVO.setPersonPercents(contractPersonPercentVOS);
        }

        //3.根据合同id查询维保服务
        log.info("3-查询维保服务");
        ContractSupportServiceQueryListDTO contractSupportServiceQueryListDTO = new ContractSupportServiceQueryListDTO();
        contractSupportServiceQueryListDTO.setContractId(id);
        List<ContractSupportServiceVO> contractSupportServiceVOS = contractSupportServiceService.queryList(contractSupportServiceQueryListDTO);
        if (CollUtil.isNotEmpty(contractSupportServiceVOS)) {
            contractOrderAllLinkInfoVO.setSupportServices(contractSupportServiceVOS);
        }

        //4.根据合同id查询付款条件
        log.info("4-查询付款条件");
        ContractPaymentConditionQueryListDTO contractPaymentConditionQueryListDTO = new ContractPaymentConditionQueryListDTO();
        contractPaymentConditionQueryListDTO.setContractId(id);
        List<ContractPaymentConditionVO> contractPaymentConditionVOs = contractPaymentConditionService.queryList(contractPaymentConditionQueryListDTO);
        if (CollUtil.isNotEmpty(contractPaymentConditionVOs)) {
            contractOrderAllLinkInfoVO.setPaymentConditions(contractPaymentConditionVOs);
        }

        //5.根据合同id查询付款计划
        log.info("5-查询付款计划");
        ContractCollectPaymentPlanQueryListDTO contractCollectPaymentPlanQueryListDTO = new ContractCollectPaymentPlanQueryListDTO();
        contractCollectPaymentPlanQueryListDTO.setContractId(id);

        //合同回款总金额
        BigDecimal contractSumBigDecimal = BigDecimal.ZERO;

        List<ContractCollectPaymentPlanVO> contractCollectPaymentPlanVOS = contractCollectPaymentPlanService.queryList(contractCollectPaymentPlanQueryListDTO);
        if (CollUtil.isNotEmpty(contractCollectPaymentPlanVOS)) {

            for (ContractCollectPaymentPlanVO contractCollectPaymentPlanVO : contractCollectPaymentPlanVOS) {
                /**处理回款单里面的回款计划信息，将钱设置到胡款计划里面**/
                //就一个已收总额
                //各个回款计划初始化金额
                BigDecimal sumBigDecimal = BigDecimal.ZERO;

                PaymentReceiptOrderPlanQueryDTO paymentReceiptOrderPlanQueryDTO = new PaymentReceiptOrderPlanQueryDTO();
                paymentReceiptOrderPlanQueryDTO.setPaymentPlanCode(contractCollectPaymentPlanVO.getPaymentPlanCode());
                List<PaymentReceiptOrderPlanVO> paymentReceiptOrderPlanVOS = paymentReceiptOrderPlanService.queryList(paymentReceiptOrderPlanQueryDTO);
                if (CollUtil.isNotEmpty(paymentReceiptOrderPlanVOS)) {
                    //计算回款单里的回款计划的钱信息
                    for (PaymentReceiptOrderPlanVO paymentReceiptOrderPlanVO : paymentReceiptOrderPlanVOS) {
                        //根据此关系查询初具体的回款计划的详情信息
                        BigDecimal paymentCount = paymentReceiptOrderPlanVO.getPaymentCount();
                        if (Objects.nonNull(paymentCount)) {
                            //求和
                            sumBigDecimal = sumBigDecimal.add(paymentCount);
                            contractSumBigDecimal = contractSumBigDecimal.add(paymentCount);
                        }
                    }
                }

                //设置金额   已收金额
                contractCollectPaymentPlanVO.setPaymentSum(sumBigDecimal);
            }
            contractOrderAllLinkInfoVO.setPaymentPlans(contractCollectPaymentPlanVOS);
        }

        //设置合同回款总金额和合同应收余额
        contractOrderVO.setContractPaymentCount(contractSumBigDecimal);
        BigDecimal subtract = contractSum.subtract(contractSumBigDecimal);
        contractOrderVO.setContractNoPaymentCount(subtract);

        //设置合同的已发货金额和未发货金额
        updateContractDeliverCount(contractOrderVO);

        contractOrderAllLinkInfoVO.setBaseData(contractOrderVO);

        //6.根据合同id查询关联附件
        log.info("6-查询关联附件");
        List<ContractAttachmentVO> attachBussList = new ArrayList<>();
        AttachmentIdRelationQueryListDTO attachmentIdRelationQueryListDTO = new AttachmentIdRelationQueryListDTO();
        attachmentIdRelationQueryListDTO.setCurrBussId(id);

        List<AttachmentRelationServiceVO> attachmentRelationServiceVOS = attachmentIdRelationService.queryList(attachmentIdRelationQueryListDTO);
        if (CollUtil.isNotEmpty(attachmentRelationServiceVOS)) {
            for (AttachmentRelationServiceVO attachmentRelationService : attachmentRelationServiceVOS) {
                Attachment attachment = attachmentService.selectById(Integer.parseInt(attachmentRelationService.getAttachBussId()));
                if (Objects.nonNull(attachment)) {
                    ContractAttachmentVO contractAttachmentVO = new ContractAttachmentVO();
                    contractAttachmentVO.setId(attachment.getId());
                    contractAttachmentVO.setFileName(attachment.getFileName());
                    attachBussList.add(contractAttachmentVO);
                }
            }
            contractOrderAllLinkInfoVO.setAttachs(attachBussList);
        }

        //7.根据合同id查询关联产品信息
        log.info("7-查询关联产品信息");
        ContractProductQueryListDTO contractProductQueryListDTO = new ContractProductQueryListDTO();
        contractProductQueryListDTO.setContractId(id);
        List<ContractProductDataVO> internalContractServiceVOS = contractProductDataService.queryList(contractProductQueryListDTO);
        if (CollUtil.isNotEmpty(internalContractServiceVOS)) {
            for (ContractProductDataVO productData : internalContractServiceVOS) {
                ProductDataInfoVO resp = productManageService.searchByUnineCode(productData.getUnineCode());
                if (Objects.isNull(resp)) {
                    continue;
                }

                BeanUtils.copyProperties(resp, productData);


                //获取生产批号和序列号
                ProductSerialNumBatchDTO productSerialNumBatchDTO = paymentReceiptOrderProductSerialService.queryProductSerialNumBatch(RpcAuthConstant.CONTRACT_ORDER, contractOrderPO.getContractNum(), productData.getUnineCode());
                productData.setProductNo(productSerialNumBatchDTO.getProductionSerialNumber());
                productData.setProduceNo(productSerialNumBatchDTO.getProductionBatchNumber());

            }

            contractOrderAllLinkInfoVO.setProductDatas(internalContractServiceVOS);
        }

        return contractOrderAllLinkInfoVO;
    }

    /**
     * @Description: 根据id或编码查询详情
     * @Param: [contractOrderQueryDTO]
     * @return: com.swxa.prp.business.contract.vo.ContractOrderAllLinkInfoVO
     * @Author: lwei
     * @Date: 2025/9/4
     */
    @Override
    public ContractOrderAllLinkInfoVO queryByIdCode(ContractOrderQueryDTO contractOrderQueryDTO) {
        ContractOrderAllLinkInfoVO contractOrderAllLinkInfoVO = new ContractOrderAllLinkInfoVO();
        String id = contractOrderQueryDTO.getId();
        String num = contractOrderQueryDTO.getContractNum();
        if (StringUtils.isBlank(id) && StringUtils.isBlank(num)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_ID_NULL);
        }

        ContractOrderQueryListDTO contractOrderQueryListDTO = new ContractOrderQueryListDTO();
        BeanUtils.copyProperties(contractOrderQueryDTO, contractOrderQueryListDTO);

        List<ContractOrderPO> contractOrderPOS = contractOrderMapper.selectlists(contractOrderQueryListDTO);
        if (CollUtil.isEmpty(contractOrderPOS)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_ORDER_NULL);
        }

        //1. 查询合同是不是真的存在
        //log.info("1-查询基本信息");
        ContractOrderPO contractOrderPO = contractOrderPOS.get(0);
        if (Objects.isNull(contractOrderPO)) {
            //合同订单不存在
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_ORDER_NULL);
        }

        id = contractOrderPO.getId();

        //处理合同总金额 如果不存在设置为0
        BigDecimal contractSum = contractOrderPO.getContractSum();
        if (Objects.isNull(contractSum)) {
            contractSum = BigDecimal.ZERO;
            contractOrderPO.setContractSum(contractSum);
        }


        ContractOrderVO contractOrderVO = new ContractOrderVO();

        BeanUtils.copyProperties(contractOrderPO, contractOrderVO);


        //处理团队人员
        String teamIds = contractOrderPO.getTeamPersonIds();
        String teamNames = contractOrderPO.getTeamPersonNames();
        if (StringUtils.isNotBlank(teamIds) && StringUtils.isNotBlank(teamNames)) {
            String[] ids = teamIds.trim().split(",");
            String[] names = teamNames.trim().split(",");
            if (ids.length == names.length) {
                List<FollowProjectOpportunityTeamPersonDTO> projectPersonDatas = new ArrayList<>();

                for (Integer i = 0; i < ids.length; i++) {
                    FollowProjectOpportunityTeamPersonDTO followProjectOpportunityTeamPersonDTO = new FollowProjectOpportunityTeamPersonDTO();
                    followProjectOpportunityTeamPersonDTO.setId(ids[i]);
                    followProjectOpportunityTeamPersonDTO.setNickname(names[i]);
                    projectPersonDatas.add(followProjectOpportunityTeamPersonDTO);
                }
                contractOrderVO.setProjectPersonDatas(projectPersonDatas);
            }
        }

        //注意：基本信息的设置 在后面处理


        //2.根据合同id查询人员分成信息
        log.info("2-查询人员分成信息");
        ContractPersonPercentQueryListDTO contractPersonPercentQueryListDTO = new ContractPersonPercentQueryListDTO();
        contractPersonPercentQueryListDTO.setContractId(id);

        List<ContractPersonPercentVO> contractPersonPercentVOS = contractPersonPercentService.queryList(contractPersonPercentQueryListDTO);
        if (CollUtil.isNotEmpty(contractPersonPercentVOS)) {
            contractOrderAllLinkInfoVO.setPersonPercents(contractPersonPercentVOS);
        }

        //3.根据合同id查询维保服务
        log.info("3-查询维保服务");
        ContractSupportServiceQueryListDTO contractSupportServiceQueryListDTO = new ContractSupportServiceQueryListDTO();
        contractSupportServiceQueryListDTO.setContractId(id);
        List<ContractSupportServiceVO> contractSupportServiceVOS = contractSupportServiceService.queryList(contractSupportServiceQueryListDTO);
        if (CollUtil.isNotEmpty(contractSupportServiceVOS)) {
            contractOrderAllLinkInfoVO.setSupportServices(contractSupportServiceVOS);
        }

        //4.根据合同id查询付款条件
        log.info("4-查询付款条件");
        ContractPaymentConditionQueryListDTO contractPaymentConditionQueryListDTO = new ContractPaymentConditionQueryListDTO();
        contractPaymentConditionQueryListDTO.setContractId(id);
        List<ContractPaymentConditionVO> contractPaymentConditionVOs = contractPaymentConditionService.queryList(contractPaymentConditionQueryListDTO);
        if (CollUtil.isNotEmpty(contractPaymentConditionVOs)) {
            contractOrderAllLinkInfoVO.setPaymentConditions(contractPaymentConditionVOs);
        }

        //5.根据合同id查询付款计划
        log.info("5-查询付款计划");
        ContractCollectPaymentPlanQueryListDTO contractCollectPaymentPlanQueryListDTO = new ContractCollectPaymentPlanQueryListDTO();
        contractCollectPaymentPlanQueryListDTO.setContractId(id);

        //合同回款总金额
        BigDecimal contractSumBigDecimal = BigDecimal.ZERO;

        List<ContractCollectPaymentPlanVO> contractCollectPaymentPlanVOS = contractCollectPaymentPlanService.queryList(contractCollectPaymentPlanQueryListDTO);
        if (CollUtil.isNotEmpty(contractCollectPaymentPlanVOS)) {

            for (ContractCollectPaymentPlanVO contractCollectPaymentPlanVO : contractCollectPaymentPlanVOS) {
                /**处理回款单里面的回款计划信息，将钱设置到胡款计划里面**/
                //就一个已收总额
                //各个回款计划初始化金额
                BigDecimal sumBigDecimal = BigDecimal.ZERO;

                PaymentReceiptOrderPlanQueryDTO paymentReceiptOrderPlanQueryDTO = new PaymentReceiptOrderPlanQueryDTO();
                paymentReceiptOrderPlanQueryDTO.setPaymentPlanCode(contractCollectPaymentPlanVO.getPaymentPlanCode());
                List<PaymentReceiptOrderPlanVO> paymentReceiptOrderPlanVOS = paymentReceiptOrderPlanService.queryList(paymentReceiptOrderPlanQueryDTO);
                if (CollUtil.isNotEmpty(paymentReceiptOrderPlanVOS)) {
                    //计算回款单里的回款计划的钱信息
                    for (PaymentReceiptOrderPlanVO paymentReceiptOrderPlanVO : paymentReceiptOrderPlanVOS) {
                        //根据此关系查询初具体的回款计划的详情信息
                        BigDecimal paymentCount = paymentReceiptOrderPlanVO.getPaymentCount();
                        if (Objects.nonNull(paymentCount)) {
                            //求和
                            sumBigDecimal = sumBigDecimal.add(paymentCount);
                            contractSumBigDecimal = contractSumBigDecimal.add(paymentCount);
                        }
                    }
                }

                //设置金额   已收金额
                contractCollectPaymentPlanVO.setPaymentSum(sumBigDecimal);
            }
            contractOrderAllLinkInfoVO.setPaymentPlans(contractCollectPaymentPlanVOS);
        }

        //设置合同回款总金额和合同应收余额
        contractOrderVO.setContractPaymentCount(contractSumBigDecimal);
        BigDecimal subtract = contractSum.subtract(contractSumBigDecimal);
        contractOrderVO.setContractNoPaymentCount(subtract);

        //设置合同的已发货金额和未发货金额
        updateContractDeliverCount(contractOrderVO);

        contractOrderAllLinkInfoVO.setBaseData(contractOrderVO);

        //6.根据合同id查询关联附件
        log.info("6-查询关联附件");
        List<ContractAttachmentVO> attachBussList = new ArrayList<>();
        AttachmentIdRelationQueryListDTO attachmentIdRelationQueryListDTO = new AttachmentIdRelationQueryListDTO();
        attachmentIdRelationQueryListDTO.setCurrBussId(id);

        List<AttachmentRelationServiceVO> attachmentRelationServiceVOS = attachmentIdRelationService.queryList(attachmentIdRelationQueryListDTO);
        if (CollUtil.isNotEmpty(attachmentRelationServiceVOS)) {
            for (AttachmentRelationServiceVO attachmentRelationService : attachmentRelationServiceVOS) {
                Attachment attachment = attachmentService.selectById(Integer.parseInt(attachmentRelationService.getAttachBussId()));
                if (Objects.nonNull(attachment)) {
                    ContractAttachmentVO contractAttachmentVO = new ContractAttachmentVO();
                    contractAttachmentVO.setId(attachment.getId());
                    contractAttachmentVO.setFileName(attachment.getFileName());
                    attachBussList.add(contractAttachmentVO);
                }
            }
            contractOrderAllLinkInfoVO.setAttachs(attachBussList);
        }

        //7.根据合同id查询关联产品信息
        log.info("7-查询关联产品信息");
        ContractProductQueryListDTO contractProductQueryListDTO = new ContractProductQueryListDTO();
        contractProductQueryListDTO.setContractId(id);
        List<ContractProductDataVO> internalContractServiceVOS = contractProductDataService.queryList(contractProductQueryListDTO);
        if (CollUtil.isNotEmpty(internalContractServiceVOS)) {
            for (ContractProductDataVO productData : internalContractServiceVOS) {
                ProductDataInfoVO resp = productManageService.searchByUnineCode(productData.getUnineCode());
                if (Objects.isNull(resp)) {
                    continue;
                }

                BeanUtils.copyProperties(resp, productData);


                //获取生产批号和序列号
                ProductSerialNumBatchDTO productSerialNumBatchDTO = paymentReceiptOrderProductSerialService.queryProductSerialNumBatch(RpcAuthConstant.CONTRACT_ORDER, contractOrderPO.getContractNum(), productData.getUnineCode());
                productData.setProductNo(productSerialNumBatchDTO.getProductionSerialNumber());
                productData.setProduceNo(productSerialNumBatchDTO.getProductionBatchNumber());

            }

            contractOrderAllLinkInfoVO.setProductDatas(internalContractServiceVOS);
        }

        return contractOrderAllLinkInfoVO;
    }

    /**
     * @Description: 分页查询，基本信息+产品信息
     * @Param: [contractOrderQueryPageDTO]
     * @return: com.swxa.prp.util.TableResultUtil
     * @Author: lwei
     * @Date: 2025/8/5
     */
    @Override
    public TableResultUtil queryBaseAndProdToPage(ContractOrderQueryPageDTO contractOrderQueryPageDTO) {


        //追加数据授权条件
        List<String> bussIds = dataAuthService.getBussIds(CrmBussTypeEnum.contractOrder.getCode());


        log.info("queryToPage param={}", JSONObject.toJSONString(contractOrderQueryPageDTO));
        // 分页
        Page<Object> page = PageHelper.startPage(contractOrderQueryPageDTO.getPageNum(), contractOrderQueryPageDTO.getPageSize());
        // todo -查询条件待定，拼接查询条件
        QueryWrapper<ContractOrderPO> queryWrapper = Wrappers.query();
        if (StringUtils.isNotEmpty(contractOrderQueryPageDTO.getSubjectSelectNameLike())) {
            queryWrapper.lambda().like(ContractOrderPO::getSubjectSelectName, contractOrderQueryPageDTO.getSubjectSelectNameLike());
        }

        if (StringUtils.isNotEmpty(contractOrderQueryPageDTO.getCustomerId())) {
            queryWrapper.lambda().eq(ContractOrderPO::getCustomerId, contractOrderQueryPageDTO.getCustomerId());
        }
        if (StringUtils.isNotEmpty(contractOrderQueryPageDTO.getCustomerIdLike())) {
            queryWrapper.lambda().like(ContractOrderPO::getCustomerId, contractOrderQueryPageDTO.getCustomerIdLike());
        }

        if (StringUtils.isNotEmpty(contractOrderQueryPageDTO.getCustomerNameLike())) {
            queryWrapper.lambda().like(ContractOrderPO::getCustomerName, contractOrderQueryPageDTO.getCustomerNameLike());
        }
        if (StringUtils.isNotEmpty(contractOrderQueryPageDTO.getContractNumLike())) {
            queryWrapper.lambda().like(ContractOrderPO::getContractNum, contractOrderQueryPageDTO.getContractNumLike());
        }
        if (CollUtil.isNotEmpty(contractOrderQueryPageDTO.getAdvanceQueryGroups())) {
            String sql = SqlToolsUtil.constructFields(contractOrderQueryPageDTO.getAdvanceQueryGroups());
            if (StringUtils.isNotBlank(sql)) {
                log.info("sqlWhere = {}", sql);
                queryWrapper.apply(sql);
            }
        }

        // 按更新时间倒序查询
        queryWrapper.lambda().orderByDesc(ContractOrderPO::getUpdateTime);


        if (CollUtil.isNotEmpty(bussIds)) {
            queryWrapper.lambda().in(ContractOrderPO::getId, bussIds);
        }

        List<ContractOrderPO> contractOrderPOS = contractOrderMapper.selectList(queryWrapper);

        //返回的结果
        List<ContractOrderAndProductsVO> result = new ArrayList<>();
        for (ContractOrderPO contractOrder : contractOrderPOS) {

            //处理合同总额，如果不存在直接设置为0
            BigDecimal contractSum = contractOrder.getContractSum();
            if (Objects.isNull(contractSum)) {
                contractSum = BigDecimal.ZERO;
            }
            contractOrder.setContractSum(contractSum);

            //赋值合同属性到返回结果里面
            ContractOrderAndProductsVO resp = new ContractOrderAndProductsVO();
            BeanUtils.copyProperties(contractOrder, resp);

            //设置合同的已发货金额和未发货金额
            updateContractDeliverCount(resp);

            //还要查询产品信息
            //根据合同id查询关联产品信息
            log.info("查询关联产品信息");
            ContractProductQueryListDTO contractProductQueryListDTO = new ContractProductQueryListDTO();
            contractProductQueryListDTO.setContractId(contractOrder.getId());
            List<ContractProductDataVO> internalContractServiceVOS = contractProductDataService.queryList(contractProductQueryListDTO);
            if (CollUtil.isNotEmpty(internalContractServiceVOS)) {
                //根据产品编码（U9编码）查询对应的产品信息
                //int i = 0;
                for (ContractProductDataVO productData : internalContractServiceVOS) {
                    ProductDataInfoVO productDataInfo = productManageService.searchByUnineCode(productData.getUnineCode());
                    if (Objects.isNull(productDataInfo)) {
                        continue;
                    }
                    BeanUtils.copyProperties(productDataInfo, productData);

                    //获取生产批号和序列号
                    ProductSerialNumBatchDTO productSerialNumBatchDTO = paymentReceiptOrderProductSerialService.queryProductSerialNumBatch(RpcAuthConstant.CONTRACT_ORDER, resp.getContractNum(), productData.getUnineCode());
                    productData.setProductNo(productSerialNumBatchDTO.getProductionSerialNumber());
                    productData.setProduceNo(productSerialNumBatchDTO.getProductionBatchNumber());

                    //internalContractServiceVOS.set(i, productData);
                }

                resp.setProductDatas(internalContractServiceVOS);
            }

            //回款计划
            ContractCollectPaymentPlanQueryListDTO contractCollectPaymentPlanQueryListDTO = new ContractCollectPaymentPlanQueryListDTO();
            contractCollectPaymentPlanQueryListDTO.setContractId(contractOrder.getId());
            List<ContractCollectPaymentPlanVO> contractCollectPaymentPlanVOS = contractCollectPaymentPlanService.queryList(contractCollectPaymentPlanQueryListDTO);
            resp.setPaymentPlans(contractCollectPaymentPlanVOS);

            /**处理回款单里面的回款计划信息**/
            //处理回款单里面的回款计划信息
            //合同回款总金额
            BigDecimal contractSumBigDecimal = BigDecimal.ZERO;

            if (CollUtil.isNotEmpty(contractCollectPaymentPlanVOS)) {
                //返回回款计划的明细信息
                List<PaymentReceiptCollectPlanDetailVO> paymentReceiptCollectPlanDetailVOS = new ArrayList<>();
                for (ContractCollectPaymentPlanVO contractCollectPaymentPlanVO : contractCollectPaymentPlanVOS) {
                    //赋值合同信息和回款计划部分信息
                    PaymentReceiptCollectPlanDetailVO paymentReceiptCollectPlanDetailVO = new PaymentReceiptCollectPlanDetailVO();
                    BeanUtils.copyProperties(contractCollectPaymentPlanVO, paymentReceiptCollectPlanDetailVO);
                    //设置回款单编码
                    paymentReceiptCollectPlanDetailVO.setSourceOrderNo(contractCollectPaymentPlanVO.getPaymentPlanCode());

                    //根据回款编码查询回款单回款计划信息
                    String paymentPlanCode = contractCollectPaymentPlanVO.getPaymentPlanCode();

                    //各个回款计划初始化金额
                    BigDecimal sumBigDecimal = BigDecimal.ZERO;

                    if (StringUtils.isNotBlank(paymentPlanCode)) {

                        PaymentReceiptOrderPlanQueryDTO paymentReceiptOrderPlanQueryDTO = new PaymentReceiptOrderPlanQueryDTO();
                        paymentReceiptOrderPlanQueryDTO.setPaymentPlanCode(contractCollectPaymentPlanVO.getPaymentPlanCode());
                        List<PaymentReceiptOrderPlanVO> paymentReceiptOrderPlanVOS = paymentReceiptOrderPlanService.queryList(paymentReceiptOrderPlanQueryDTO);
                        if (CollUtil.isNotEmpty(paymentReceiptOrderPlanVOS)) {
                            //计算回款单里的回款计划的钱信息
                            for (PaymentReceiptOrderPlanVO paymentReceiptOrderPlanVO : paymentReceiptOrderPlanVOS) {
                                //根据此关系查询初具体的回款计划的详情信息
                                BigDecimal paymentCount = paymentReceiptOrderPlanVO.getPaymentCount();
                                if (Objects.nonNull(paymentCount)) {
                                    //求和
                                    sumBigDecimal = sumBigDecimal.add(paymentCount);

                                    //汇总合同回款总金额
                                    contractSumBigDecimal = contractSumBigDecimal.add(paymentCount);
                                }
                            }
                        }
                    }

                    //设置金额   已收金额
                    paymentReceiptCollectPlanDetailVO.setPaymentSum(sumBigDecimal);

                    //计算当前应该收取金额
                    BigDecimal paymentCount = contractCollectPaymentPlanVO.getPaymentCount();
                    if (Objects.nonNull(paymentCount) && Objects.nonNull(sumBigDecimal)) {
                        paymentReceiptCollectPlanDetailVO.setCurrentPaymentCount(paymentCount.subtract(sumBigDecimal));
                    } else {
                        paymentReceiptCollectPlanDetailVO.setCurrentPaymentCount(BigDecimal.ZERO);
                    }

                    //设置合同编码、合同金额、最新开票日期、已开票金额、 合同已回款金额（已回款金额最后赋值）
                    paymentReceiptCollectPlanDetailVO.setContractNum(contractOrder.getContractNum());
                    paymentReceiptCollectPlanDetailVO.setContractSum(contractOrder.getContractSum());
                    paymentReceiptCollectPlanDetailVO.setContractInvoiceCount(contractOrder.getContractInvoiceCount());
                    paymentReceiptCollectPlanDetailVO.setLastPaymentDate(contractOrder.getLastPaymentDate());
                    paymentReceiptCollectPlanDetailVO.setUpdateInvoiceTime(contractOrder.getUpdateInvoiceTime());

                    //赋值
                    paymentReceiptCollectPlanDetailVOS.add(paymentReceiptCollectPlanDetailVO);
                }


                //遍历回款单里面的回款计划信息，赋值合同回款总额
                for (PaymentReceiptCollectPlanDetailVO data : paymentReceiptCollectPlanDetailVOS) {
                    data.setContractPaymentCount(contractSumBigDecimal);
                }

                //赋值汇款单里面的回款计划信息
                resp.setPaymentReceiptCollectPlanDetails(paymentReceiptCollectPlanDetailVOS);

                //修改合同里面的已汇款金额和应收金额
                resp.setContractPaymentCount(contractSumBigDecimal);
                resp.setContractNoPaymentCount(contractSum.subtract(contractSumBigDecimal));
                /**处理回款单里面的回款计划信息**/
            }
            result.add(resp);
        }

        PageInfo<ContractOrderAndProductsVO> pageInfo = new PageInfo<>(result);
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        return TableResultUtil.buildTableResult(pageInfo);

    }

    private void updateContractDeliverCount(ContractOrderVO contractOrderVO) {
        String id = contractOrderVO.getId();
        if (StringUtils.isBlank(id)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_ID_NULL);
        }

        //查询产品信息
        ContractProductQueryListDTO contractProductQueryListDTO = new ContractProductQueryListDTO();
        contractProductQueryListDTO.setContractId(id);
        List<ContractProductDataVO> contractProductDataVOS = contractProductDataService.queryList(contractProductQueryListDTO);
        if (CollUtil.isNotEmpty(contractProductDataVOS)) {
            //已发货金额
            BigDecimal contractDeliverSumDecimal = BigDecimal.ZERO;
            for (ContractProductDataVO contractProductDataVO : contractProductDataVOS) {
                //计算每个产品的  已发货钱=已发货数量*含税单价
                Integer deliverNum = contractProductDataVO.getDeliverNum();
                //含税单价
                BigDecimal unitTaxPrice = contractProductDataVO.getUnitTaxPrice();
                if (Objects.nonNull(deliverNum) && Objects.nonNull(unitTaxPrice)) {
                    //只有数量和含税单价都不为空才回去计算已发货的价
                    BigDecimal multiply = unitTaxPrice.multiply(new BigDecimal(deliverNum));
                    contractDeliverSumDecimal = contractDeliverSumDecimal.add(multiply);
                }
            }
            //设置已发货金钱和未发货金钱
            contractOrderVO.setContractDeliveryCount(contractDeliverSumDecimal);
            contractOrderVO.setContractNoDeliveryCount(contractOrderVO.getContractSum().subtract(contractDeliverSumDecimal));
        }
    }


    /**
     * //1.现根据contractNum 查询初合同的信息
     * //2.根据合同id和产品编码查询产品信息
     * //3.产品数量计算
     * //    ---已发货数量=当前数据库已发货的数量+现在要发货的数量
     * //    ---未发货数量就是=产品总数量-已发货数量 (注意怕判断非负--发超了)
     * //4.更新产品信息：已发货数量、未发货数量
     * //5.更新合同里面的已发货金额和为发货金额
     */

    @Transactional
    @Override
    public synchronized void updateContractOrderDeliverNum(DealProductDeliverNumBO contractOrderUpdateDeliverNumDTO) {

        //1.现根据contractNum 查询初合同的信息
        String contractNum = contractOrderUpdateDeliverNumDTO.getSourceOrderNum();
        if (StringUtils.isBlank(contractNum)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_SOURCE_ORDER_NUM_NULL);
        }
        //2.校验更新产品信息不能为空
        List<ProductDeliverNumBO> contractOrderDeliverNumDTOS = contractOrderUpdateDeliverNumDTO.getProductInfoBOS();
        if (CollUtil.isEmpty(contractOrderDeliverNumDTOS)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_PRODUCT_INFO_NULL);
        }

        //查询合同信息
        ContractOrderQueryListDTO contractOrderQueryListDTO = new ContractOrderQueryListDTO();
        contractOrderQueryListDTO.setContractNum(contractNum);
        List<ContractOrderPO> contractOrderPOS = contractOrderMapper.selectlists(contractOrderQueryListDTO);
        if (CollUtil.isEmpty(contractOrderPOS)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_ORDER_BASE_DATA_NULL);
        }
        ContractOrderPO contractOrderPO = contractOrderPOS.get(0);

        //查询产品
        for (ProductDeliverNumBO contractOrderDeliverNumDTO : contractOrderDeliverNumDTOS) {

            String productNo = contractOrderDeliverNumDTO.getUnineCode();
            String shipmentNum = contractOrderDeliverNumDTO.getShipmentNum();
            if (StringUtils.isNotBlank(shipmentNum)) {
                try {//验证是否是整数
                    Integer.parseInt(shipmentNum);
                } catch (Exception e) {
                    //todo修改错误信息    发货数量不是整数
                    throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_PRODUCT_NOT_INT_NULL);
                }
            }

            Integer deliverNum = Integer.parseInt(contractOrderDeliverNumDTO.getShipmentNum());
            if (StringUtils.isBlank(productNo) || (Objects.isNull(deliverNum))) {
                //todo修改错误信息    产品编码和已发货数量不能为空
                throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_PRODUCT_DE_NOT_NULL);
            }

            //查询产品信息
            ContractProductQueryListDTO contractProductQueryListDTO = new ContractProductQueryListDTO();
            contractProductQueryListDTO.setContractId(contractOrderPO.getId());
            contractProductQueryListDTO.setUnineCode(productNo);
            List<ContractProductDataVO> contractProductDataVOS = contractProductDataService.queryList(contractProductQueryListDTO);
            if (CollUtil.isEmpty(contractProductDataVOS)) {
                //todo修改错误信息   合同中不存在此产品
                throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_PRODUCT_EXIT_NOT_NULL);
            }


            //合同中的产品信息
            ContractProductDataVO contractProductDataVO = contractProductDataVOS.get(0);

            //产品总数量
            Integer productNum = contractProductDataVO.getProductNum();
            if (Objects.isNull(productNum)) {
                //todo修改错误信息   合同中产品总数量不存在
                throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_PRODUCT_TOTAL_NUM_NULL);
            }


            //先计算已发货数量   =当前数据库里面已发货数量+现在的已发货数量
            //数据库里面的
            Integer dbDeliverNum = contractProductDataVO.getDeliverNum();
            if (Objects.nonNull(dbDeliverNum)) {
                deliverNum = deliverNum + dbDeliverNum;
            }


            //未发货数量 ,判断不能超出发货数量
            Integer noDeliverNum = productNum - deliverNum;
            if (noDeliverNum.compareTo(Integer.parseInt("0")) < 0) {//超出发货数量
                //todo修改错误信息   超出发货数量
                throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_PRODUCT_OVER_NUM_NULL);
            }

            //更新产品信息
            ContractProductDataUpdateDTO contractProductDataUpdateDTO = new ContractProductDataUpdateDTO();
            contractProductDataUpdateDTO.setDeliverNum(deliverNum);
            contractProductDataUpdateDTO.setNoDeliverNum(noDeliverNum);
            contractProductDataUpdateDTO.setId(contractProductDataVO.getId());
            contractProductDataService.updateById(contractProductDataUpdateDTO);

        }

    }

    /**
     * @Description: 更新开票信息
     * @Param: [updateInvoiceInfo]
     * @return: void
     * @Author: lwei
     * @Date: 2025/8/21
     */

    @Override
    public void updateContractInvoice(ContractOrderUpdateInvoiceInfoDTO updateInvoiceInfo) {
        String contranctNum = updateInvoiceInfo.getContranctNum();
        if (StringUtils.isBlank(contranctNum)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_ORDER_ADD_ERROR);
        }

        BigDecimal decimal = updateInvoiceInfo.getCurrentInvoiceCount();
        if (Objects.isNull(decimal)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_ORDER_ADD_ERROR);
        }

        ContractOrderQueryListDTO contractOrderQueryListDTO = new ContractOrderQueryListDTO();
        contractOrderQueryListDTO.setContractNum(contranctNum);

        List<ContractOrderPO> contractOrderPOList = contractOrderMapper.selectlists(contractOrderQueryListDTO);
        if (CollUtil.isEmpty(contractOrderPOList)) {
            //没有找到合同单号
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_BUSS_INTERNAL_CONTRACT_NOT_EXISTS_ERROR);
        }
        ContractOrderPO contractOrderPO = contractOrderPOList.get(0);
        //更新开票金额 ， 开票金额计算逻辑， 开票金额 = 已开票金额 + 本次开票金额
        if (Objects.isNull(contractOrderPO.getContractInvoiceCount())) {
            //若没有值，直接赋值
            contractOrderPO.setContractInvoiceCount(decimal);
        } else {
            //若有值，先增加合计，再赋值
            BigDecimal sum = decimal.add(contractOrderPO.getContractInvoiceCount());
            contractOrderPO.setContractInvoiceCount(sum);
        }

        //更新开票张数 ， 累加
        if (Objects.isNull(contractOrderPO.getInvoiceNum())) {
            contractOrderPO.setInvoiceNum(updateInvoiceInfo.getInvoiceNum());
        } else {
            Integer invoiceNumSum = contractOrderPO.getInvoiceNum() + updateInvoiceInfo.getInvoiceNum();
            contractOrderPO.setInvoiceNum(invoiceNumSum);
        }

        //更新开票时间
        contractOrderPO.setUpdateInvoiceTime(updateInvoiceInfo.getInvoiceDate());
        contractOrderPO.setUpdateTime(new Date());

        //更新值
        contractOrderMapper.updateByPrimaryKeySelective(contractOrderPO);

    }


    /**
     * @Description: 根据负责人id列表查询对应的记录id列表
     * @Param: [saleUserId]
     * @return: java.util.List<java.lang.String>
     * @Author: lwei
     * @Date: 2025/9/18
     */
    @Override
    public List<String> findBySaleUserIds(List<String> saleUserId) {

        if (CollUtil.isNotEmpty(saleUserId)) {
            List<String> ids = contractOrderMapper.selectListBySaleUserId(saleUserId);
            if (CollUtil.isNotEmpty(ids)) {
                return ids;
            }
        }
        return null;
    }

    /**
     * @Description: 根据创建者id列表，查询对应的数据id列表
     * @Param: [createId]
     * @return: java.util.List<java.lang.String>
     * @Author: lwei
     * @Date: 2025/9/18
     */
    @Override
    public List<String> findByCreateIds(List<String> createId) {
        if (CollUtil.isNotEmpty(createId)) {
            List<String> ids = contractOrderMapper.selectListByCreateId(createId);
            if (CollUtil.isNotEmpty(ids)) {
                return ids;
            }
        }
        return null;
    }

    /**
     * @Description: 根据id，更新审批状态
     * @Param: [processStatusDTO]
     * @return: void
     * @Author: lwei
     * @Date: 2025/9/19
     */
    @Override
    public void updateProcessStatusById(BussProcessStatusDTO processStatusDTO) {
        String id = processStatusDTO.getId();
        if (StringUtils.isNotBlank(id)) {
            ContractOrderPO bussContactInfo = new ContractOrderPO();
            bussContactInfo.setId(id);
            bussContactInfo.setBussProcessStatus(processStatusDTO.getBussProcessStatus());

            contractOrderMapper.updateByPrimaryKeySelective(bussContactInfo);
        }
    }

    /**
     * 根据合同订单编号获取合同订单的U9编码
     * @param contractOrderCode
     * @return
     */
    @Override
    public String getUnineCodeByContractOrderCode(String contractOrderCode){
        LambdaQueryWrapper<ContractOrderPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContractOrderPO::getContractNum, contractOrderCode);
        ContractOrderPO contractOrderPO = contractOrderMapper.selectOne(queryWrapper);
        return contractOrderPO.getUnineSoCode();
    }



    /**
     * @Description: 添加合同单到u9
     * @Param: [contractOrderId]
     * @return: void
     * @Author: xhy
     * @Date: 2025/9/19
     */
    @Override
    public void addContractOrder2Unine(String contractOrderId) {

        ContractOrderPO contractOrderPO = contractOrderMapper.selectById(contractOrderId);

        ContractOrderAdd2UnineDTO contractOrderAdd2UnineDTO = new ContractOrderAdd2UnineDTO();

        contractOrderAdd2UnineDTO.setDoc("so");
        contractOrderAdd2UnineDTO.setAction("add");

        ContractOrderAdd2UnineDTO.Context context = new ContractOrderAdd2UnineDTO.Context();
        context.setUserCode("admin");
        context.setEntCode("02");
        context.setOrgCode(contractOrderPO.getSubjectSelectName());

        contractOrderAdd2UnineDTO.setContext(context);

        ContractOrderAdd2UnineDataDTO cAdd2UnineDataDTO = new ContractOrderAdd2UnineDataDTO();

        cAdd2UnineDataDTO.setDocNo("");
        cAdd2UnineDataDTO.setOrderBy(contractOrderPO.getCustomerCode());
        cAdd2UnineDataDTO.setBusinessDate(contractOrderPO.getArchiveDate());
        cAdd2UnineDataDTO.setDept("");
        cAdd2UnineDataDTO.setCurrency("C001");
        cAdd2UnineDataDTO.setIsPriceIncludeTax("true");
        cAdd2UnineDataDTO.setMemo("");

        //获取销售负责人工号
        QueryUserByIdNameDTO queryUserByIdNameDTO = new QueryUserByIdNameDTO();
        queryUserByIdNameDTO.setId(Long.parseLong(contractOrderPO.getSalesManagerId()));
        SysUserVO sysUserVO = userInfoService.queryUserByIdName(queryUserByIdNameDTO);
        cAdd2UnineDataDTO.setSeller(sysUserVO.getEmployeeId());





    }

}
