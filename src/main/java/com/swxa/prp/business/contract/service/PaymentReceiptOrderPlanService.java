package com.swxa.prp.business.contract.service;

import com.swxa.prp.business.contract.dto.PaymentReceiptOrderPlanDeleteDTO;
import com.swxa.prp.business.contract.dto.PaymentReceiptOrderPlanInsertDTO;
import com.swxa.prp.business.contract.dto.PaymentReceiptOrderPlanQueryDTO;
import com.swxa.prp.business.contract.dto.PaymentReceiptOrderPlanUpdateDTO;
import com.swxa.prp.business.contract.vo.PaymentReceiptOrderPlanVO;

import java.util.List;

public interface PaymentReceiptOrderPlanService {


    /**
     * @Description: 添加
     * @Param: insertDTO
     * @return:
     * @Author:
     * @Date:
     */
    void insertData(PaymentReceiptOrderPlanInsertDTO insertDTO);


    /**
     * @Description:删除
     * @Param: [id]
     * @return:
     * @Author:
     * @Date:
     */
    void deleteByID(PaymentReceiptOrderPlanDeleteDTO deleteDTO);

    /**
     * @Description: 更新
     * @Param
     * @return:
     * @Date:
     */

    void updateByID(PaymentReceiptOrderPlanUpdateDTO updateDTO);

    /**
     * @Description: 列表查询
     * @Param:
     * @return:
     * @Date:
     */

    List<PaymentReceiptOrderPlanVO> queryList(PaymentReceiptOrderPlanQueryDTO queryDTO);

    /**
     * @Description: 通过回款单编号删除列表信息
     * @Param: [receiptNo]
     * @return: void
     * @Author: zhangweicheng
     * @Date: 2025/8/22
     */

    void deleteByReceiptOrderNo(String receiptNo);
}
