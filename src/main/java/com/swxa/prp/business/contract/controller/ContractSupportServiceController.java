package com.swxa.prp.business.contract.controller;

import com.swxa.prp.business.contract.dto.*;
import com.swxa.prp.business.contract.service.ContractSupportServiceService;
import com.swxa.prp.business.contract.vo.ContractSupportServiceVO;
import com.swxa.prp.util.ResponseData;
import com.swxa.prp.util.TableResultUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * @Description: 合同订单维保服务控制层
 * @Author: zhangweicheng
 * @Date: 2025/7/18
 */

@Controller
@RequestMapping("/contract/support/service")
public class ContractSupportServiceController {


    @Autowired
    private ContractSupportServiceService contractSupportServiceService;

    private static final Logger log = LoggerFactory.getLogger("dayLogger");


    /**
     * @Description: 新增
     * @Param: [contractSupportServiceAddDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/19
     */


    @ResponseBody
    @PostMapping(value = "/insert")
    public ResponseData insert(@Validated @RequestBody ContractSupportServiceAddDTO contractSupportServiceAddDTO) {

        log.info("执行新增操作");

        contractSupportServiceService.insert(contractSupportServiceAddDTO);

        return ResponseData.ok();
    }

    /**
     * @Description: 列表查询
     * @Param: [contractSupportServiceQueryListDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/19
     */


    @ResponseBody
    @PostMapping(value = "/queryList")
    public ResponseData queryList(@Validated @RequestBody ContractSupportServiceQueryListDTO contractSupportServiceQueryListDTO) {

        List<ContractSupportServiceVO> contractSupportServiceVOS = contractSupportServiceService.queryList(contractSupportServiceQueryListDTO);

        return ResponseData.ok(true, contractSupportServiceVOS);
    }


    /**
     * @Description: 分页查询
     * @Param: [contractSupportServiceQueryPageDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/19
     */


    @ResponseBody
    @PostMapping(value = "/queryToPage")
    public ResponseData queryToPage(@Validated @RequestBody ContractSupportServiceQueryPageDTO contractSupportServiceQueryPageDTO) {

        TableResultUtil resultUtil = contractSupportServiceService.queryToPage(contractSupportServiceQueryPageDTO);

        return ResponseData.ok(true, resultUtil);
    }


    /**
     * @Description: 根据ID删除关系
     * @Param: [contractSupportServiceDeleteDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/19
     */


    @ResponseBody
    @PostMapping(value = "/deleteById")
    public ResponseData deletePersonById(@Validated @RequestBody ContractSupportServiceDeleteDTO contractSupportServiceDeleteDTO) {
        contractSupportServiceService.deleteById(contractSupportServiceDeleteDTO);
        return ResponseData.ok();
    }


    /**
     * @Description: 更新
     * @Param: [contractSupportServiceEditDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/19
     */


    @ResponseBody
    @PostMapping(value = "/updateById")
    public ResponseData updateById(@Validated @RequestBody ContractSupportServiceEditDTO contractSupportServiceEditDTO) {

        log.info("更新");
        contractSupportServiceService.updateById(contractSupportServiceEditDTO);
        return ResponseData.ok();
    }


}
