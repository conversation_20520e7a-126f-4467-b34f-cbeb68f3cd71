package com.swxa.prp.business.contract.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.swxa.prp.business.contract.dao.ContractPaymentConditionMapper;
import com.swxa.prp.business.contract.dto.*;
import com.swxa.prp.business.contract.model.ContractPaymentConditionPO;
import com.swxa.prp.business.contract.service.ContractPaymentConditionService;
import com.swxa.prp.business.contract.vo.ContractPaymentConditionVO;
import com.swxa.prp.constant.SwErrorCodeConstant;
import com.swxa.prp.exception.SwPrpException;
import com.swxa.prp.util.MyUtil;
import com.swxa.prp.util.TableResultUtil;
import com.swxa.prp.util.UserUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * @Description: 合同订单付款条件业务逻辑实现
 * @Author: zhangweicheng
 * @Date: 2025/7/19
 */

@Service
public class ContractPaymentConditionServiceImpl implements ContractPaymentConditionService {

    @Autowired
    private ContractPaymentConditionMapper contractPaymentConditionMapper;
    private static final Logger log = LoggerFactory.getLogger("dayLogger");


    @Transactional
    @Override
    public void insert(ContractPaymentConditionAddDTO addContractPaymentCondition) {
        log.info("ContractPaymentConditioServiceImpl.addContractPaymentConditio {}", addContractPaymentCondition);

        ContractPaymentConditionPO contractPaymentConditionPO = new ContractPaymentConditionPO();

        //赋值
        BeanUtils.copyProperties(addContractPaymentCondition, contractPaymentConditionPO);

        contractPaymentConditionPO.setId(MyUtil.getRandomID());
        contractPaymentConditionPO.setCreateId(UserUtil.getLoginUserId());
        contractPaymentConditionPO.setCreateTime(new Date());
        contractPaymentConditionPO.setUpdateTime(new Date());

        contractPaymentConditionMapper.insertSelective(contractPaymentConditionPO);

    }


    @Transactional
    @Override
    public void deleteById(ContractPaymentConditionDeleteDTO deleteContractPaymentCondition) {
        log.info("ContractPaymentConditioServiceImpl.deleteContractProductById {}", deleteContractPaymentCondition);
        String id = deleteContractPaymentCondition.getId();
        if (Objects.isNull(id)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_PAYMENTCONDITION_ID_NULL);
        }

        contractPaymentConditionMapper.deleteByPrimaryKey(id);

    }


    @Transactional
    @Override
    public void updateById(ContractPaymentConditionUpdateDTO updateContractPaymentCondition) {

        log.info("ContractPaymentConditioServiceImpl.updateContractPaymentConditionById {}", updateContractPaymentCondition);

        ContractPaymentConditionPO contractPaymentConditionPO = new ContractPaymentConditionPO();

        String id = updateContractPaymentCondition.getId();
        if (Objects.isNull(id)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_PAYMENTCONDITION_ID_NULL);
        }

        //赋值
        BeanUtils.copyProperties(updateContractPaymentCondition, contractPaymentConditionPO);

        contractPaymentConditionPO.setId(id);
        //更新时间
        contractPaymentConditionPO.setUpdateTime(new Date());

        contractPaymentConditionMapper.updateByPrimaryKeySelective(contractPaymentConditionPO);
    }


    @Override
    public ContractPaymentConditionVO queryById(ContractPaymentConditionQueryDTO queryContractPaymentCondition) {
        log.info("ContractPaymentConditioServiceImpl.queryContractProductById {}", queryContractPaymentCondition);

        ContractPaymentConditionVO contractPaymentConditionVO = new ContractPaymentConditionVO();

        String id = queryContractPaymentCondition.getId();
        if (Objects.isNull(id)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_PAYMENTCONDITION_ID_NULL);
        }

        ContractPaymentConditionPO contractPaymentConditionPO = contractPaymentConditionMapper.selectByPrimaryKey(id);

        if (Objects.isNull(contractPaymentConditionPO)) {
            BeanUtils.copyProperties(contractPaymentConditionPO, contractPaymentConditionVO);
            return contractPaymentConditionVO;
        }

        return null;

    }


    @Override
    public TableResultUtil queryToPage(ContractPaymentConditionQueryPageDTO queryContractPaymentConditionToPageDTO) {

        log.info("ContractPaymentConditioServiceImpl.queryContractPaymentConditionToPage {}", queryContractPaymentConditionToPageDTO);
        // 分页
        Page<Object> page = PageHelper.startPage(queryContractPaymentConditionToPageDTO.getPageNum(), queryContractPaymentConditionToPageDTO.getPageSize());
        // todo -查询条件待定，拼接查询条件
        QueryWrapper<ContractPaymentConditionPO> queryWrapper = Wrappers.query();
        if (StringUtils.isNotEmpty(queryContractPaymentConditionToPageDTO.getStageName())) {
            queryWrapper.lambda().eq(ContractPaymentConditionPO::getStageName, queryContractPaymentConditionToPageDTO.getStageName());
        }

        // 按更新时间倒序查询
        queryWrapper.lambda().orderByDesc(ContractPaymentConditionPO::getCreateTime);
        List<ContractPaymentConditionPO> contractPaymentConditionIPage = contractPaymentConditionMapper.selectList(queryWrapper);

        List<ContractPaymentConditionVO> result = new ArrayList<>();
        for (ContractPaymentConditionPO contractPaymentCondition : contractPaymentConditionIPage) {
            ContractPaymentConditionVO resp = new ContractPaymentConditionVO();
            BeanUtils.copyProperties(contractPaymentCondition, resp);
            result.add(resp);
        }

        PageInfo<ContractPaymentConditionVO> pageInfo = new PageInfo<>(result);
        pageInfo.setTotal(page.getTotal());

        return TableResultUtil.buildTableResult(pageInfo);
    }

    @Override
    public void deleteByContractId(String contractId) {
        if (StringUtils.isBlank(contractId)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_ID_NULL);
        }
        //查询该合同的所有付款条件信息
        ContractPaymentConditionQueryListDTO contractPaymentConditionQueryListDTO = new ContractPaymentConditionQueryListDTO();
        contractPaymentConditionQueryListDTO.setContractId(contractId);
        List<ContractPaymentConditionVO> contractPaymentConditionVOS = queryList(contractPaymentConditionQueryListDTO);
        if (CollUtil.isNotEmpty(contractPaymentConditionVOS)) {
            //删除
            for (ContractPaymentConditionVO contractPaymentConditionVO : contractPaymentConditionVOS) {
                contractPaymentConditionMapper.deleteById(contractPaymentConditionVO.getId());
            }
        }
    }

    @Override
    public List<ContractPaymentConditionVO> queryList(ContractPaymentConditionQueryListDTO contractPaymentConditionQueryListDTO) {
        List<ContractPaymentConditionVO> contractPaymentConditionVOS = new ArrayList<>();

        List<ContractPaymentConditionPO> contractPaymentConditionPOS = contractPaymentConditionMapper.queryList(contractPaymentConditionQueryListDTO);
        if(CollUtil.isNotEmpty(contractPaymentConditionPOS)){
            for (ContractPaymentConditionPO contractPaymentConditionPO:
                    contractPaymentConditionPOS) {
                ContractPaymentConditionVO contractPaymentConditionVO = new ContractPaymentConditionVO();
                BeanUtils.copyProperties(contractPaymentConditionPO,contractPaymentConditionVO);
                contractPaymentConditionVOS.add(contractPaymentConditionVO);
            }
        }

        return contractPaymentConditionVOS;
    }
}
