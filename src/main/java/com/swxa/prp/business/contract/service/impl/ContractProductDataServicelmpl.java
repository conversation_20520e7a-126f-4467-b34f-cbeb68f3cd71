package com.swxa.prp.business.contract.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.ISelect;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.swxa.prp.business.contract.dao.ContractProductDataMapper;
import com.swxa.prp.business.contract.dto.*;
import com.swxa.prp.business.contract.model.ContractProductDataPO;
import com.swxa.prp.business.contract.service.ContractProductDataService;
import com.swxa.prp.business.contract.vo.ContractProductDataVO;
import com.swxa.prp.business.contract.vo.InternalContractServiceVO;
import com.swxa.prp.constant.SwErrorCodeConstant;
import com.swxa.prp.exception.SwPrpException;
import com.swxa.prp.util.MyUtil;
import com.swxa.prp.util.TableResultUtil;
import com.swxa.prp.util.UserUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @Param:
 * @return:
 * @Author: zhangweicheng
 * @Date: 2025/7/19
 */

@Service
public class ContractProductDataServicelmpl implements ContractProductDataService {

    @Autowired
    private ContractProductDataMapper contractProductDataMapper;

    private static final Logger log = LoggerFactory.getLogger("dayLogger");


    @Transactional
    @Override
    public void insert(ContractProductDataAddDTO contractProductDataAddDTO) {

        log.info("添加合同产品，{}", contractProductDataAddDTO);

        ContractProductDataPO contractProductDataPO = new ContractProductDataPO();
        BeanUtils.copyProperties(contractProductDataAddDTO, contractProductDataPO);


        contractProductDataPO.setId(MyUtil.getRandomID());
        contractProductDataPO.setCreateId(UserUtil.getLoginUserId());
        contractProductDataPO.setCreateTime(new Date());
        contractProductDataPO.setUpdateTime(new Date());

        //添加
        contractProductDataMapper.insertSelective(contractProductDataPO);

    }


    @Override
    @Transactional
    public void deleteById(ContractProductDataDeleteDTO contractProductDataDeleteDTO) {

        //获取用户id
        String id = contractProductDataDeleteDTO.getId();
        if (Objects.isNull(id)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_ID_NULL);
        }

        //删除
        contractProductDataMapper.deleteByPrimaryKey(id);

    }


    @Override
    @Transactional
    public void updateById(ContractProductDataUpdateDTO contractProductDataUpdateDTO) {


        ContractProductDataPO contractProductDataPO = new ContractProductDataPO();
        String id = contractProductDataUpdateDTO.getId();
        if (Objects.isNull(id)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_ID_NULL);
        }


        //赋值
        BeanUtils.copyProperties(contractProductDataUpdateDTO, contractProductDataPO);

        contractProductDataPO.setId(id);

        contractProductDataMapper.updateByPrimaryKeySelective(contractProductDataPO);
    }


    @Override
    public List<ContractProductDataVO> queryList(ContractProductQueryListDTO contractProductQueryListDTO) {

        //列表查询
        List<ContractProductDataPO> contractProductDataPOS = contractProductDataMapper.selectList(contractProductQueryListDTO);

        List<ContractProductDataVO> resListVOS = new ArrayList<>();

        //不为空处理返回结果
        if (CollUtil.isNotEmpty(contractProductDataPOS)) {
            for (ContractProductDataPO contractProductDataPO : contractProductDataPOS) {
                ContractProductDataVO contractProductDataVO = new ContractProductDataVO();
                BeanUtils.copyProperties(contractProductDataPO, contractProductDataVO);
                resListVOS.add(contractProductDataVO);
            }
        }

        return resListVOS;
    }


    @Override
    public TableResultUtil queryToPage(ContractProductDataQueryPageDTO contractProductDataQueryPageDTO) {

        //分页查询
        PageInfo<InternalContractServiceVO> pageInfo = PageHelper.startPage(contractProductDataQueryPageDTO.getPageNum(), contractProductDataQueryPageDTO.getPageSize()).doSelectPageInfo(new ISelect() {

            @Override
            public void doSelect() {
                ContractProductQueryListDTO contractProductQueryListDTO = new ContractProductQueryListDTO();

                //设置各种赋值条件
                queryList(contractProductQueryListDTO);
            }
        });

        return TableResultUtil.buildTableResult(pageInfo);
    }

    @Override
    public void deleteByContractId(String contractId) {
        if (StringUtils.isBlank(contractId)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_ID_NULL);
        }
        //根据合同的id查询产品信息
        ContractProductQueryListDTO contractProductQueryListDTO = new ContractProductQueryListDTO();
        contractProductQueryListDTO.setContractId(contractId);
        List<ContractProductDataVO> contractProductDataVOS = queryList(contractProductQueryListDTO);
        if (CollUtil.isNotEmpty(contractProductDataVOS)) {
            //删除
            for (ContractProductDataVO contractProductDataVO : contractProductDataVOS) {
                contractProductDataMapper.deleteByPrimaryKey(contractProductDataVO.getId());
            }
        }
    }


}
