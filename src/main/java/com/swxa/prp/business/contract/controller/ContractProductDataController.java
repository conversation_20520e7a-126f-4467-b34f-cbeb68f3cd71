package com.swxa.prp.business.contract.controller;

import com.swxa.prp.business.contract.dto.*;
import com.swxa.prp.business.contract.service.ContractProductDataService;
import com.swxa.prp.business.contract.vo.ContractProductDataVO;
import com.swxa.prp.util.ResponseData;
import com.swxa.prp.util.TableResultUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * @Description: 合同订单
 * @Author: zhangweicheng
 * @Date: 2025/7/18
 */

@Controller
@RequestMapping("/contract/product/data")
public class ContractProductDataController {


    @Autowired
    private ContractProductDataService contractProductDataService;

    private static final Logger log = LoggerFactory.getLogger("dayLogger");


    /**
     * @Description: 新增
     * @Param: [contractProductDataAddDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/18
     */


    @ResponseBody
    @PostMapping(value = "/insert")
    public ResponseData insert(@Validated @RequestBody ContractProductDataAddDTO contractProductDataAddDTO) {

        log.info("执行新增操作");

        contractProductDataService.insert(contractProductDataAddDTO);

        return ResponseData.ok();
    }

    /**
     * @Description: 列表查询
     * @Param: [contractOrderQueryListDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/18
     */


    @ResponseBody
    @PostMapping(value = "/queryList")
    public ResponseData queryList(@Validated @RequestBody ContractProductQueryListDTO contractProductQueryListDTO) {

        List<ContractProductDataVO> internalContractServiceVOS = contractProductDataService.queryList(contractProductQueryListDTO);

        return ResponseData.ok(true, internalContractServiceVOS);
    }


    /**
     * @Description: 分页查询
     * @Param: [contractProductDataQueryPageDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/18
     */


    @ResponseBody
    @PostMapping(value = "/queryToPage")
    public ResponseData queryToPage(@Validated @RequestBody ContractProductDataQueryPageDTO contractProductDataQueryPageDTO) {

        TableResultUtil resultUtil = contractProductDataService.queryToPage(contractProductDataQueryPageDTO);

        return ResponseData.ok(true, resultUtil);
    }


    /**
     * @Description: 根据ID删除关系
     * @Param: [contractProductDataDeleteDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/18
     */


    @ResponseBody
    @PostMapping(value = "/deleteById")
    public ResponseData deletePersonById(@Validated @RequestBody ContractProductDataDeleteDTO contractProductDataDeleteDTO) {
        contractProductDataService.deleteById(contractProductDataDeleteDTO);
        return ResponseData.ok();
    }


    /**
     * @Description: 更新合同产品信息
     * @Param: [contractOrderUpdateDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/18
     */

    @ResponseBody
    @PostMapping(value = "/updateById")
    public ResponseData updateById(@Validated @RequestBody ContractProductDataUpdateDTO contractProductDataUpdateDTO) {

        log.info("更新");
        contractProductDataService.updateById(contractProductDataUpdateDTO);

        return ResponseData.ok();
    }


}
