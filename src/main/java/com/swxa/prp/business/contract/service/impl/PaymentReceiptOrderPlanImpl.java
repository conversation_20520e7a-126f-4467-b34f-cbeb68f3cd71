package com.swxa.prp.business.contract.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.ISelect;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.swxa.prp.business.contract.dto.PaymentReceiptOrderPlanDeleteDTO;
import com.swxa.prp.business.contract.dto.PaymentReceiptOrderPlanInsertDTO;
import com.swxa.prp.business.contract.dto.PaymentReceiptOrderPlanQueryDTO;
import com.swxa.prp.business.contract.dto.PaymentReceiptOrderPlanUpdateDTO;
import com.swxa.prp.business.contract.model.PaymentReceiptOrderPlanPO;
import com.swxa.prp.business.contract.service.PaymentReceiptOrderPlanService;
import com.swxa.prp.business.contract.vo.PaymentReceiptOrderPlanVO;
import com.swxa.prp.business.dict.vo.BussDicDataVO;
import com.swxa.prp.business.supplymanage.dto.*;
import com.swxa.prp.business.supplymanage.entity.TransferOrderProductPO;
import com.swxa.prp.business.contract.dao.PaymentReceiptOrderPlanPOMapper;
import com.swxa.prp.business.supplymanage.vo.TransferOrderProductVO;
import com.swxa.prp.constant.SwErrorCodeConstant;
import com.swxa.prp.exception.SwPrpException;
import com.swxa.prp.util.MyUtil;
import com.swxa.prp.util.TableResultUtil;
import com.swxa.prp.util.UserUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
public class PaymentReceiptOrderPlanImpl implements PaymentReceiptOrderPlanService {
    private static final Logger log = LoggerFactory.getLogger("dayLogger");

    @Autowired
    private PaymentReceiptOrderPlanPOMapper crudMapper;

    /**
     * 增加
     */
    @Override
    @Transactional
    public void insertData(PaymentReceiptOrderPlanInsertDTO insertDTO) {


        PaymentReceiptOrderPlanPO insetPO = new PaymentReceiptOrderPlanPO();
        BeanUtils.copyProperties(insertDTO, insetPO);

        //设置主键ID
        insetPO.setId(MyUtil.getRandomID());

        insetPO.setCreateId(UserUtil.getLoginUserId());
        //设置时间
        insetPO.setCreateTime(new Date());
        insetPO.setUpdateTime(new Date());

        //添加
        crudMapper.insertSelective(insetPO);
    }


    /**
     * 删除指定id
     */

    @Override
    @Transactional
    public void deleteByID(PaymentReceiptOrderPlanDeleteDTO deleteDTO) {

        //获取用户id
        String id = deleteDTO.getId();
        if (Objects.isNull(id)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_ID_NULL);
        }
        //删除
        crudMapper.deleteByPrimaryKey(id);


        //删除该回款单下的回款计划钱的问题

    }

    /**
     * 根据id更新
     */
    @Override
    @Transactional
    public void updateByID(PaymentReceiptOrderPlanUpdateDTO updateDTO) {

        PaymentReceiptOrderPlanPO updatePO = new PaymentReceiptOrderPlanPO();
        String id = updateDTO.getId();
        if (Objects.isNull(id)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_ID_NULL);
        }

        BeanUtils.copyProperties(updateDTO, updatePO);

        //设置ID
        updatePO.setId(id);

        //设置时间
        updatePO.setUpdateTime(new Date());


        crudMapper.updateByPrimaryKeySelective(updatePO);
    }


    /**
     * 列表查询
     */

    @Override
    public List<PaymentReceiptOrderPlanVO> queryList(PaymentReceiptOrderPlanQueryDTO queryDTO) {

        //列表查询
        List<PaymentReceiptOrderPlanPO> queryPOS = crudMapper.selectList(queryDTO);


        List<PaymentReceiptOrderPlanVO> resListVOS = new ArrayList<>();

        //不为空处理返回结果
        if (CollUtil.isNotEmpty(queryPOS)) {
            for (PaymentReceiptOrderPlanPO dto : queryPOS) {
                PaymentReceiptOrderPlanVO resListVO = new PaymentReceiptOrderPlanVO();
                BeanUtils.copyProperties(dto, resListVO);
                resListVOS.add(resListVO);
            }
        }

        return resListVOS;
    }

    @Override
    public void deleteByReceiptOrderNo(String receiptNo) {
        //汇款单编号不能为空
        if (StringUtils.isBlank(receiptNo)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_RECEIPT_NO_NULL_ERROR);
        }

        //查询列表
        PaymentReceiptOrderPlanQueryDTO queryDTO = new PaymentReceiptOrderPlanQueryDTO();
        queryDTO.setReceiptNo(receiptNo);
        List<PaymentReceiptOrderPlanVO> paymentReceiptOrderPlanVOS = queryList(queryDTO);
        if (CollUtil.isNotEmpty(paymentReceiptOrderPlanVOS)) {
            for (PaymentReceiptOrderPlanVO paymentReceiptOrderPlanVO : paymentReceiptOrderPlanVOS) {
                PaymentReceiptOrderPlanDeleteDTO deleteDTO = new PaymentReceiptOrderPlanDeleteDTO();
                deleteDTO.setId(paymentReceiptOrderPlanVO.getId());
                //删除操作
                deleteByID(deleteDTO);
            }
        }

    }

}
