package com.swxa.prp.business.contract.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.ISelect;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.swxa.prp.PrpApplication;
import com.swxa.prp.business.act.conditionFields.dto.BussProcessStatusDTO;
import com.swxa.prp.business.attach.dto.AttachmentIdRelationInsertDTO;
import com.swxa.prp.business.attach.dto.AttachmentIdRelationQueryListDTO;
import com.swxa.prp.business.attach.model.Attachment;
import com.swxa.prp.business.attach.service.AttachmentIdRelationService;
import com.swxa.prp.business.attach.service.AttachmentService;
import com.swxa.prp.business.attach.vo.AttachmentRelationServiceVO;
import com.swxa.prp.business.contract.dto.*;
import com.swxa.prp.business.contract.model.PaymentReceiptOrderPO;
import com.swxa.prp.business.contract.dao.PaymentReceiptOrderPOMapper;
import com.swxa.prp.business.contract.service.ContractCollectPaymentPlanService;
import com.swxa.prp.business.contract.service.ContractOrderService;
import com.swxa.prp.business.contract.service.PaymentReceiptOrderPlanService;
import com.swxa.prp.business.contract.service.PaymentReceiptOrderService;
import com.swxa.prp.business.contract.vo.*;
import com.swxa.prp.business.customer.dto.QueryCustomerIdDTO;
import com.swxa.prp.business.customer.service.CustomerService;
import com.swxa.prp.business.customer.vo.BussCustomerVO;
import com.swxa.prp.business.dataauth._enum.CrmBussTypeEnum;
import com.swxa.prp.business.dataauth.dataauthrules.service.DataAuthService;
import com.swxa.prp.business.supplymanage.dto.TransferOrderProductQueryDTO;
import com.swxa.prp.business.supplymanage.entity.BussTransferOrderPO;
import com.swxa.prp.business.supplymanage.vo.TransferOrderBaseAndProdServiceVO;
import com.swxa.prp.business.supplymanage.vo.TransferOrderProductVO;
import com.swxa.prp.constant.SwErrorCodeConstant;
import com.swxa.prp.exception.SwPrpException;
import com.swxa.prp.util.MyUtil;
import com.swxa.prp.util.SqlToolsUtil;
import com.swxa.prp.util.TableResultUtil;
import com.swxa.prp.util.UserUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
public class PaymentReceiptOrderImpl implements PaymentReceiptOrderService {
    private static final Logger log = LoggerFactory.getLogger("dayLogger");

    @Autowired
    private PaymentReceiptOrderPOMapper crudMapper;
    @Autowired
    private AttachmentIdRelationService attachmentIdRelationService;
    @Autowired
    private ContractCollectPaymentPlanService contractCollectPaymentPlanService;

    @Autowired
    private PaymentReceiptOrderPlanService paymentReceiptOrderPlanService;

    @Autowired
    private AttachmentService attachmentService;

    @Lazy
    @Autowired
    private DataAuthService dataAuthService;

    @Autowired
    private ContractOrderService contractOrderService;
    @Autowired
    private CustomerService customerService;

    /**
     * 增加
     */
    @Override
    @Transactional
    public void insertData(PaymentReceiptOrderInsertDTO insertDTO) {


        PaymentReceiptOrderPO insetPO = new PaymentReceiptOrderPO();
        BeanUtils.copyProperties(insertDTO, insetPO);

        //处理脱敏数据，客户名称
        if (StringUtils.isNotBlank(insetPO.getCustomerId())) {
            QueryCustomerIdDTO requestParam  = new QueryCustomerIdDTO();
            requestParam.setId(insetPO.getCustomerId());
            BussCustomerVO bussCustomerVO = customerService.queryById(requestParam);

            insetPO.setCustomerName(bussCustomerVO.getCustomerName());
        }

        //设置主键ID
        insetPO.setId(MyUtil.getRandomID());

        insetPO.setCreateId(UserUtil.getLoginUserId());
        //设置时间
        insetPO.setCreateTime(new Date());
        insetPO.setUpdateTime(new Date());

        //添加
        crudMapper.insertSelective(insetPO);

        //处理附件
        List<String> attachIds = insertDTO.getAttachIds();
        if (CollUtil.isNotEmpty(attachIds)) {
            for (String attId : attachIds) {
                AttachmentIdRelationInsertDTO attachmentIdRelationInsertDTO = new AttachmentIdRelationInsertDTO();
                attachmentIdRelationInsertDTO.setCurrBussId(insetPO.getId());
                attachmentIdRelationInsertDTO.setAttachBussId(attId);
                attachmentIdRelationService.insert(attachmentIdRelationInsertDTO);
            }
        }

        //处理回款计划信息包含合同信息
        List<ReceiptCollectPaymentPlanAddDTO> paymentPlans = insertDTO.getPaymentReceiptCollectPlanDetails();
        if (CollUtil.isEmpty(paymentPlans)) {//回款单回款计划金额信息为空
            throw new SwPrpException(SwErrorCodeConstant.ERROR_RECEIPT_PAYMENT_PLAN_ERROR);
        }

        //更新每个回款单里面   回款计划的钱的问题
        for (ReceiptCollectPaymentPlanAddDTO receiptCollectPaymentPlanAddDTO : paymentPlans) {
            ContractOrderUpdatePaymentPlanInfoDTO updatePaymentPlanInfoDTO = new ContractOrderUpdatePaymentPlanInfoDTO();
            updatePaymentPlanInfoDTO.setSourceOrderNo(insertDTO.getSourceOrderNo());
            updatePaymentPlanInfoDTO.setSourceOrderType(insertDTO.getSourceOrderType());
            updatePaymentPlanInfoDTO.setPaymentPlanCode(receiptCollectPaymentPlanAddDTO.getPaymentPlanCode());
            updatePaymentPlanInfoDTO.setPaymentSum(receiptCollectPaymentPlanAddDTO.getCurrentPaymentCount());
            updatePaymentPlanInfoDTO.setLastPaymentDate(insertDTO.getReceiptDate());
            updatePaymentPlanInfoDTO.setReceiptNo(insertDTO.getReceiptNo());
            contractCollectPaymentPlanService.updateContracePaymentPlanInfo(updatePaymentPlanInfoDTO);
        }
    }


    /**
     * 删除指定id
     */

    @Override
    @Transactional
    public void deleteByID(PaymentReceiptOrderIdDTO deleteDTO) {

        //获取id
        String id = deleteDTO.getId();
        if (Objects.isNull(id)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_ID_NULL);
        }
        //查询信息
        PaymentReceiptOrderPO paymentReceiptOrderPO = crudMapper.selectByPrimaryKey(id);
        //删除
        crudMapper.deleteByPrimaryKey(id);
        //删除附件
        attachmentIdRelationService.deleteByBussId(paymentReceiptOrderPO.getId());
        //todo 删除回款单与回款计划的钱关系表,根据回款单编号删除回款计划钱的数据
        paymentReceiptOrderPlanService.deleteByReceiptOrderNo(paymentReceiptOrderPO.getReceiptNo());

    }


    /**
     * 根据id更新
     */
    @Override
    @Transactional
    public void updateByID(PaymentReceiptOrderUpdateDTO updateDTO) {

        //1.更新基本数据
        PaymentReceiptOrderPO updatePO = new PaymentReceiptOrderPO();
        String id = updateDTO.getId();
        if (Objects.isNull(id)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_ID_NULL);
        }

        BeanUtils.copyProperties(updateDTO, updatePO);
        //处理脱敏数据，客户名称
        if (StringUtils.isNotBlank(updatePO.getCustomerId())) {
            QueryCustomerIdDTO requestParam  = new QueryCustomerIdDTO();
            requestParam.setId(updatePO.getCustomerId());
            BussCustomerVO bussCustomerVO = customerService.queryById(requestParam);

            updatePO.setCustomerName(bussCustomerVO.getCustomerName());
        }

        //设置ID
        updatePO.setId(id);

        //设置时间
        updatePO.setUpdateTime(new Date());
        crudMapper.updateByPrimaryKeySelective(updatePO);

        //2.处理附件
        List<String> attachIds = updateDTO.getAttachIds();
        if (CollUtil.isNotEmpty(attachIds)) {

            //查询当前合同对应的附件关系
            AttachmentIdRelationQueryListDTO attachmentIdRelationQueryListDTO = new AttachmentIdRelationQueryListDTO();
            attachmentIdRelationQueryListDTO.setCurrBussId(updatePO.getId());
            List<AttachmentRelationServiceVO> attachmentRelationServiceVOS = attachmentIdRelationService.queryList(attachmentIdRelationQueryListDTO);
            if (CollUtil.isEmpty(attachmentRelationServiceVOS)) {
                //新附件直接添加
                for (String attId : attachIds) {
                    AttachmentIdRelationInsertDTO attachmentIdRelationInsertDTO = new AttachmentIdRelationInsertDTO();
                    attachmentIdRelationInsertDTO.setCurrBussId(updatePO.getId());
                    attachmentIdRelationInsertDTO.setAttachBussId(attId);
                    attachmentIdRelationService.insert(attachmentIdRelationInsertDTO);
                }
            } else {
                //暂存已经存在的附件ID
                List<String> existsAttIds = new ArrayList<>();
                for (AttachmentRelationServiceVO attachmentRelationServiceVO : attachmentRelationServiceVOS) {
                    String attachBussId = attachmentRelationServiceVO.getAttachBussId();
                    //不为空
                    if (attachIds.contains(attachBussId)) {
                        existsAttIds.add(attachBussId);
                    } else {
                        //说明附件被删除了，需要删除附件
                        attachmentIdRelationService.deleteByAttachBussId(attachBussId);
                    }
                }

                //遍历编辑时提交的附件ID，不存在的就执行添加操作
                for (String aid : attachIds) {
                    //不存在新增
                    if (!existsAttIds.contains(aid)) {
                        AttachmentIdRelationInsertDTO attachmentIdRelationInsertDTO = new AttachmentIdRelationInsertDTO();
                        attachmentIdRelationInsertDTO.setCurrBussId(updatePO.getId());
                        attachmentIdRelationInsertDTO.setAttachBussId(aid);
                        attachmentIdRelationService.insert(attachmentIdRelationInsertDTO);
                    }
                }
            }

        } else {
            attachmentIdRelationService.deleteByBussId(updatePO.getId());
        }

        //3.处理回款单回款计划的钱的问题
        //处理回款计划信息包含合同信息
        List<ReceiptCollectPaymentPlanAddDTO> paymentPlans = updateDTO.getPaymentReceiptCollectPlanDetails();
        if (CollUtil.isEmpty(paymentPlans)) {//回款单回款计划金额信息为空
            throw new SwPrpException(SwErrorCodeConstant.ERROR_RECEIPT_PAYMENT_PLAN_ERROR);
        }

        //更新每个回款单里面   回款计划的钱的问题
        for (ReceiptCollectPaymentPlanAddDTO receiptCollectPaymentPlanAddDTO : paymentPlans) {
            ContractOrderUpdatePaymentPlanInfoDTO updatePaymentPlanInfoDTO = new ContractOrderUpdatePaymentPlanInfoDTO();
            updatePaymentPlanInfoDTO.setSourceOrderNo(updateDTO.getSourceOrderNo());
            updatePaymentPlanInfoDTO.setSourceOrderType(updateDTO.getSourceOrderType());
            updatePaymentPlanInfoDTO.setPaymentPlanCode(receiptCollectPaymentPlanAddDTO.getPaymentPlanCode());
            updatePaymentPlanInfoDTO.setPaymentSum(receiptCollectPaymentPlanAddDTO.getCurrentPaymentCount());
            updatePaymentPlanInfoDTO.setLastPaymentDate(updateDTO.getReceiptDate());
            updatePaymentPlanInfoDTO.setReceiptNo(updateDTO.getReceiptNo());
            contractCollectPaymentPlanService.updateContracePaymentPlanInfo(updatePaymentPlanInfoDTO);
        }

    }


    /**
     * 分页查询
     */
    @Override
    public TableResultUtil queryToPage(PaymentReceiptOrderQueryPageDTO queryPageDTO) {


        //追加数据授权条件
        List<String> bussIds = dataAuthService.getBussIds(CrmBussTypeEnum.contractReceipt.getCode());


        PageInfo<PaymentReceiptOrderVO> pageInfo = PageHelper.startPage(queryPageDTO.getPageNum(), queryPageDTO.getPageSize()).doSelectPageInfo(new ISelect() {
            @Override
            public void doSelect() {
                PaymentReceiptOrderQueryDTO queryDTO = new PaymentReceiptOrderQueryDTO();
                queryDTO.setSql(SqlToolsUtil.constructFields(queryPageDTO.getAdvanceQueryGroups()));
                queryDTO.setSourceOrderNo(queryPageDTO.getSourceOrderNo());
                queryDTO.setCustomerName(queryPageDTO.getCustomerName());
                queryDTO.setSubjectSelectName(queryPageDTO.getSubjectSelectName());


                //设置  可访问的业务ids  contractOrderQueryListDTO
                if (CollUtil.isNotEmpty(bussIds)) {
                    queryDTO.setBussIds(bussIds);
                    queryDTO.setIsAuth(false);
                }


                queryList(queryDTO);
            }
        });

        //计算合同金额和合同应余额
        List<PaymentReceiptOrderVO> list = pageInfo.getList();
        if (CollUtil.isNotEmpty(list)) {
            List<PaymentReceiptOrderVO> paymentReceiptOrderVOS = JSONObject.parseArray(JSONObject.toJSONString(list), PaymentReceiptOrderVO.class);
            if (CollUtil.isNotEmpty(paymentReceiptOrderVOS)) {
                for (PaymentReceiptOrderVO paymentReceiptOrderVO : paymentReceiptOrderVOS) {
                    String sourceOrderNo = paymentReceiptOrderVO.getSourceOrderNo();
                    if (StringUtils.isBlank(sourceOrderNo)) {//编码不存在直接设置为0
                        paymentReceiptOrderVO.setContractSum(BigDecimal.ZERO);
                        paymentReceiptOrderVO.setContractNoPaymentCount(BigDecimal.ZERO);
                    } else {
                        //查询合同信息
                        ContractOrderQueryListDTO contractOrderQueryListDTO = new ContractOrderQueryListDTO();
                        contractOrderQueryListDTO.setContractNum(sourceOrderNo);
                        List<ContractOrderVO> contractOrderVOS = contractOrderService.queryList(contractOrderQueryListDTO);
                        if (CollUtil.isEmpty(contractOrderVOS)) {
                            //合同信息不存在
                            //throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_ORDER_NULL);
                            return TableResultUtil.buildTableResult(pageInfo);
                        }

                        ContractOrderVO contractOrderVO = contractOrderVOS.get(0);
                        //合同总金额
                        BigDecimal contractSum = contractOrderVO.getContractSum();
                        if (Objects.isNull(contractSum)) {
                            contractSum = BigDecimal.ZERO;
                        }


                        //查询合同对应的回款计划信息
                        ContractCollectPaymentPlanQueryListDTO contractCollectPaymentPlanQueryListDTO = new ContractCollectPaymentPlanQueryListDTO();
                        contractCollectPaymentPlanQueryListDTO.setContractId(contractOrderVO.getId());
                        List<ContractCollectPaymentPlanVO> contractCollectPaymentPlanVOS = contractCollectPaymentPlanService.queryList(contractCollectPaymentPlanQueryListDTO);


                        //合同回款总金额
                        BigDecimal contractSumBigDecimal = BigDecimal.ZERO;
                        if (CollUtil.isNotEmpty(contractCollectPaymentPlanVOS)) {


                            //根据每个回款计划编码查询，查询回款计划钱的问题
                            for (ContractCollectPaymentPlanVO contractCollectPaymentPlanVO : contractCollectPaymentPlanVOS) {

                                //根据回款编码查询回款单回款计划信息
                                String paymentPlanCode = contractCollectPaymentPlanVO.getPaymentPlanCode();

                                if (StringUtils.isNotBlank(paymentPlanCode)) {

                                    PaymentReceiptOrderPlanQueryDTO paymentReceiptOrderPlanQueryDTO = new PaymentReceiptOrderPlanQueryDTO();
                                    paymentReceiptOrderPlanQueryDTO.setPaymentPlanCode(contractCollectPaymentPlanVO.getPaymentPlanCode());
                                    List<PaymentReceiptOrderPlanVO> paymentReceiptOrderPlanVOS = paymentReceiptOrderPlanService.queryList(paymentReceiptOrderPlanQueryDTO);
                                    if (CollUtil.isNotEmpty(paymentReceiptOrderPlanVOS)) {
                                        //计算回款单里的回款计划的钱信息
                                        for (PaymentReceiptOrderPlanVO paymentReceiptOrderPlanVO : paymentReceiptOrderPlanVOS) {
                                            //根据此关系查询初具体的回款计划的详情信息
                                            BigDecimal paymentCount = paymentReceiptOrderPlanVO.getPaymentCount();
                                            if (Objects.nonNull(paymentCount)) {
                                                //汇总合同回款总金额
                                                contractSumBigDecimal=contractSumBigDecimal.add(paymentCount);
                                            }
                                        }
                                    }
                                }

                            }

                        } else {
                            paymentReceiptOrderVO.setContractSum(BigDecimal.ZERO);
                            paymentReceiptOrderVO.setContractNoPaymentCount(BigDecimal.ZERO);
                        }

                        //合同以回款金额和合同应收余额
                        paymentReceiptOrderVO.setContractSum(contractSum);
                        paymentReceiptOrderVO.setContractNoPaymentCount(contractSum.subtract(contractSumBigDecimal));

                    }
                }
            }

            //设置新的数据
            pageInfo.setList(paymentReceiptOrderVOS);
        }

        return TableResultUtil.buildTableResult(pageInfo);
    }

    /**
     * 列表查询
     */

    @Override
    public List<PaymentReceiptOrderVO> queryList(PaymentReceiptOrderQueryDTO queryDTO) {


        //列表查询
        if (queryDTO.getIsAuth()) {
            //追加数据授权条件
            List<String> bussIds = dataAuthService.getBussIds(CrmBussTypeEnum.contractReceipt.getCode());
            //设置  可访问的业务ids  contractOrderQueryListDTO
            if (CollUtil.isNotEmpty(bussIds)) {
                queryDTO.setBussIds(bussIds);
            }
        }


        //列表查询
        List<PaymentReceiptOrderPO> queryPOS = crudMapper.selectList(queryDTO);


        List<PaymentReceiptOrderVO> resListVOS = new ArrayList<>();

        //不为空处理返回结果
        if (CollUtil.isNotEmpty(queryPOS)) {
            for (PaymentReceiptOrderPO dto : queryPOS) {
                PaymentReceiptOrderVO resListVO = new PaymentReceiptOrderVO();
                BeanUtils.copyProperties(dto, resListVO);
                resListVOS.add(resListVO);
            }
        }

        return resListVOS;
    }

    @Override
    public PaymentReceiptOrderVO queryById(PaymentReceiptOrderIdDTO queryDTO) {


        //1.
        String id = queryDTO.getId();
        if (Objects.isNull(id)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_ID_NULL);
        }

        //2.根据ID查询基本信息
        PaymentReceiptOrderPO paymentReceiptOrderPO = crudMapper.selectByPrimaryKey(id);
        if (Objects.isNull(paymentReceiptOrderPO)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_RECEIPT_PAYMENT_DATA_ERROR);
        }


        //处理返回结果
        PaymentReceiptOrderVO paymentReceiptOrderVO = new PaymentReceiptOrderVO();
        BeanUtils.copyProperties(paymentReceiptOrderPO, paymentReceiptOrderVO);


        //查询附件信息
        AttachmentIdRelationQueryListDTO attachmentIdRelationQueryListDTO = new AttachmentIdRelationQueryListDTO();
        attachmentIdRelationQueryListDTO.setCurrBussId(paymentReceiptOrderPO.getId());
        List<AttachmentRelationServiceVO> attachmentRelationServiceVOS = attachmentIdRelationService.queryList(attachmentIdRelationQueryListDTO);
        if (CollUtil.isNotEmpty(attachmentRelationServiceVOS)) {
            //存储附件的id
            List<Attachment> attIds = new ArrayList<>();
            for (AttachmentRelationServiceVO attachmentRelationServiceVO : attachmentRelationServiceVOS) {
                String attachBussId = attachmentRelationServiceVO.getAttachBussId();
                if (StringUtils.isBlank(attachBussId)) {
                    continue;
                }
                Attachment attachment = attachmentService.selectById(Integer.parseInt(attachBussId));
                attIds.add(attachment);
            }
            //赋值
            paymentReceiptOrderVO.setAttachIds(attIds);
        }

        //根据合同编码查询回款计划
        String sourceOrderNo = paymentReceiptOrderPO.getSourceOrderNo();

        if (StringUtils.isBlank(sourceOrderNo)) {//合同编码不能为空
            throw new SwPrpException(SwErrorCodeConstant.ERROR_ORDER_CODE_NULL);
        }


        //查询合同信息
        ContractOrderQueryListDTO contractOrderQueryListDTO = new ContractOrderQueryListDTO();
        contractOrderQueryListDTO.setContractNum(sourceOrderNo);
        List<ContractOrderVO> contractOrderVOS = contractOrderService.queryList(contractOrderQueryListDTO);
        if (CollUtil.isEmpty(contractOrderVOS)) {
            //合同信息不存在
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_ORDER_NULL);
        }

        ContractOrderVO contractOrderVO = contractOrderVOS.get(0);
        //合同总金额
        BigDecimal contractSum = contractOrderVO.getContractSum();
        if (Objects.isNull(contractSum)) {
            contractSum = BigDecimal.ZERO;
        }


        //查询合同对应的回款计划信息
        ContractCollectPaymentPlanQueryListDTO contractCollectPaymentPlanQueryListDTO = new ContractCollectPaymentPlanQueryListDTO();
        contractCollectPaymentPlanQueryListDTO.setContractId(contractOrderVO.getId());
        List<ContractCollectPaymentPlanVO> contractCollectPaymentPlanVOS = contractCollectPaymentPlanService.queryList(contractCollectPaymentPlanQueryListDTO);


        //合同回款总金额
        BigDecimal contractSumBigDecimal = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(contractCollectPaymentPlanVOS)) {

            //返回回款计划的明细信息
            List<PaymentReceiptCollectPlanDetailVO> paymentReceiptCollectPlanDetailVOS = new ArrayList<>();

            //根据每个回款计划编码查询，查询回款计划钱的问题
            for (ContractCollectPaymentPlanVO contractCollectPaymentPlanVO : contractCollectPaymentPlanVOS) {

                //赋值合同信息和回款计划部分信息
                PaymentReceiptCollectPlanDetailVO paymentReceiptCollectPlanDetailVO = new PaymentReceiptCollectPlanDetailVO();
                BeanUtils.copyProperties(contractCollectPaymentPlanVO, paymentReceiptCollectPlanDetailVO);
                //设置回款单编码
                paymentReceiptCollectPlanDetailVO.setSourceOrderNo(contractCollectPaymentPlanVO.getPaymentPlanCode());

                //根据回款编码查询回款单回款计划信息
                String paymentPlanCode = contractCollectPaymentPlanVO.getPaymentPlanCode();

                //各个回款计划初始化金额
                BigDecimal sumBigDecimal = BigDecimal.ZERO;

                if (StringUtils.isNotBlank(paymentPlanCode)) {

                    PaymentReceiptOrderPlanQueryDTO paymentReceiptOrderPlanQueryDTO = new PaymentReceiptOrderPlanQueryDTO();
                    paymentReceiptOrderPlanQueryDTO.setPaymentPlanCode(contractCollectPaymentPlanVO.getPaymentPlanCode());
                    List<PaymentReceiptOrderPlanVO> paymentReceiptOrderPlanVOS = paymentReceiptOrderPlanService.queryList(paymentReceiptOrderPlanQueryDTO);
                    if (CollUtil.isNotEmpty(paymentReceiptOrderPlanVOS)) {
                        //计算回款单里的回款计划的钱信息
                        for (PaymentReceiptOrderPlanVO paymentReceiptOrderPlanVO : paymentReceiptOrderPlanVOS) {
                            //根据此关系查询初具体的回款计划的详情信息
                            BigDecimal paymentCount = paymentReceiptOrderPlanVO.getPaymentCount();
                            if (Objects.nonNull(paymentCount)) {
                                //求和
                                sumBigDecimal=sumBigDecimal.add(paymentCount);

                                //汇总合同回款总金额
                                contractSumBigDecimal=contractSumBigDecimal.add(paymentCount);
                            }
                        }
                    }
                }

                //设置金额   已收金额
                paymentReceiptCollectPlanDetailVO.setPaymentSum(sumBigDecimal);

                //计算当前应该收取金额
                BigDecimal paymentCount = contractCollectPaymentPlanVO.getPaymentCount();
                if (Objects.nonNull(paymentCount) && Objects.nonNull(sumBigDecimal)) {
                    paymentReceiptCollectPlanDetailVO.setCurrentPaymentCount(paymentCount.subtract(sumBigDecimal));
                } else {
                    paymentReceiptCollectPlanDetailVO.setCurrentPaymentCount(BigDecimal.ZERO);
                }


                //设置合同编码、合同金额、最新开票日期、已开票金额、 合同已回款金额（已回款金额最后赋值）
                paymentReceiptCollectPlanDetailVO.setContractNum(contractOrderVO.getContractNum());
                paymentReceiptCollectPlanDetailVO.setContractSum(contractOrderVO.getContractSum());
                paymentReceiptCollectPlanDetailVO.setContractInvoiceCount(contractOrderVO.getContractInvoiceCount());
                paymentReceiptCollectPlanDetailVO.setLastPaymentDate(contractOrderVO.getLastPaymentDate());
                paymentReceiptCollectPlanDetailVO.setUpdateInvoiceTime(contractOrderVO.getUpdateInvoiceTime());

                //赋值
                paymentReceiptCollectPlanDetailVOS.add(paymentReceiptCollectPlanDetailVO);
            }


            //最后设置合同的以回款总额，复制到每个付款计划里面
            if (CollUtil.isNotEmpty(paymentReceiptCollectPlanDetailVOS)) {
                for (PaymentReceiptCollectPlanDetailVO paymentReceiptCollectPlanDetailVO : paymentReceiptCollectPlanDetailVOS) {
                    paymentReceiptCollectPlanDetailVO.setContractPaymentCount(contractSumBigDecimal);
                }
            }
            //回款单回款计划钱的详情信息
            paymentReceiptOrderVO.setPaymentReceiptCollectPlanDetails(paymentReceiptCollectPlanDetailVOS);
        }


        //合同以回款金额和合同应收余额
        paymentReceiptOrderVO.setContractSum(contractSum);
        paymentReceiptOrderVO.setContractNoPaymentCount(contractSum.subtract(contractSumBigDecimal));


        return paymentReceiptOrderVO;
    }

    /**
    * @Description: 根据id或编码查询详情
    * @Param: [queryDTO]
    * @return: com.swxa.prp.business.contract.vo.PaymentReceiptOrderVO
    * @Author: lwei
    * @Date: 2025/9/4
    */
    @Override
    public PaymentReceiptOrderVO queryByIdCode(PaymentReceiptOrderIdDTO queryDTO) {
        //1.
        String id = queryDTO.getId();
        String num = queryDTO.getReceiptNo();
        if (StringUtils.isBlank(id) && StringUtils.isBlank(num)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_ID_NULL);
        }

        PaymentReceiptOrderQueryDTO queryListDTO = new PaymentReceiptOrderQueryDTO();
        BeanUtils.copyProperties(queryDTO,queryListDTO);
        List<PaymentReceiptOrderPO> queryPOS = crudMapper.selectList(queryListDTO);

        if (CollUtil.isEmpty(queryPOS)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_RECEIPT_PAYMENT_DATA_ERROR);
        }

        //2.根据ID查询基本信息
        PaymentReceiptOrderPO paymentReceiptOrderPO = queryPOS.get(0);
        if (Objects.isNull(paymentReceiptOrderPO)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_RECEIPT_PAYMENT_DATA_ERROR);
        }


        //处理返回结果
        PaymentReceiptOrderVO paymentReceiptOrderVO = new PaymentReceiptOrderVO();
        BeanUtils.copyProperties(paymentReceiptOrderPO, paymentReceiptOrderVO);


        //查询附件信息
        AttachmentIdRelationQueryListDTO attachmentIdRelationQueryListDTO = new AttachmentIdRelationQueryListDTO();
        attachmentIdRelationQueryListDTO.setCurrBussId(paymentReceiptOrderPO.getId());
        List<AttachmentRelationServiceVO> attachmentRelationServiceVOS = attachmentIdRelationService.queryList(attachmentIdRelationQueryListDTO);
        if (CollUtil.isNotEmpty(attachmentRelationServiceVOS)) {
            //存储附件的id
            List<Attachment> attIds = new ArrayList<>();
            for (AttachmentRelationServiceVO attachmentRelationServiceVO : attachmentRelationServiceVOS) {
                String attachBussId = attachmentRelationServiceVO.getAttachBussId();
                if (StringUtils.isBlank(attachBussId)) {
                    continue;
                }
                Attachment attachment = attachmentService.selectById(Integer.parseInt(attachBussId));
                attIds.add(attachment);
            }
            //赋值
            paymentReceiptOrderVO.setAttachIds(attIds);
        }

        //根据合同编码查询回款计划
        String sourceOrderNo = paymentReceiptOrderPO.getSourceOrderNo();

        if (StringUtils.isBlank(sourceOrderNo)) {//合同编码不能为空
            throw new SwPrpException(SwErrorCodeConstant.ERROR_ORDER_CODE_NULL);
        }


        //查询合同信息
        ContractOrderQueryListDTO contractOrderQueryListDTO = new ContractOrderQueryListDTO();
        contractOrderQueryListDTO.setContractNum(sourceOrderNo);
        List<ContractOrderVO> contractOrderVOS = contractOrderService.queryList(contractOrderQueryListDTO);
        if (CollUtil.isEmpty(contractOrderVOS)) {
            //合同信息不存在
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_ORDER_NULL);
        }

        ContractOrderVO contractOrderVO = contractOrderVOS.get(0);
        //合同总金额
        BigDecimal contractSum = contractOrderVO.getContractSum();
        if (Objects.isNull(contractSum)) {
            contractSum = BigDecimal.ZERO;
        }


        //查询合同对应的回款计划信息
        ContractCollectPaymentPlanQueryListDTO contractCollectPaymentPlanQueryListDTO = new ContractCollectPaymentPlanQueryListDTO();
        contractCollectPaymentPlanQueryListDTO.setContractId(contractOrderVO.getId());
        List<ContractCollectPaymentPlanVO> contractCollectPaymentPlanVOS = contractCollectPaymentPlanService.queryList(contractCollectPaymentPlanQueryListDTO);


        //合同回款总金额
        BigDecimal contractSumBigDecimal = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(contractCollectPaymentPlanVOS)) {

            //返回回款计划的明细信息
            List<PaymentReceiptCollectPlanDetailVO> paymentReceiptCollectPlanDetailVOS = new ArrayList<>();

            //根据每个回款计划编码查询，查询回款计划钱的问题
            for (ContractCollectPaymentPlanVO contractCollectPaymentPlanVO : contractCollectPaymentPlanVOS) {

                //赋值合同信息和回款计划部分信息
                PaymentReceiptCollectPlanDetailVO paymentReceiptCollectPlanDetailVO = new PaymentReceiptCollectPlanDetailVO();
                BeanUtils.copyProperties(contractCollectPaymentPlanVO, paymentReceiptCollectPlanDetailVO);
                //设置回款单编码
                paymentReceiptCollectPlanDetailVO.setSourceOrderNo(contractCollectPaymentPlanVO.getPaymentPlanCode());

                //根据回款编码查询回款单回款计划信息
                String paymentPlanCode = contractCollectPaymentPlanVO.getPaymentPlanCode();

                //各个回款计划初始化金额
                BigDecimal sumBigDecimal = BigDecimal.ZERO;

                if (StringUtils.isNotBlank(paymentPlanCode)) {

                    PaymentReceiptOrderPlanQueryDTO paymentReceiptOrderPlanQueryDTO = new PaymentReceiptOrderPlanQueryDTO();
                    paymentReceiptOrderPlanQueryDTO.setPaymentPlanCode(contractCollectPaymentPlanVO.getPaymentPlanCode());
                    List<PaymentReceiptOrderPlanVO> paymentReceiptOrderPlanVOS = paymentReceiptOrderPlanService.queryList(paymentReceiptOrderPlanQueryDTO);
                    if (CollUtil.isNotEmpty(paymentReceiptOrderPlanVOS)) {
                        //计算回款单里的回款计划的钱信息
                        for (PaymentReceiptOrderPlanVO paymentReceiptOrderPlanVO : paymentReceiptOrderPlanVOS) {
                            //根据此关系查询初具体的回款计划的详情信息
                            BigDecimal paymentCount = paymentReceiptOrderPlanVO.getPaymentCount();
                            if (Objects.nonNull(paymentCount)) {
                                //求和
                                sumBigDecimal=sumBigDecimal.add(paymentCount);

                                //汇总合同回款总金额
                                contractSumBigDecimal=contractSumBigDecimal.add(paymentCount);
                            }
                        }
                    }
                }

                //设置金额   已收金额
                paymentReceiptCollectPlanDetailVO.setPaymentSum(sumBigDecimal);

                //计算当前应该收取金额
                BigDecimal paymentCount = contractCollectPaymentPlanVO.getPaymentCount();
                if (Objects.nonNull(paymentCount) && Objects.nonNull(sumBigDecimal)) {
                    paymentReceiptCollectPlanDetailVO.setCurrentPaymentCount(paymentCount.subtract(sumBigDecimal));
                } else {
                    paymentReceiptCollectPlanDetailVO.setCurrentPaymentCount(BigDecimal.ZERO);
                }


                //设置合同编码、合同金额、最新开票日期、已开票金额、 合同已回款金额（已回款金额最后赋值）
                paymentReceiptCollectPlanDetailVO.setContractNum(contractOrderVO.getContractNum());
                paymentReceiptCollectPlanDetailVO.setContractSum(contractOrderVO.getContractSum());
                paymentReceiptCollectPlanDetailVO.setContractInvoiceCount(contractOrderVO.getContractInvoiceCount());
                paymentReceiptCollectPlanDetailVO.setLastPaymentDate(contractOrderVO.getLastPaymentDate());
                paymentReceiptCollectPlanDetailVO.setUpdateInvoiceTime(contractOrderVO.getUpdateInvoiceTime());

                //赋值
                paymentReceiptCollectPlanDetailVOS.add(paymentReceiptCollectPlanDetailVO);
            }


            //最后设置合同的以回款总额，复制到每个付款计划里面
            if (CollUtil.isNotEmpty(paymentReceiptCollectPlanDetailVOS)) {
                for (PaymentReceiptCollectPlanDetailVO paymentReceiptCollectPlanDetailVO : paymentReceiptCollectPlanDetailVOS) {
                    paymentReceiptCollectPlanDetailVO.setContractPaymentCount(contractSumBigDecimal);
                }
            }
            //回款单回款计划钱的详情信息
            paymentReceiptOrderVO.setPaymentReceiptCollectPlanDetails(paymentReceiptCollectPlanDetailVOS);
        }


        //合同以回款金额和合同应收余额
        paymentReceiptOrderVO.setContractSum(contractSum);
        paymentReceiptOrderVO.setContractNoPaymentCount(contractSum.subtract(contractSumBigDecimal));


        return paymentReceiptOrderVO;
    }


    /**
     * @Description: 根据负责人id列表查询对应的记录id列表
     * @Param: [saleUserId]
     * @return: java.util.List<java.lang.String>
     * @Author: lwei
     * @Date: 2025/9/18
     */
    @Override
    public List<String> findBySaleUserIds(List<String> saleUserId) {

        if (CollUtil.isNotEmpty(saleUserId)) {
            List<String> ids = crudMapper.selectListBySaleUserId(saleUserId);
            if (CollUtil.isNotEmpty(ids)) {
                return ids;
            }
        }
        return null;
    }

    /**
     * @Description: 根据创建者id列表，查询对应的数据id列表
     * @Param: [createId]
     * @return: java.util.List<java.lang.String>
     * @Author: lwei
     * @Date: 2025/9/18
     */
    @Override
    public List<String> findByCreateIds(List<String> createId) {
        if (CollUtil.isNotEmpty(createId)) {
            List<String> ids = crudMapper.selectListByCreateId(createId);
            if (CollUtil.isNotEmpty(ids)) {
                return ids;
            }
        }
        return null;
    }

    /**
     * @Description: 根据id，更新审批状态
     * @Param: [processStatusDTO]
     * @return: void
     * @Author: lwei
     * @Date: 2025/9/19
     */
    @Override
    public void updateProcessStatusById(BussProcessStatusDTO processStatusDTO) {
        String id = processStatusDTO.getId();
        if (StringUtils.isNotBlank(id)) {
            PaymentReceiptOrderPO bussContactInfo = new PaymentReceiptOrderPO();
            bussContactInfo.setId(id);
            bussContactInfo.setBussProcessStatus(processStatusDTO.getBussProcessStatus());

            crudMapper.updateByPrimaryKeySelective(bussContactInfo);
        }
    }


}
