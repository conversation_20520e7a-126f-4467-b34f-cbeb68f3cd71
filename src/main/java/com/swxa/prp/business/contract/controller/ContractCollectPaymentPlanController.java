package com.swxa.prp.business.contract.controller;

import com.swxa.prp.business.contract.dto.*;
import com.swxa.prp.business.contract.service.ContractCollectPaymentPlanService;
import com.swxa.prp.business.contract.vo.ContractCollectPaymentPlanAndBaseVO;
import com.swxa.prp.datamask.SwDeSensitive;
import com.swxa.prp.util.ResponseData;
import com.swxa.prp.util.TableResultUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * @Description: 合同订单回款计划控制层
 * @Author: zhangweicheng
 * @Date: 2025/7/19
 */

@Controller
@RequestMapping("/contract/collect/payment/plan")
public class ContractCollectPaymentPlanController {

    @Autowired
    private ContractCollectPaymentPlanService contractCollectPaymentPlanService;

    private static final Logger log = LoggerFactory.getLogger("dayLogger");


    /**
     * @Description: 删除
     * @Param: [deleteCollectPaymentPlan]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/19
     */


    @ResponseBody
    @PostMapping(value = "/deleteById")
    public ResponseData deleteById(@Validated @RequestBody ContractCollectPaymentPlanDeleteDTO deleteCollectPaymentPlan) {

        log.info("ContractCollectPaymentPlanController.deleteContractCollectPaymentPlan {}", deleteCollectPaymentPlan);

        contractCollectPaymentPlanService.deleteById(deleteCollectPaymentPlan);

        return ResponseData.ok();
    }


    /**
     * @Description: 根据Id或编码查询详情
     * @Param: [queryContractCollectPaymentPlan]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/19
     */


    @ResponseBody
    @PostMapping(value = "/queryById")
    public ResponseData queryById(@Validated @RequestBody ContractCollectPaymentPlanQueryDTO queryContractCollectPaymentPlan) {

        log.info("ContractCollectPaymentPlanController.queryContractPaymentConditionById {}", queryContractCollectPaymentPlan);

        ContractCollectPaymentPlanAndBaseVO contractCollectPaymentPlanAndBaseVO = contractCollectPaymentPlanService.queryByIdCode(queryContractCollectPaymentPlan);

        String filedName=queryContractCollectPaymentPlan.getFiledName();

        if (!queryContractCollectPaymentPlan.getIsMask()) {
            // 不脱敏，返回明文
            if (StringUtils.isNotBlank(filedName)) {
                // 指定了非脱敏字段，只处理这个字段值，并返回
                String fieldNomaskVal= SwDeSensitive.decryptSensitive(contractCollectPaymentPlanAndBaseVO,filedName);
                return ResponseData.ok(true, fieldNomaskVal);
            } else {
                // 处理所有的脱敏字段值，返回整个对象
                return ResponseData.ok(true, SwDeSensitive.removeSensitive(contractCollectPaymentPlanAndBaseVO));
            }
        } else {
            //脱敏，直接返回
            return ResponseData.ok(true, contractCollectPaymentPlanAndBaseVO);
        }

    }


    /**
     * @Description: 分页查询
     * @Param: [queryContractCollectPaymentPlanToPageDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/19
     */

    @ResponseBody
    @PostMapping(value = "/queryToPage")
    public ResponseData queryToPage(@Validated @RequestBody ContractCollectPaymentPlanQueryTOPageDTO queryContractCollectPaymentPlanToPageDTO) {
        log.info("ContractCollectPaymentPlanController.queryContractCollectPaymentPlanToPage {}", queryContractCollectPaymentPlanToPageDTO);

        TableResultUtil tableResultUtil = contractCollectPaymentPlanService.queryToPage(queryContractCollectPaymentPlanToPageDTO);

        return ResponseData.ok(true, tableResultUtil);
    }


}
