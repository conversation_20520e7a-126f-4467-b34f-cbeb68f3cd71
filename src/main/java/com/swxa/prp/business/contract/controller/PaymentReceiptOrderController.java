package com.swxa.prp.business.contract.controller;

import com.swxa.prp.business.contract.dto.*;
import com.swxa.prp.business.contract.service.PaymentReceiptOrderService;
import com.swxa.prp.business.contract.vo.PaymentReceiptOrderVO;
import com.swxa.prp.datamask.SwDeSensitive;
import com.swxa.prp.util.ResponseData;
import com.swxa.prp.util.TableResultUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * @Description: 回款单
 * @Author: zhangweicheng
 * @Date: 2025/8/22
 */


@Controller
@RequestMapping("/receipt")
public class PaymentReceiptOrderController {

    @Autowired
    private PaymentReceiptOrderService crudService;

    private static final Logger log = LoggerFactory.getLogger("dayLogger");


    /**
     * @Description: 添加
     * @Param: [insertDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/8/22
     */

    @ResponseBody
    @PostMapping(value = "/add")
    public ResponseData add(@Validated @RequestBody PaymentReceiptOrderInsertDTO insertDTO) {
        crudService.insertData(insertDTO);
        return ResponseData.ok();
    }


    /**
     * :根据id删除
     */
    @ResponseBody
    @PostMapping(value = "/deleteById")
    public ResponseData deleteById(@Validated @RequestBody PaymentReceiptOrderIdDTO deleteDTO) {
        crudService.deleteByID(deleteDTO);
        return ResponseData.ok();
    }

    /**
     * : 修改
     */
    @ResponseBody
    @PostMapping(value = "/update")
    public ResponseData update(@Validated @RequestBody PaymentReceiptOrderUpdateDTO updateDTO) {
        crudService.updateByID(updateDTO);
        return ResponseData.ok();
    }

    /**
     * :查询列表
     */

    @ResponseBody
    @PostMapping(value = "/queryList")
    public ResponseData queryList(@Validated @RequestBody PaymentReceiptOrderQueryDTO queryDTO) {
        List<PaymentReceiptOrderVO> demoLdVOS = crudService.queryList(queryDTO);
        return ResponseData.ok(true, demoLdVOS);
    }

    /**
     * :分页查询
     */
    @ResponseBody
    @PostMapping(value = "/queryToPage")
    public ResponseData queryToPage(@Validated @RequestBody PaymentReceiptOrderQueryPageDTO queryPageDTO) {
        log.info("");
        TableResultUtil resultUtil = crudService.queryToPage(queryPageDTO);
        return ResponseData.ok(true, resultUtil);
    }


    /**
     * 根据ID查询
     */
    @ResponseBody
    @PostMapping(value = "/queryById")
    public ResponseData queryById(@Validated @RequestBody PaymentReceiptOrderIdDTO deleteDTO) {
        log.info("");
        PaymentReceiptOrderVO paymentReceiptOrderVO = crudService.queryByIdCode(deleteDTO);

        String filedName=deleteDTO.getFiledName();

        if (!deleteDTO.getIsMask()) {
            // 不脱敏，返回明文
            if (StringUtils.isNotBlank(filedName)) {
                // 指定了非脱敏字段，只处理这个字段值，并返回
                String fieldNomaskVal= SwDeSensitive.decryptSensitive(paymentReceiptOrderVO,filedName);
                return ResponseData.ok(true, fieldNomaskVal);
            } else {
                // 处理所有的脱敏字段值，返回整个对象
                return ResponseData.ok(true, SwDeSensitive.removeSensitive(paymentReceiptOrderVO));
            }
        } else {
            //脱敏，直接返回
            return ResponseData.ok(true, paymentReceiptOrderVO);
        }

    }


    /**
     *1.这个controller里面的接口定义到yapi上
     * 2.列表查询和分页查询增加条件：客户名称  主题选择；
     * 3.高级查询定义
     */

}
