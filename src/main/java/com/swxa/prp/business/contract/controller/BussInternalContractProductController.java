package com.swxa.prp.business.contract.controller;

import com.swxa.prp.business.contract.dto.*;
import com.swxa.prp.business.contract.service.BussInteralContractProductService;
import com.swxa.prp.business.contract.vo.BussInternalContractProductVO;
import com.swxa.prp.util.ResponseData;
import com.swxa.prp.util.TableResultUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * @Description: 内部合同产品表处理接口
 * @Author: zhangweicheng
 * @Date: 2025/7/21
 */

@Controller
@RequestMapping("/internal/contract/product")
public class BussInternalContractProductController {

    @Autowired
    private BussInteralContractProductService bussInteralContractProductService;

    private static final Logger log = LoggerFactory.getLogger("dayLogger");

    /**
     * @Description: 增加内部合同产品信息
     * @Param: [addBussInternalContractProductDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/21
     */

    @ResponseBody
    @PostMapping(value = "/add")
    public ResponseData addContractProduct(@Validated @RequestBody AddBussInternalContractProductDTO addBussInternalContractProductDTO) {

        log.info("BussInternalContractProductController.addContractProduct {}", addBussInternalContractProductDTO);

        bussInteralContractProductService.add(addBussInternalContractProductDTO);

        return ResponseData.ok();
    }

    /**
     * @Description: 删除指定id的内部合同产品信息
     * @Param: [deleteBussInternalContractProductDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/21
     */


    @ResponseBody
    @PostMapping(value = "/deleteById")
    public ResponseData deleteContractProductById(@Validated @RequestBody DeleteBussInternalContractProductDTO deleteBussInternalContractProductDTO) {

        log.info("BussInternalContractProductController.deleteContractProductById {}", deleteBussInternalContractProductDTO);

        bussInteralContractProductService.deleteById(deleteBussInternalContractProductDTO);

        return ResponseData.ok();
    }

    /**
     * @Description: 修改内部合同产品信息
     * @Param: [updateBussInternalContractProductDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/21
     */

    @ResponseBody
    @PostMapping(value = "/update")
    public ResponseData updateContractProductById(@Validated @RequestBody UpdateBussInternalContractProductDTO updateBussInternalContractProductDTO) {

        log.info("BussInternalContractProductController.updateContractProductById {}", updateBussInternalContractProductDTO);

        bussInteralContractProductService.updateById(updateBussInternalContractProductDTO);

        return ResponseData.ok();
    }

    /**
     * @Description: 根据ID查询内部合同产品信息
     * @Param: [queryBussInternalContractProductDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/21
     */


    @ResponseBody
    @PostMapping(value = "/queryById")
    public ResponseData queryContractProductById(@Validated @RequestBody QueryBussInternalContractProductDTO queryBussInternalContractProductDTO) {

        log.info("BussInternalContractProductController.queryContractProductById {}", queryBussInternalContractProductDTO);

        BussInternalContractProductVO bussInternalContractProductVO = bussInteralContractProductService.queryById(queryBussInternalContractProductDTO);

        return ResponseData.ok(true, bussInternalContractProductVO);
    }


    /**
     * @Description: 分页查询
     * @Param: [queryBussInternalContractProductToPageDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/21
     */

    @ResponseBody
    @PostMapping(value = "/queryToPage")
    public ResponseData queryContractProductToPage(@Validated @RequestBody QueryBussInternalContractProductToPageDTO queryBussInternalContractProductToPageDTO) {

        log.info("BussInternalContractProductController.queryContractProductToPage {}", queryBussInternalContractProductToPageDTO);

        TableResultUtil tableResultUtil = bussInteralContractProductService.queryToPage(queryBussInternalContractProductToPageDTO);

        return ResponseData.ok(true, tableResultUtil);
    }

}
