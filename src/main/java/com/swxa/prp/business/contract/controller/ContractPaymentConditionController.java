package com.swxa.prp.business.contract.controller;

import com.swxa.prp.business.contract.dto.*;
import com.swxa.prp.business.contract.model.ContractPaymentConditionPO;
import com.swxa.prp.business.contract.service.ContractPaymentConditionService;
import com.swxa.prp.business.contract.vo.ContractPaymentConditionVO;
import com.swxa.prp.util.ResponseData;
import com.swxa.prp.util.TableResultUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * @Description: 合同订单付款条件处理接口
 * @Author: zhangweicheng
 * @Date: 2025/7/19
 */

@Controller
@RequestMapping("/contract/payment/condition")
public class ContractPaymentConditionController {

    @Autowired
    private ContractPaymentConditionService contractPaymentConditioService;

    private static final Logger log = LoggerFactory.getLogger("dayLogger");

    /**
     * @Description: 添加
     * @Param: [addContractPaymentCondition]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/19
     */

    @ResponseBody
    @PostMapping(value = "/insert")
    public ResponseData addContractPaymentConditio(@Validated @RequestBody ContractPaymentConditionAddDTO addContractPaymentCondition) {

        log.info("ContractPaymentConditioController.addContractPaymentConditio {}", addContractPaymentCondition);

        contractPaymentConditioService.insert(addContractPaymentCondition);

        return ResponseData.ok();
    }

    /**
     * @Description: 删除
     * @Param: [deleteContractPaymentCondition]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/19
     */


    @ResponseBody
    @PostMapping(value = "/deleteById")
    public ResponseData deleteContractPaymentCondition(@Validated @RequestBody ContractPaymentConditionDeleteDTO deleteContractPaymentCondition) {

        log.info("ContractPaymentConditionController.deleteContractPaymentCondition {}", deleteContractPaymentCondition);

        contractPaymentConditioService.deleteById(deleteContractPaymentCondition);

        return ResponseData.ok();
    }

    /**
     * @Description: 修改
     * @Param: [updateContractPaymentCondition]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/19
     */

    @ResponseBody
    @PostMapping(value = "/updateById")
    public ResponseData updateById(@Validated @RequestBody ContractPaymentConditionUpdateDTO updateContractPaymentCondition) {

        log.info("ContractPaymentConditionController.updateContractPaymentConditionById {}", updateContractPaymentCondition);

        contractPaymentConditioService.updateById(updateContractPaymentCondition);

        return ResponseData.ok();
    }

    /**
     * @Description: 查询
     * @Param: [queryContractPaymentCondition]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/19
     */


    @ResponseBody
    @PostMapping(value = "/queryById")
    public ResponseData queryById(@Validated @RequestBody ContractPaymentConditionQueryDTO queryContractPaymentCondition) {

        log.info("ContractPaymentConditionController.queryContractPaymentConditionById {}", queryContractPaymentCondition);

        ContractPaymentConditionVO contractPaymentConditionVO = contractPaymentConditioService.queryById(queryContractPaymentCondition);

        return ResponseData.ok(true, contractPaymentConditionVO);
    }

    /**
    * @Description: 根据合同id查询付款条件信息列表
    * @Param: [queryContractPaymentCondition]
    * @return: com.swxa.prp.util.ResponseData
    * @Author: lwei
    * @Date: 2025/7/21
    */
    @ResponseBody
    @PostMapping(value = "/queryListByContractId")
    public ResponseData queryListByContractId(@Validated @RequestBody ContractPaymentConditionQueryListDTO contractPaymentConditionQueryListDTO) {

        log.info("ContractPaymentConditionController.queryListByContractId {}", contractPaymentConditionQueryListDTO);

        List<ContractPaymentConditionVO> contractPaymentConditionVOs = contractPaymentConditioService.queryList(contractPaymentConditionQueryListDTO);

        return ResponseData.ok(true, contractPaymentConditionVOs);
    }


    /**
     * @Description:分页查询
     * @Param: [queryContractPaymentConditionToPageDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/19
     */

    @ResponseBody
    @PostMapping(value = "/queryToPage")
    public ResponseData queryToPage(@Validated @RequestBody ContractPaymentConditionQueryPageDTO queryContractPaymentConditionToPageDTO) {

        log.info("ContractPaymentConditionController.queryContractPaymentConditionToPage {}", queryContractPaymentConditionToPageDTO);

        TableResultUtil tableResultUtil = contractPaymentConditioService.queryToPage(queryContractPaymentConditionToPageDTO);

        return ResponseData.ok(true, tableResultUtil);
    }

}
