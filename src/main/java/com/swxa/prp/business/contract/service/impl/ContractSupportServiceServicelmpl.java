package com.swxa.prp.business.contract.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.ISelect;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.swxa.prp.business.contract.dao.ContractSupportServiceMapper;
import com.swxa.prp.business.contract.dto.*;
import com.swxa.prp.business.contract.model.ContractSupportServicePO;
import com.swxa.prp.business.contract.service.ContractSupportServiceService;
import com.swxa.prp.business.contract.vo.ContractSupportServiceVO;
import com.swxa.prp.business.contract.vo.InternalContractServiceVO;
import com.swxa.prp.constant.SwErrorCodeConstant;
import com.swxa.prp.exception.SwPrpException;
import com.swxa.prp.util.MyUtil;
import com.swxa.prp.util.TableResultUtil;
import com.swxa.prp.util.UserUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 合同订单维保服务业务逻辑层实现
 * @Author: zhangweicheng
 * @Date: 2025/7/19
 */

@Service
public class ContractSupportServiceServicelmpl implements ContractSupportServiceService {

    @Autowired
    private ContractSupportServiceMapper contractSupportServiceMapper;

    private static final Logger log = LoggerFactory.getLogger("dayLogger");


    @Transactional
    @Override
    public void insert(ContractSupportServiceAddDTO contractSupportServiceAddDTO) {

        log.info("添加合同订单，{}", contractSupportServiceAddDTO);

        ContractSupportServicePO contractSupportServicePO = new ContractSupportServicePO();

        BeanUtils.copyProperties(contractSupportServiceAddDTO, contractSupportServicePO);

        //设置值
        contractSupportServicePO.setId(MyUtil.getRandomID());
        contractSupportServicePO.setCreateTime(new Date());
        contractSupportServicePO.setUpdateTime(new Date());
        contractSupportServicePO.setCreateId(UserUtil.getLoginUserId());

        //添加
        contractSupportServiceMapper.insertSelective(contractSupportServicePO);

    }


    @Override
    @Transactional
    public void deleteById(ContractSupportServiceDeleteDTO contractSupportServiceDeleteDTO) {


        //获取用户id
        String id = contractSupportServiceDeleteDTO.getId();
        if (Objects.isNull(id)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_ID_NULL);
        }

        //删除
        contractSupportServiceMapper.deleteByPrimaryKey(id);

    }


    @Override
    @Transactional
    public void updateById(ContractSupportServiceEditDTO contractSupportServiceEditDTO) {


        ContractSupportServicePO contractSupportServicePO = new ContractSupportServicePO();

        String id = contractSupportServiceEditDTO.getId();
        if (Objects.isNull(id)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_ID_NULL);
        }

        //赋值
        BeanUtils.copyProperties(contractSupportServiceEditDTO, contractSupportServicePO);

        contractSupportServicePO.setId(id);

        contractSupportServiceMapper.updateByPrimaryKeySelective(contractSupportServicePO);
    }


    @Override
    public List<ContractSupportServiceVO> queryList(ContractSupportServiceQueryListDTO contractSupportServiceQueryListDTO) {


        //列表查询
        List<ContractSupportServicePO> contractOrderPOS = contractSupportServiceMapper.selectList(contractSupportServiceQueryListDTO);

        List<ContractSupportServiceVO> resListVOS = new ArrayList<>();

        //不为空处理返回结果
        if (CollUtil.isNotEmpty(contractOrderPOS)) {
            for (ContractSupportServicePO contractSupportServicePO : contractOrderPOS) {
                ContractSupportServiceVO contractSupportServiceVO = new ContractSupportServiceVO();
                BeanUtils.copyProperties(contractSupportServicePO, contractSupportServiceVO);
                resListVOS.add(contractSupportServiceVO);
            }
        }

        return resListVOS;
    }


    @Override
    public TableResultUtil queryToPage(ContractSupportServiceQueryPageDTO contractSupportServiceQueryPageDTO) {

        //分页查询
        PageInfo<InternalContractServiceVO> pageInfo = PageHelper.startPage(contractSupportServiceQueryPageDTO.getPageNum(), contractSupportServiceQueryPageDTO.getPageSize()).doSelectPageInfo(new ISelect() {

            @Override
            public void doSelect() {
                ContractSupportServiceQueryListDTO contractSupportServiceQueryListDTO = new ContractSupportServiceQueryListDTO();

                //设置各种赋值条件
                queryList(contractSupportServiceQueryListDTO);
            }
        });

        return TableResultUtil.buildTableResult(pageInfo);
    }

    @Override
    public void deleteByContractId(String contractId) {
        if (StringUtils.isBlank(contractId)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_CONTRACT_ID_NULL);
        }
        //根据合同的id查询出所有的信息来
        ContractSupportServiceQueryListDTO contractSupportServiceQueryListDTO = new ContractSupportServiceQueryListDTO();
        contractSupportServiceQueryListDTO.setContractId(contractId);
        List<ContractSupportServiceVO> contractSupportServiceVOS = queryList(contractSupportServiceQueryListDTO);
        if (CollUtil.isNotEmpty(contractSupportServiceVOS)) {
            for (ContractSupportServiceVO contractSupportServiceVO : contractSupportServiceVOS) {
                //删除
                contractSupportServiceMapper.deleteByPrimaryKey(contractSupportServiceVO.getId());
            }
        }
    }


}
