package com.swxa.prp.business.contract.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.swxa.prp.annotation.ExcelColumn;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> @Date 2025-08-07
 */
@Data
@TableName("t_buss_internal_contract_product")
public class BussInternalContractProductPO {

	//生产批号
	@ExcelColumn(name = "生产编号", exportIndex = 2)
	private String productionBatchNumber;
	//序列号
	@ExcelColumn(name = "序列号", exportIndex = 3)
	private String productionSerialNumber;

	/**
	 * 主键ID
	 */
	private String id;

	/**
	 * 内部合同id
	 */
	@ExcelColumn(name = "内部合同编码", exportIndex = 0)
	private String internalContractId;

	/**
	 * U9编码
	 */
	@ExcelColumn(name = "U9编码", exportIndex = 1)
	private String unineCode;

	/**
	 * 产品名称
	 */
	@ExcelColumn(name = "合同产品名称", exportIndex = 4)
	private String productName;

	/**
	 * 产品型号
	 */
	@ExcelColumn(name = "合同产品型号", exportIndex = 5)
	private String productModel;

	/**
	 * 产品编码
	 */
	@ExcelColumn(name = "产品编码", exportIndex = 6)
	private String productCode;

	/**
	 * 产品数量
	 */
	@ExcelColumn(name = "数量", exportIndex = 7)

	private Integer productNum;

	/**
	 * 产品税率
	 */
	@ExcelColumn(name = "税率", exportIndex = 7)
	private Double productTax;

	/**
	 * 应付金额   ,必选
	 */

	private BigDecimal paymentCount;

	/**
	 * 含税单价 , 含税单价  必选  decimal(16，6)
	 */
	@ExcelColumn(name = "含税单价", exportIndex = 8)
	private BigDecimal unitTaxPrice;

	/**
	 * 不含税单价  ,不含税单价  必选  decimal(16，6)
	 */
	@ExcelColumn(name = "不含税单价", exportIndex = 9)
	private BigDecimal noUnitTaxPrice;

	/**
	 * 价税合计  ,价税合计  必选  decimal(16，6)
	 */
	@ExcelColumn(name = "税价合计", exportIndex = 10)
	private BigDecimal totalAmountPrice;

	/**
	 * 不含税金额  ,不含税金额  必选  decimal(16，6)
	 */
	@ExcelColumn(name = "不含税合计", exportIndex = 11)
	private BigDecimal noTotalAmountPrice;

	/**
	 * 税额  ,税额  必选  decimal(16，6)
	 */
	@ExcelColumn(name = "税额", exportIndex = 12)
	private BigDecimal taxPrice;

	/**
	 * 软件是否可退税  , 软件是否可退税   必选  下拉是/否  字符串 长度20
	 */
	@ExcelColumn(name = "软件是否可退税", exportIndex = 13)
	private String softwareTaxRefund;

	/**
	 * 备注
	 */
	@ExcelColumn(name = "备注", exportIndex = 14)
	private String remark;

	/**
	 * 源单类型
	 */
	private String contractSourceOrderType;

	/**
	 * 源单合同编码
	 */
	private String contractSourceOrderNum;

	/**
	 * 创建者ID
	 */
	private String createId;

	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 更新时间
	 */
	private Date updateTime;

}