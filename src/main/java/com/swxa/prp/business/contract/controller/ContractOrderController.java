package com.swxa.prp.business.contract.controller;

import com.swxa.prp.business.contract.dto.*;
import com.swxa.prp.business.contract.service.ContractOrderService;
import com.swxa.prp.business.contract.vo.ContractOrderAllLinkInfoVO;
import com.swxa.prp.business.contract.vo.ContractOrderVO;
import com.swxa.prp.datamask.SwDeSensitive;
import com.swxa.prp.util.ResponseData;
import com.swxa.prp.util.TableResultUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * @Description: 合同订单
 * @Author: zhangweicheng
 * @Date: 2025/7/18
 */

@Controller
@RequestMapping("/contract/order")
public class ContractOrderController {


    @Autowired
    private ContractOrderService contractOrderService;

    private static final Logger log = LoggerFactory.getLogger("dayLogger");


    /**
     * @Description: 增加合同订单，前段添加
     * @Param: [contractOrderFrontAddDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/20
     */

    @ResponseBody
    @PostMapping(value = "/add")
    public ResponseData add(@Validated @RequestBody ContractOrderFrontAddDTO contractOrderFrontAddDTO) {
        contractOrderService.addContractOrder(contractOrderFrontAddDTO);
        return ResponseData.ok();
    }


    /**
     * @Description:更新合同订单，前段调用
     * @Param: [contractOrderUpdateDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/18
     */

    @ResponseBody
    @PostMapping(value = "/update")
    public ResponseData update(@Validated @RequestBody ContractOrderFrontUpdateDTO contractOrderFrontUpdateDTO) {
        log.info("更新");
        contractOrderService.updateContractOrder(contractOrderFrontUpdateDTO);
        return ResponseData.ok();
    }


    /**
     * @Description: 列表查询
     * @Param: [contractOrderQueryListDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/18
     */


    @ResponseBody
    @PostMapping(value = "/queryList")
    public ResponseData queryList(@Validated @RequestBody ContractOrderQueryListDTO contractOrderQueryListDTO) {

        List<ContractOrderVO> internalContractServiceVOS = contractOrderService.queryList(contractOrderQueryListDTO);

        return ResponseData.ok(true, internalContractServiceVOS);
    }

    /**
     * @Description: 分页查询
     * @Param: [internalContractQueryPageDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/18
     */

    @ResponseBody
    @PostMapping(value = "/queryToPage")
    public ResponseData queryToPage(@Validated @RequestBody ContractOrderQueryPageDTO contractOrderQueryPageDTO) {

        TableResultUtil resultUtil = contractOrderService.queryToPage(contractOrderQueryPageDTO);

        return ResponseData.ok(true, resultUtil);
    }


    /**
     * @Description: 查询案例合同，不区分数据授权，只区分查询
     * @Param: [contractOrderQueryPageDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/9/24
     */

    @ResponseBody
    @PostMapping(value = "/queryCaseToPage")
    public ResponseData queryCaseToPage(@Validated @RequestBody ContractOrderQueryPageDTO contractOrderQueryPageDTO) {

        TableResultUtil resultUtil = contractOrderService.queryCaseToPage(contractOrderQueryPageDTO);

        return ResponseData.ok(true, resultUtil);
    }

    /**
     * @Description: 根据ID删除关系
     * @Param: [contractOrderDeleteDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/18
     */

    @ResponseBody
    @PostMapping(value = "/deleteById")
    public ResponseData deletePersonById(@Validated @RequestBody ContractOrderDeleteDTO contractOrderDeleteDTO) {
        contractOrderService.deleteById(contractOrderDeleteDTO);
        return ResponseData.ok();
    }


    /**
     * @Description: 根据合同id查询合同信息
     * @Param: [contractOrderQueryDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: lwei
     * @Date: 2025/7/21
     */
    @ResponseBody
    @PostMapping(value = "/queryById")
    public ResponseData queryById(@Validated @RequestBody ContractOrderQueryDTO contractOrderQueryDTO) {
        ContractOrderAllLinkInfoVO contractOrderAllLinkInfoVO = contractOrderService.queryByIdCode(contractOrderQueryDTO);

        String filedName = contractOrderQueryDTO.getFiledName();

        if (!contractOrderQueryDTO.getIsMask()) {
            // 不脱敏，返回明文
            if (StringUtils.isNotBlank(filedName)) {
                // 指定了非脱敏字段，只处理这个字段值，并返回
                String fieldNomaskVal = SwDeSensitive.decryptSensitive(contractOrderAllLinkInfoVO, filedName);
                return ResponseData.ok(true, fieldNomaskVal);
            } else {
                // 处理所有的脱敏字段值，返回整个对象
                return ResponseData.ok(true, SwDeSensitive.removeSensitive(contractOrderAllLinkInfoVO));
            }
        } else {
            //脱敏，直接返回
            return ResponseData.ok(true, contractOrderAllLinkInfoVO);
        }
    }


    /**
     * @Description: 分页查询，基本信息+产品信息
     * @Param: [internalContractQueryPageDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/7/18
     */

    @ResponseBody
    @PostMapping(value = "/queryBaseAndProdToPage")
    public ResponseData queryBaseAndProdToPage(@Validated @RequestBody ContractOrderQueryPageDTO contractOrderQueryPageDTO) {

        TableResultUtil resultUtil = contractOrderService.queryBaseAndProdToPage(contractOrderQueryPageDTO);

        return ResponseData.ok(true, resultUtil);
    }


/*    @ResponseBody
    @PostMapping(value = "/exportDataToExcelFile")
    public ResponseData exportDataToExcelFile(@Validated @RequestBody ContractExportDataToExcelFileDTO contractExportDataToExcelFileDTO) {

        ContractExportDataToExcelFileVO contractExportDataToExcelFileVO = contractOrderService.exportDataToExcelFile(contractExportDataToExcelFileDTO);

        return ResponseData.ok(true, contractExportDataToExcelFileVO);
    }*/
}
