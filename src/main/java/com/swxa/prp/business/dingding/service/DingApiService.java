package com.swxa.prp.business.dingding.service;

import com.swxa.prp.business.dingding.dto.GetTokenResponse;
import com.swxa.prp.business.dingding.dto.NoticeRequest;
import com.swxa.prp.business.dingding.dto.NoticeResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.List;

@Service
public class DingApiService {
    @Resource
    RestTemplate restTemplate;

    public static final String GET_TOKEN = "https://oapi.dingtalk.com/gettoken";

    public static final String NOTICE = "https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2";

    @Cacheable(cacheNames = "ding-access-token", key = "appKey")
    public String getAccessToken(String appKey, String appsecret) {
        GetTokenResponse response = restTemplate.getForObject(GET_TOKEN + "?appkey=" + appKey + "&appsecret=" + appsecret, GetTokenResponse.class);
        return response.getAccess_token();
    }

    public String getDefaultAccessToken() {
        String appKey = "dingedjbkr86vfmqavtt";
        String appsecret = "mZzqPwSIeCnblltrcdgXVRP3AURNJzXjLwucPG6WByj7ybqM91m6hrQY7CSCRNVd";
        return getAccessToken(appKey, appsecret);
    }

    public void sendNotice(String message, List<String> dingIds) {
        String accessToken = getDefaultAccessToken();

        NoticeRequest request = new NoticeRequest();
        NoticeRequest.Msg msg = new NoticeRequest.Msg();
        NoticeRequest.Msg.Text text = new NoticeRequest.Msg.Text();

        text.setContent(message);

        msg.setText(text);
        msg.setMsgtype("text");

        request.setMsg(msg);
        request.setTo_all_user("false");
        request.setAgent_id("2680113958");
//        request.setDept_id_list("423316540");
//        request.setUserid_list("022321093141858772");
        String userIds = StringUtils.join(dingIds, ",");
        request.setUserid_list(userIds);

        restTemplate.postForObject(NOTICE + "?access_token=" + accessToken, request, NoticeResponse.class);
    }


   //  01270126515124441015


}
