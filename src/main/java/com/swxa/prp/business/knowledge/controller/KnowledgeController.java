package com.swxa.prp.business.knowledge.controller;

import com.swxa.prp.business.knowledge.request.*;
import com.swxa.prp.business.knowledge.response.KnowledgeWithFileVO;
import com.swxa.prp.business.knowledge.service.KnowledgeService;
import com.swxa.prp.exception.SwPrpException;
import com.swxa.prp.util.AssignedUserTransUtils;
import com.swxa.prp.util.ExcelUtils;
import com.swxa.prp.util.ResponseData;
import com.swxa.prp.util.TableResultUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.support.StandardServletMultipartResolver;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * 知识管理列表
 */
@RestController
@RequestMapping("/knowledge")
public class KnowledgeController {


	@Resource
	private KnowledgeService knowledgeService;

	/**
	 * 添加知识列表
	 *
	 * @param knowledgeDTO
	 * @return
	 */
	@RequestMapping("/add")
	public ResponseData add(@RequestBody KnowledgeDTO knowledgeDTO) {

		knowledgeService.add(knowledgeDTO);

		return ResponseData.ok();

	}


	/**
	 * 修改知识列表
	 *
	 * @param knowledgeDTO
	 * @return
	 */
	@RequestMapping("/update")
	public ResponseData update(@RequestBody KnowledgeDTO knowledgeDTO) {

		knowledgeService.update(knowledgeDTO);
		return ResponseData.ok();

	}

	/**
	 * 删除知识列表
	 *
	 * @param knowledgeDTO
	 * @return
	 */
	@RequestMapping("/delete")
	public ResponseData delete(@RequestBody KnowledgeIdsDTO knowledgeDTO) {

		knowledgeService.delete(knowledgeDTO);
		return ResponseData.ok();
	}

	/**
	 * 查询知识列表
	 *
	 * @param knowledgeDTO
	 * @return
	 */
	@RequestMapping("/find")
	public ResponseData find(@RequestBody KnowledgePageDTO knowledgeDTO) {

		TableResultUtil tableResultUtil = knowledgeService.find(knowledgeDTO);
		return ResponseData.ok(true, tableResultUtil);
	}

	@RequestMapping("/findById")
	public ResponseData findById(@RequestBody KnowledgeIdDTO knowledgeDTO) {

		KnowledgeWithFileVO byId = knowledgeService.findById(knowledgeDTO);
		return ResponseData.ok(true, byId);
	}


	/**
	 * 通过Excel导入知识
	 *
	 * @return
	 */
	@PostMapping("/import")
	public ResponseData importByExcel(@Validated @RequestBody MultipartFile file) throws Exception {
		//解析Excel表格
		List<KnowledgeImportDTO> importList = ExcelUtils.readFromFile(file, "知识管理", KnowledgeImportDTO.class);

		//把KnowledgeImportDTO转换成KnowledgeDTO
		List<KnowledgeDTO> knowledgeDTOList = new ArrayList<>();
		for (KnowledgeImportDTO importDTO : importList) {
			KnowledgeDTO item = new KnowledgeDTO();
			item.setNoteNo(importDTO.getNoteNo());
			item.setNotesTitle(importDTO.getNotesTitle());
			item.setFileType(importDTO.getFileType());
			item.setRemark(importDTO.getRemark());
			//根据用户名查询用户ID
			item.setAssignedUserId(AssignedUserTransUtils.getUserIdByName(importDTO.getAssignedUserName()));
			if (StringUtils.isBlank(item.getAssignedUserId())) {
				//FIXME 定义错误码
				throw new SwPrpException("", String.format("用户[%s]不存在", importDTO.getAssignedUserName()));
			}
			knowledgeDTOList.add(item);
		}

		try {
			knowledgeService.multiAdd(knowledgeDTOList);
		} catch (Exception e) {
			throw new SwPrpException(e.getMessage());
		}
		return ResponseData.ok();
	}


}
