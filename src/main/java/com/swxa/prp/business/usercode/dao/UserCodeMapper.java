package com.swxa.prp.business.usercode.dao;


import com.swxa.prp.business.usercode.model.UserCode;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_buss_user_code】的数据库操作Mapper
 * @createDate 2024-06-04 17:21:13
 * @Entity generator.domain.UserCode
 */
@Mapper
public interface UserCodeMapper {

    int deleteByPrimaryKey(Long id);

    int insertSelective(UserCode record);

    UserCode selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UserCode record);

    List<UserCode> query(UserCode record);
}
