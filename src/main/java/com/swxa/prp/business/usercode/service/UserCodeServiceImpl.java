package com.swxa.prp.business.usercode.service;

import com.swxa.prp.business.usercode.model.UserCode;
import com.swxa.prp.business.usercode.dao.UserCodeMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;


@Service
public class UserCodeServiceImpl implements UserCodeService {
    @Resource
    UserCodeMapper userCodeMapper;


    @Override
    public List<UserCode> query(UserCode record) {
        return userCodeMapper.query(record);
    }

    @Override
    public int insertSelective(UserCode record) {
        return userCodeMapper.insertSelective(record);
    }
}
