package com.swxa.prp.business.userinfo.controller;
import com.swxa.prp.business.userinfo.dto.UpdateLoginUserDTO;
import com.swxa.prp.business.userinfo.service.UserInfoService;
import com.swxa.prp.model.SysUserVO;
import com.swxa.prp.business.userinfo.dto.QueryUserByIdNameDTO;
import com.swxa.prp.util.ResponseData;
import com.swxa.prp.util.UserUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/userInfo")
public class UserInfoController {

    @Autowired
    private UserInfoService userInfoService;
    private static final Logger log = LoggerFactory.getLogger("dayLogger");

    
    /**
    * @Description: 获取个人信息
    * @Param: [queryParamDTO]
    * @return: [com.swxa.prp.business.dict.dto.QueryParamDTO]
    * @Author: lidi
    * @Date: 2025/8/26
    */
    @ResponseBody
    @PostMapping(value = "/queryLoginUser")
    public ResponseData queryLoginUser() {

        return ResponseData.ok(true, UserUtil.getLoginUser());
    }


    /** 
    * @Description:  编辑个人信息
    * @Param: 
    * @return: 
    * @Author: lidi
    * @Date: 2025/8/26
    */
    @ResponseBody
    @PostMapping(value = "/updateLoginUser")
    public ResponseData updateLoginUser(@Validated @RequestBody UpdateLoginUserDTO updateLoginUserDTO) {

        log.info("");
        userInfoService.updateLoginUser(updateLoginUserDTO);
        return ResponseData.ok();
    }


    /**
     * @Description: 根据id查询用户信息
     * @Param:
     * @return:
     * @Author: lidi
     * @Date: 2025/9/3
     */
    @ResponseBody
    @PostMapping(value = "/queryUserByIdName")
    public ResponseData queryUserByIdName(  @RequestBody QueryUserByIdNameDTO queryUserByIdNameDTO){

        SysUserVO sysUserVO = userInfoService.queryUserByIdName(queryUserByIdNameDTO);

        return  ResponseData.ok(true, sysUserVO);
    }


}
