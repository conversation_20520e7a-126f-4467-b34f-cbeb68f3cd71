package com.swxa.prp.business.userinfo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.swxa.prp.constant.SwErrorCodeConstant;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class UpdateLoginUserDTO {



    //必须
    @NotNull(message = SwErrorCodeConstant.ERROR_CODE_ID_NULL)
    private Long id;

    /**
     * @Description: 后加新字段
     * @Param:
     * @return:
     * @Author: lidi
     * @Date: 2025/8/8
     */

    private  String dingId;

    private  String dingNo;

    //工号
    private  String employeeId;

    //必须
    //@NotBlank(message = SwErrorCodeConstant.ERROR_USER_NAME_LIKE_NULL_ERROR)
    private String username;//登陆账号 默认邮箱  也可以自定义,注册的人 都默认使用邮箱登录

    //必须
    //@NotBlank(message = SwErrorCodeConstant.ERROR_CODE_0A030002)
    private String nickname;//这个是first name   用户名

    private String lastname;//这个是last name  姓

    //必须
    //@NotNull(message = SwErrorCodeConstant.ERROR_SEX_NULL_ERROR)
    private Integer sex;// 0男1女

    //必须
    //@NotBlank(message = SwErrorCodeConstant.ERROR_CODE_0A030003)
    private String company;// 公司

    //必须
    //@NotBlank(message = SwErrorCodeConstant.ERROR_CODE_DEPARTMENT_NAME_NULL_ERROR)
    private String depName;// 部门
    private String depId;// 部门ID
    private String depSortCode;//所属部门编码
    private Integer departmentLeader;//是否为部门负责人

    private String job;//这个是工作

    //必须
    //@NotBlank(message = SwErrorCodeConstant.ERROR_CODE_0A030004)
    private String email;

    //必须
    //@NotBlank(message = SwErrorCodeConstant.ERROR_PHONE_NULL_ERROR)
    private String phone;

    //必须
    //@NotBlank(message = SwErrorCodeConstant.ERROR_CODE_0A030005)
    private String companyBelong;//这个是国家


    /*************************************************************/

    private String telephone;

    //必须
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthday;

    private String headImgUrl;
    private Integer status;//是否锁定用户  2锁定，1有效

    private String updatePwdFlag;// 是否更新过密码，主要用于prp系统用户初次登陆的时候强制更改密码


    private String roleNames;

    private String roleIdsStr;

    private String userID;//用户身份标识

    //修改密码时间，如果当前时间减去这个时间大于180天，那么就需要重置密码了
    private Date updatePwdDate;



}
