package com.swxa.prp.business.userinfo.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.swxa.prp._enum.RpcAuthConstant;
import com.swxa.prp.business.userinfo.dto.QueryUserByIdNameDTO;
import com.swxa.prp.business.userinfo.dto.UpdateLoginUserDTO;
import com.swxa.prp.business.userinfo.service.UserInfoService;
import com.swxa.prp.constant.SwErrorCodeConstant;
import com.swxa.prp.exception.SwPrpException;
import com.swxa.prp.model.SysUserVO;
import com.swxa.prp.util.HttpUtil;
import com.swxa.prp.util.MyUtil;
import com.swxa.prp.util.ResponseData;
import com.swxa.prp.util.SwEncryptUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class UserInfoServiceImpl implements UserInfoService {


    @Value("${constant.auth.ipAddr}")
    private String authUrl;

    @Override
    public void updateLoginUser(UpdateLoginUserDTO updateLoginUserDTO) {

            String responseData = HttpUtil.sendPostRequestBodySSL(authUrl + RpcAuthConstant.LOGIN_USER_UPDATE, JSONObject.toJSONString(updateLoginUserDTO), MyUtil.getToken());
            ResponseData queryMsg = JSONObject.parseObject(responseData, ResponseData.class);
            if (SwErrorCodeConstant.QUERY_SUCCESS_CODE_0A0A0001.equals(queryMsg.getCode())) {//代表请求成功
                throw new SwPrpException(SwErrorCodeConstant.QUERY_SUCCESS_CODE_0A0A0001);
            }
            else {
                throw new SwPrpException(SwErrorCodeConstant.QUERY_SUCCESS_CODE_0A0A0001_ERROR);
            }

    }


    //根据id username查讯用户信息
    @Override
    public SysUserVO queryUserByIdName(QueryUserByIdNameDTO queryUserByIdNameDTO){

        //判断非空
        Long id = queryUserByIdNameDTO.getId();
        String username= queryUserByIdNameDTO.getUsername();
        if (id != null || username != null) {

            String responseData = HttpUtil.sendPostRequestBodySSL(authUrl + RpcAuthConstant.AUTH_RPC_USER_QUERY_ID_NAME, JSONObject.toJSONString(queryUserByIdNameDTO), MyUtil.getToken());
            ResponseData queryMsg = JSONObject.parseObject(responseData, ResponseData.class);
            if (SwErrorCodeConstant.QUERY_SUCCESS_CODE_0A0A0001.equals(queryMsg.getCode())) {//代表请求成功
                //获取加密信息
                String listInfo = queryMsg.getObject().toString();

                //解密信息
                String decrypt = SwEncryptUtil.decrypt(listInfo);

                SysUserVO result= JSONObject.parseObject(decrypt, SysUserVO.class);

                return result;
            }
            else{
                //防止用户id变化
                return new SysUserVO();

            }
        } else {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_NULL_ERROR);
        }
    }


}
