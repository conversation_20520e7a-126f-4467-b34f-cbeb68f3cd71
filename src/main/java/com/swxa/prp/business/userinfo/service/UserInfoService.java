package com.swxa.prp.business.userinfo.service;

import com.swxa.prp.business.userinfo.dto.UpdateLoginUserDTO;
import com.swxa.prp.model.SysUserVO;
import com.swxa.prp.business.userinfo.dto.QueryUserByIdNameDTO;

public interface UserInfoService {


    void updateLoginUser(UpdateLoginUserDTO updateLoginUserDTO);

    /**
     * @Description: 根据id username查询用户信息
     * @Param:
     * @return:
     * @Author: lidi
     * @Date: 2025/9/3
     */
    SysUserVO queryUserByIdName(QueryUserByIdNameDTO queryUserByIdNameDTO);

}
