package com.swxa.prp.business.customer.vo;

import com.swxa.prp.datamask.SwSensitive;
import lombok.Data;

@Data
public class BussContactVO {

    // 物理主键
    private String id;

    // 联系人编号
    private String contactCode;

    // 客户信息主键
    private String customerId;

    // 联系人姓名
    @SwSensitive(fieldName = "name")
    private String name;

    // 所在部门
    private String department;

    // 职务
    private String position;

    // 性别
    private String sex;

    // 手机号
    @SwSensitive(fieldName = "mobile")
    private String mobile;

    // 电话
    private String tel;

    // 微信
    @SwSensitive(fieldName = "wechat")
    private String wechat;

    // 上级领导
    private String superiorLeader;

    // 是否首要联系人
    private String primaryContactFlag;

    // 是否函证接收人
    private String confirmationReceiverFlag;

    // 接收函证类型
    private String confirmationType;

    // 备注
    private String remark;

    /**
     * 销售部门ID
     */
    private String salesManagerDeptId;

    /**
     * 销售部门
     */
    private String salesManagerDept;

    /**
     * 销售负责人ID
     */
    private String salesManagerId;

    /**
     * 销售负责人
     */
    private String salesManager;

    /**
     * 扩展字段1
     */
    private Integer extend1;

    // 扩展字段2
    private String extend2;

    // 扩展字段3
    private String extend3;

    // 创建人
    private String createBy;

    // 创建时间
    private String createTime;

    // 更新人
    private String updateBy;

    // 更新时间
    private String updateTime;

    @SwSensitive(fieldName = "customerName")
    private String customerName; // 客户名称


    /**
     * 审批流程状态
     *    ("1":"未发起"),
     *    ("2": "运行中"),  //此状态不允许编辑
     *    ("3":"已完成");
     */
    private String bussProcessStatus;
}
