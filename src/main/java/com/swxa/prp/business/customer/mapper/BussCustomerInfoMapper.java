package com.swxa.prp.business.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.swxa.prp.business.customer.entity.BussCustomerInfoPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 联系人数据库操作层
 * @Author: zhangweicheng
 * @Date: 2025/7/22
 */

@Mapper
public interface BussCustomerInfoMapper extends BaseMapper<BussCustomerInfoPO> {

    //删除
    int deleteByPrimaryKey(String id);

    //添加
    int insertSelective(BussCustomerInfoPO row);

    //查询
    BussCustomerInfoPO selectByPrimaryKey(String id);

    //更新
    int updateByPrimaryKeySelective(BussCustomerInfoPO row);

    List<String> selectListBySaleUserId(@Param("idsArray") List<String> ids);

    List<String> selectListByCreateId(@Param("idsArray") List<String> ids);

}