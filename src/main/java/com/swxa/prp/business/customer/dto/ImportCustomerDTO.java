package com.swxa.prp.business.customer.dto;

import com.swxa.prp.annotation.ExcelColumn;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 客户档案导入DTO
 */
@Data
public class ImportCustomerDTO {

    /**
     * 客户编号
     */
    @ExcelColumn(name = "客户编号", exportIndex = 0, require = false)
    private String customerCode;

    /**
     * 公司主体
     */
    @ExcelColumn(name = "公司主体", exportIndex = 1, require = true)
    @NotNull
    private String corporation;

    /**
     * 客户集
     */
    @ExcelColumn(name = "客户集", exportIndex = 2, require = false)
    private String customerGroup;

    /**
     * 客户名称
     */
    @ExcelColumn(name = "客户名称", exportIndex = 3, require = true)
    @NotNull
    private String customerName;

    /**
     * 销售部门
     */
    @ExcelColumn(name = "销售部门", exportIndex = 4, require = false)
    private String salesManagerDept;

    /**
     * 销售负责人
     */
    @ExcelColumn(name = "销售负责人", exportIndex = 5, require = false)
    private String salesManager;

    /**
     * 客户来源
     */
    @ExcelColumn(name = "客户来源", exportIndex = 6, require = false)
    private String customerSource;

    /**
     * 是否为战略客户
     */
    @ExcelColumn(name = "是否为战略客户", exportIndex = 7, require = false)
    private String strategicCustomerFlag;

    /**
     * 资源描述
     */
    @ExcelColumn(name = "资源描述", exportIndex = 8, require = false)
    private String resourceDescription;

    /**
     * 团队成员
     */
    @ExcelColumn(name = "团队成员", exportIndex = 9, require = false)
    private String teamMembers;

    /**
     * 业务说明
     */
    @ExcelColumn(name = "业务说明", exportIndex = 10, require = false)
    private String businessDescription;

    /**
     * 曾用名
     */
    @ExcelColumn(name = "曾用名", exportIndex = 11, require = false)
    private String formerName;

    /**
     * 成交状态
     */
    @ExcelColumn(name = "成交状态", exportIndex = 12, require = false)
    private String transactionState;

    /**
     * 客户级别
     */
    @ExcelColumn(name = "客户级别", exportIndex = 13, require = false)
    private String customerLevel;

    /**
     * 合作领域/行业
     */
    @ExcelColumn(name = "合作领域/行业", exportIndex = 14, require = false)
    private String industry;

    /**
     * 渠道类型
     */
    @ExcelColumn(name = "渠道类型", exportIndex = 15, require = false)
    private String channelType;

    /**
     * 合作价格折扣-硬件
     */
    @ExcelColumn(name = "合作价格折扣-硬件", exportIndex = 16, require = false)
    private Float discountHardware;

    /**
     * 合作价格折扣-软件
     */
    @ExcelColumn(name = "合作价格折扣-软件", exportIndex = 17, require = false)
    private Float discountSoftware;

    /**
     * 渠道协议
     */
    @ExcelColumn(name = "渠道协议", exportIndex = 18, require = false)
    private String channelAgreement;

    /**
     * 协议起始日期
     */
    @ExcelColumn(name = "协议起始日期", exportIndex = 19, require = false)
    private String agreementStart;

    /**
     * 协议截止日期
     */
    @ExcelColumn(name = "协议截止日期", exportIndex = 20, require = false)
    private String agreementEnd;

    /**
     * 是否申请延长协议周期
     */
    @ExcelColumn(name = "是否申请延长协议周期", exportIndex = 21, require = false)
    private String agreementExtendFlag;

    /**
     * 证书编号
     */
    @ExcelColumn(name = "证书编号", exportIndex = 22, require = false)
    private String certificateNumber;

    /**
     * 考核任务额
     */
    @ExcelColumn(name = "考核任务额", exportIndex = 23, require = false)
    private BigDecimal taskAmount;

    /**
     * 跟进情况
     */
    @ExcelColumn(name = "跟进情况", exportIndex = 24, require = false)
    private String followUp;

    /**
     * 注册时间
     */
    @ExcelColumn(name = "注册时间", exportIndex = 25, require = false)
    private String registeredDate;

    /**
     * 注册资金
     */
    @ExcelColumn(name = "注册资金", exportIndex = 26, require = false)
    private BigDecimal registeredCapital;

    /**
     * 注册行业
     */
    @ExcelColumn(name = "注册行业", exportIndex = 27, require = false)
    private String registeredIndustry;

    /**
     * 企业性质
     */
    @ExcelColumn(name = "企业性质", exportIndex = 28, require = false)
    private String enterpriseNature;

    /**
     * 法定代表人
     */
    @ExcelColumn(name = "法定代表人", exportIndex = 29, require = false)
    private String legalRepresentative;

    /**
     * 官网网址
     */
    @ExcelColumn(name = "官网网址", exportIndex = 30, require = false)
    private String officialWebsite;

    /**
     * 人员规模
     */
    @ExcelColumn(name = "人员规模", exportIndex = 31, require = false)
    private String staffSize;

    /**
     * 社保人数
     */
    @ExcelColumn(name = "社保人数", exportIndex = 32, require = false)
    private Integer socialSecurityNumber;

    /**
     * 经营范围
     */
    @ExcelColumn(name = "经营范围", exportIndex = 33, require = false)
    private String businessScope;

    /**
     * 公司Email
     */
    @ExcelColumn(name = "公司Email", exportIndex = 34, require = false)
    private String companyEmail;

    /**
     * 公司电话
     */
    @ExcelColumn(name = "公司电话", exportIndex = 35, require = false)
    private String companyTel;

    /**
     * 联系人姓名
     */
    @ExcelColumn(name = "联系人姓名", exportIndex = 36, require = false)
    private String name;

    /**
     * 联系人编号
     */
    @ExcelColumn(name = "联系人编号", exportIndex = 37, require = false)
    private String contactCode;

    /**
     * 职务
     */
    @ExcelColumn(name = "职务", exportIndex = 38, require = false)
    private String position;

    /**
     * 手机号码
     */
    @ExcelColumn(name = "手机号码", exportIndex = 39, require = false)
    private String mobile;

    /**
     * 联系人电话
     */
    @ExcelColumn(name = "联系人电话", exportIndex = 40, require = false)
    private String tel;

    /**
     * 微信
     */
    @ExcelColumn(name = "微信", exportIndex = 41, require = false)
    private String wechat;

    /**
     * 部门
     */
    @ExcelColumn(name = "部门", exportIndex = 42, require = false)
    private String department;

    /**
     * 国家
     */
    @ExcelColumn(name = "国家", exportIndex = 43, require = false)
    private String country;

    /**
     * 省份
     */
    @ExcelColumn(name = "省份", exportIndex = 44, require = false)
    private String province;

    /**
     * 城市
     */
    @ExcelColumn(name = "城市", exportIndex = 45, require = false)
    private String city;

    /**
     * 区域
     */
    @ExcelColumn(name = "区域", exportIndex = 46, require = false)
    private String region;

    /**
     * 详细地址
     */
    @ExcelColumn(name = "详细地址", exportIndex = 47, require = false)
    private String address;

    /**
     * 开票公司名称
     */
    @ExcelColumn(name = "开票公司名称", exportIndex = 48, require = false)
    private String companyName;

    /**
     * 开户行
     */
    @ExcelColumn(name = "开户行", exportIndex = 49, require = false)
    private String bankName;

    /**
     * 开票电话
     */
    @ExcelColumn(name = "开票电话", exportIndex = 50, require = false)
    private String phoneNumber;

    /**
     * 纳税人资质
     */
    @ExcelColumn(name = "纳税人资质", exportIndex = 51, require = false)
    private String taxpayerType;

    /**
     * 纳税人识别号
     */
    @ExcelColumn(name = "纳税人识别号", exportIndex = 52, require = false)
    private String taxpayerId;

    /**
     * 银行账户
     */
    @ExcelColumn(name = "银行账户", exportIndex = 53, require = false)
    private String bankAccount;

    /**
     * 开票地址
     */
    @ExcelColumn(name = "开票地址", exportIndex = 54, require = false)
    private String invoiceAddress;
}
