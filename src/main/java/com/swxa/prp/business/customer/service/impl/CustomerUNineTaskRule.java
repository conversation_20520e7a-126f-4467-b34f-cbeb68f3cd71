package com.swxa.prp.business.customer.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.swxa.prp.business.customer.entity.BussCustomerInfoPO;
import com.swxa.prp.business.customer.mapper.BussCustomerInfoMapper;
import com.swxa.prp.business.customer.model.UNineCustomerReq;
import com.swxa.prp.business.customer.model.UNineCustomerRes;
import com.swxa.prp.business.customer.model.UNineCustomerSiteDTO;
import com.swxa.prp.business.front.fileservice.async.rule.AsyncTaskRule;
import com.swxa.prp.business.unine.service.UNineOrgCodeService;
import com.swxa.prp.exception.SwPrpException;
import com.swxa.prp.util.HttpUtil;
import com.swxa.prp.util.UNineTokenUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.swxa.prp.business.customer.enums.CustomerUNineOrgCodeEnum.PUBLIC_SET;
import static com.swxa.prp.business.shippingnotice.constant.ShippingNoticeErrorCodeConstant.ERROR_CODE_1010F00017;


/**
 * @Description: 异步调用U9 创建客户接口
 * @Author: zhangweicheng
 * @Date: 2023/11/27
 */
@Slf4j
@Component
public class CustomerUNineTaskRule implements AsyncTaskRule {


    @Value ("${constant.u9.ipAddr}")
    private String u9Addr;

    @Value ("${constant.u9.token}")
    private String u9Token;

    @Value ("${constant.u9.customerPath}")
    private String customerPath;

    @Value ("${constant.u9.customerUpdatePath}")
    private String customerUpdatePath;

    @Resource
    private BussCustomerInfoMapper bussCustomerInfoMapper;

    @Autowired
    private UNineOrgCodeService uNineOrgCodeService;

    private BussCustomerInfoPO customerInfo;

    private boolean canUNine; //（更新）是否可以传递U9，用来判断是创建完后的更新还是审批中的未创建的更新

    public CustomerUNineTaskRule() {

    }

    public BussCustomerInfoPO getCustomerInfo() {
        return customerInfo;
    }

    public void setCustomerInfo(BussCustomerInfoPO customerInfo) {
        this.customerInfo = customerInfo;
    }

    public boolean isCanUNine() {
        return canUNine;
    }

    public void setCanUNine(boolean canUNine) {
        this.canUNine = canUNine;
    }

    @Override
    public void start() {
        log.info("begin send customer to unine, customerCode {}", customerInfo.getCustomerCode());

        if (StrUtil.isEmpty(customerInfo.getU9Code()) || canUNine) {
            //不存在U9编码，新建或者是审批中的更新
            if (!canUNine) {
                log.error("U9 customer is not exist, can not update!");
                return;
            }
            String token = getU9Token(PUBLIC_SET.getCode()); //获取授权
            //不存在U9编码，走新建 todo 或者重复调新建接口
            //组装参数报文
            String customerParam = assembleParam(PUBLIC_SET.getCode());
            // 发送U9
            String res = HttpUtil.sendU9PostRequestBody(u9Addr + customerPath, customerParam, token);
            log.info("U9 customer result :{}", res);
            //判断成功，需要使用Data数组里边的IsSuccess来判断，不能使用外部的Success参数。
            JSONObject resultObj = JSONObject.parseObject(res);
            UNineCustomerRes data = resultObj.getJSONArray("Data").getObject(0, UNineCustomerRes.class);
            if (data.isIsSucess()) {
                //执行成功
                customerInfo.setU9Code(String.valueOf(data.getID()));
                customerInfo.setU9Flag("是");
            } else {
                //执行失败
                customerInfo.setU9Error(data.getErrorMsg());
            }
            customerInfo.setU9Time(DateUtil.now());
            customerInfo.setUpdateTime(DateUtil.now());

            bussCustomerInfoMapper.updateByPrimaryKeySelective(customerInfo);
        } else {
            //更新---客户开票信息
            if (updateU9()) {
                //重传全部成功
                customerInfo.setU9Arq("是");
                customerInfo.setU9ArqTime(DateUtil.now());
                customerInfo.setUpdateTime(DateUtil.now());

                bussCustomerInfoMapper.updateByPrimaryKeySelective(customerInfo);
            }

        }


        log.info("end U9 customer ");
    }

    @Override
    public void doSuccess() {
        //
    }

    @Override
    public void doError() {
        //
        log.error("U9 customer is error!");
    }

    private String getU9Token(String orgCode) {
        //todo param参数传入，当前登录用户；根据主体不同选择不同的orgcode；修改测试和正式；
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("userCode", "admin"); //操作者账号
        paramMap.put("entCode", u9Addr.contains("8.38") ? "01" : "02"); //账套代码 ，测试02；正式01
        paramMap.put("orgCode", orgCode); //公司主体代码，创建客户默认100，公共套账
        paramMap.put("clientSecret", "a4a628c616ea4e8b94446b0fd6a269cc"); //应用密钥
        paramMap.put("clientId", "swxa"); //应用id
        String token = UNineTokenUtil.getToken(u9Addr + u9Token, paramMap);
//        log.info("token = {}", token);
        if ("error".equals(token)) {
            log.error("get token error");
            throw new SwPrpException(ERROR_CODE_1010F00017);
        }
        return token;
    }

    // 将 customer  转换为一个 List<UNineCustomerReq> 对象，用于发送给 U9 系统
    private String assembleParam(String orgCode) {
        List<UNineCustomerReq> customerReqs = new ArrayList<>();
        // 客户信息
        UNineCustomerReq req = new UNineCustomerReq();
        req.setCode(customerInfo.getCustomerCode());
        req.setName(customerInfo.getCustomerName());
        req.setShortName(customerInfo.getCustomerName());
        req.setStateTaxNo(StrUtil.isEmpty(customerInfo.getTaxpayerId()) ? "1" : customerInfo.getTaxpayerId());

        // 修改客户开票信息时需要使用，换成不同主体
        UNineCustomerReq.ShippmentRule org = new UNineCustomerReq.ShippmentRule();
        org.setCode(orgCode);
        req.setOrg(org);

        List<UNineCustomerSiteDTO> customerSiteDTOS = new ArrayList<>();
        UNineCustomerSiteDTO customerSite = new UNineCustomerSiteDTO();
        customerSite.setCode(customerInfo.getCustomerCode());
        customerSite.setBuyerNoteName(customerInfo.getCompanyName()); //todo 公司名称？
        customerSite.setBuyerBankAccount(StrUtil.isEmpty(customerInfo.getBankName()) ? "2" : customerInfo.getBankName());
        customerSite.setBuyerBankAccountCode(StrUtil.isEmpty(customerInfo.getBankAccount()) ? "3" : customerInfo.getBankAccount());
        customerSite.setCheckTakerPhoneNo(StrUtil.isEmpty(customerInfo.getPhoneNumber()) ? "4" : customerInfo.getPhoneNumber());
        customerSiteDTOS.add(customerSite);

        req.setCustomerSiteDTOs(customerSiteDTOS);

        customerReqs.add(req);
        return JSONObject.toJSONString(customerReqs);
    }

    private boolean updateU9() {
        //先循环获取每个主体的token，在循环修改每个主体下客户档案
        for (String orgCode : uNineOrgCodeService.getAllOrgCode()) {
            String token = getU9Token(orgCode);
            //组装参数报文
            String customerParam = assembleParam(orgCode);
            // 发送U9---修改客户
            String res = HttpUtil.sendU9PostRequestBody(u9Addr + customerUpdatePath, customerParam, token);
            log.info("U9 org {} customer modify result :{}", uNineOrgCodeService.getValueByCode(orgCode), res);
            //判断成功，需要使用Data数组里边的IsSuccess来判断，不能使用外部的Success参数。
            JSONObject resultObj = JSONObject.parseObject(res);
            JSONArray data1 = resultObj.getJSONArray("Data");
            if (data1.isEmpty()) {
                // 返回的结果里面是空的，天安特别处理
                log.error("U9 org {} modify customer is empty", uNineOrgCodeService.getValueByCode(orgCode));
                return false;
            }
            UNineCustomerRes data = data1.getObject(0, UNineCustomerRes.class);
            if (!data.isIsSucess()) {
                //有主体更新客户失败
                //todo 更新失败还继续吗？？
                log.error("U9 org {} modify customer is error {}", uNineOrgCodeService.getValueByCode(orgCode), data.getErrorMsg());
            }
        }
        return true;
    }

}
