package com.swxa.prp.business.customer.dto;

import com.swxa.prp.business.common.dto.BasePageParamDTO;
import com.swxa.prp.model.dto.AdvanceQueryGroupDTO;
import lombok.Data;

import java.util.List;

@Data
public class QueryContactPageDTO extends BasePageParamDTO {

    // 联系人编号
    private String contactCode;

    // 联系人姓名
    private String name;

    // 查询条件
    private List<AdvanceQueryGroupDTO> advanceQueryGroups;

}
