package com.swxa.prp.business.customer.entity;

import com.swxa.prp.annotation.FieldName;
import com.swxa.prp.business.common.inter.AdvanceQueryInterface;
import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * @ Date：2025-08-20-9:25
 * @ Version：1.0
 * @ Description：客户联系人-高级查询-字段类
 */
@Data
@Component
public class AdvanceQueryContactInfoField implements AdvanceQueryInterface {

    // 联系人编号
    @FieldName (value = "联系人编号")
    private String contactCode;
    // 客户信息主键
    @FieldName (value = "客户信息主键")
    private String customerId;
    // 联系人姓名
    @FieldName (value = "联系人姓名")
    private String name;
    // 所在部门
    @FieldName (value = "所在部门")
    private String department;
    // 职务
    @FieldName (value = "职务")
    private String position;
    // 性别
    @FieldName (value = "性别")
    private String sex;
    // 手机号
    @FieldName (value = "手机号")
    private String mobile;
    // 电话
    @FieldName (value = "电话")
    private String tel;
    // 微信
    @FieldName (value = "微信")
    private String wechat;
    // 上级领导
    @FieldName (value = "上级领导")
    private String superiorLeader;
    // 是否首要联系人
    @FieldName (value = "是否首要联系人")
    private String primaryContactFlag;
    // 是否函证接收人
    @FieldName (value = "是否函证接收人")
    private String confirmationReceiverFlag;
    // 接收函证类型
    @FieldName (value = "接收函证类型")
    private String confirmationType;
    // 备注
    @FieldName (value = "备注")
    private String remark;
    /**
     * 销售部门ID
     */
    @FieldName (value = "销售部门ID")
    private String salesManagerDeptId;
    /**
     * 销售部门
     */
    @FieldName (value = "销售部门")
    private String salesManagerDept;
    /**
     * 销售负责人ID
     */
    @FieldName (value = "销售负责人ID")
    private String salesManagerId;
    /**
     * 销售负责人
     */
    @FieldName (value = "销售负责人")
    private String salesManager;
    /**
     * 创建人
     */
    @FieldName (value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @FieldName (value = "创建时间", type = "time")
    private String createTime;
    /**
     * 更新人
     */
    @FieldName (value = "更新人")
    private String updateBy;
    /**
     * 更新时间
     */
    @FieldName (value = "更新时间", type = "time")
    private String updateTime;

    /**
     * @Description: 获取服务编码-联系人
     */
    @Override
    public String getCode() {
        return "businessContact";
    }

    /**
     * @Description: 获取定义的服务类
     */
    @Override
    public Class getClsaa() {
        return AdvanceQueryContactInfoField.class;
    }

}
