package com.swxa.prp.business.customer.enums;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * U9客户主体 组织代码
 * 后续使用接口获取字段，该类废弃
 */
public enum CustomerUNineOrgCodeEnum {
    PUBLIC_SET("100", "公共账套"),
    SANSEC("101", "三未信安科技股份有限公司"),
    SD_SANSEC("102", "山东三未信安信息科技有限公司"),
    SDDCF("103", "山东多次方半导体有限公司"),
    SH_SANSEC("104", "上海三未信安企业发展有限公司"),
    NJ_SANSEC("105", "南京三未信安信息技术有限公司"),
    XA_SANSEC("106", "西安三未信安信息科技有限公司"),
    SANSEC_GZ("107", "三未信安科技股份有限公司广州分公司"),
    SANSEC_CD("108", "三未信安科技股份有限公司成都分公司"),
    SANSEC_ZZ("109", "三未信安科技股份有限公司郑州分公司"),
    SANSEC_CQ("110", "三未信安科技股份有限公司重庆分公司"),
    SANSEC_CS("111", "三未信安科技股份有限公司长沙分公司"),
    BJSWPY("112", "北京三未普益投资合伙企业（有限合伙）"),
    JNFQYY("113", "济南风起云涌企业管理咨询合伙企业（有限合伙）"),
    GX_SANSEC("114", "广西三未信安信息科技有限公司"),
    CQ_SANSEC("115", "重庆三未信安信息科技有限公司"),
    SANSEC_QM("116", "三未信安科技股份有限公司昆明分公司"),
    KEIYOU("201", "广州江南科友科技股份有限公司"),
    KY_BJ("202", "广州江南科友科技股份有限公司北京分公司"),
    KY_SH("203", "广州江南科友科技股份有限公司上海分公司"),
    KY_HZ("204", "广州江南科友科技股份有限公司杭州分公司"),
    KY_CD("205", "广州江南科友科技股份有限公司成都分公司"),
    KY_SZ("206", "广州江南科友科技股份有限公司深圳分公司"),
    KY_WH("207", "广州江南科友科技股份有限公司武汉分公司"),
    BJ_KY("208", "北京江南科友科技有限公司"),
    JUSTSEC("301", "北京世纪先承信息安全科技有限公司"),
    TASS("401", "北京江南天安科技有限公司"),
    ;

    private String code; //U9组织代码orgCode
    private String orgName; //主体名称

    private CustomerUNineOrgCodeEnum(String code, String orgName) {
        this.code = code;
        this.orgName = orgName;
    }

    public static List<String> codes() {
        List<String> res = new ArrayList<>();
        CustomerUNineOrgCodeEnum[] values = values();
        for (CustomerUNineOrgCodeEnum codeEnum : values) {
            res.add(codeEnum.code);
        }
        return res;
    }

    // 获取枚举值的编码集合除公共
    public static List<String> getCodesExPub() {
        return Arrays.stream(values())
                .map(CustomerUNineOrgCodeEnum::getCode)
                .filter(i -> !i.equals(PUBLIC_SET.code))
                .collect(Collectors.toList());
    }

    //通过传入code，获取对应组织名
    public static String getValueByCode(String code) {
        CustomerUNineOrgCodeEnum[] values = values();
        for (CustomerUNineOrgCodeEnum codeEnum : values) {
            if (codeEnum.code.equals(code)) {
                return codeEnum.orgName;
            }
        }
        return null;
    }

    //通过传入组织名，获取对应组织名
    public static String getCodeByValue(String value) {
    
        for(CustomerUNineOrgCodeEnum en : values()){
            if(en.orgName.equals(value)){
                return en.code;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }
}
