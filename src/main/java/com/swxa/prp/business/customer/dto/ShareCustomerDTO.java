package com.swxa.prp.business.customer.dto;

import com.swxa.prp.constant.SwErrorCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class ShareCustomerDTO {

    @NotEmpty(message = SwErrorCodeConstant.TEAM_MEMBER_LIST_NULL_ERROR)
    private String teamMembers; // 团队成员

    @NotEmpty(message = SwErrorCodeConstant.TEAM_MEMBER_LIST_NULL_ERROR)
    private String teamMembersId; // 团队成员ID

    // 客户信息主键
    @NotEmpty(message = SwErrorCodeConstant.CUSTOMER_ID_NULL_ERROR)
    private String id;
}
