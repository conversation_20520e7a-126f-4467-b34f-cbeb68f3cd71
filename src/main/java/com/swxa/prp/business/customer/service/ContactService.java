package com.swxa.prp.business.customer.service;

import com.swxa.prp.business.act.conditionFields.dto.BussProcessStatusDTO;
import com.swxa.prp.business.customer.dto.*;
import com.swxa.prp.business.customer.vo.BussContactVO;
import com.swxa.prp.util.TableResultUtil;

import java.util.List;

/**
 * @Description:联系人业务逻辑接口
 * @Author: zhangweicheng
 * @Date: 2025/7/22
 */

public interface ContactService {

    /**
     * @Description: 新增
     * @Param: [requestParam]
     * @return: void
     * @Author: zhangweicheng
     * @Date: 2025/7/22
     */

    void add(AddContactDTO requestParam);

    /**
     * 批量新增联系人
     *
     * @param requestParamList 联系人列表
     */
    void batchAdd(List<AddContactDTO> requestParamList);

    /**
     * 根据客户名称查询客户ID
     *
     * @param customerName 客户名称
     * @return 客户ID，未找到返回null
     */
    String findCustomerIdByName(String customerName);

    /**
     * @Description: 分页查询
     * @Param: [requestParam]
     * @return: com.swxa.prp.util.TableResultUtil
     * @Author: zhangweicheng
     * @Date: 2025/7/22
     */

    TableResultUtil queryPage(QueryContactPageDTO requestParam);

    /**
     * @Description: 更新
     * @Param: [requestParam]
     * @return: void
     * @Author: zhangweicheng
     * @Date: 2025/7/22
     */

    void update(UpdateContactDTO requestParam);


    /**
     * @Description: 根据ID删除
     * @Param: [ids]
     * @return: void
     * @Author: zhangweicheng
     * @Date: 2025/7/22
     */

    void delete(String[] ids);

    void importContract(ImportContactDTO requestParam);

    void export(ExportContactDTO requestParam);

    BussContactVO queryById(QueryContactIdDTO requestParam);
    /**
     * @Description: 根据负责人id列表查询对应的记录id列表
     * @Param: [saleUserId]
     * @return: java.util.List<java.lang.String>
     * @Author: lwei
     * @Date: 2025/9/18
     */
    List<String> findBySaleUserIds(List<String> saleUserId);

    /**
     * @Description: 根据创建者id列表，查询对应的记录id列表
     * @Param: [createId]
     * @return: java.util.List<java.lang.String>
     * @Author: lwei
     * @Date: 2025/9/18
     */
    List<String> findByCreateIds(List<String> createId);

    /**
     * @Description: 根据id，更新审批状态
     * @Param: [processStatusDTO]
     * @return: void
     * @Author: lwei
     * @Date: 2025/9/19
     */
    void updateProcessStatusById(BussProcessStatusDTO processStatusDTO);
}
