package com.swxa.prp.business.customer.controller;

import com.swxa.prp.business.customer.dto.*;
import com.swxa.prp.business.customer.service.CustomerService;
import com.swxa.prp.business.customer.vo.BussCustomerVO;
import com.swxa.prp.business.knowledge.request.KnowledgeImportDTO;
import com.swxa.prp.datamask.SwDeSensitive;
import com.swxa.prp.exception.SwPrpException;
import com.swxa.prp.util.AssignedUserTransUtils;
import com.swxa.prp.util.ExcelUtils;
import com.swxa.prp.util.ResponseData;
import com.swxa.prp.util.TableResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.support.StandardServletMultipartResolver;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/customer")
public class CustomerController {

    @Resource
    private CustomerService customerService;

    /**
     * 新增客户档案
     * @param requestParam 入参
     * @return 处理结果
     */
    @PostMapping(value = "/add")
    public ResponseData add(@Validated @RequestBody AddCustomerDTO requestParam) {

        log.info("新增客户档案：{}",requestParam);
        customerService.add(requestParam);

        return ResponseData.ok();
    }

    /**
     * 分页查询客户档案
     * @param requestParam 入参
     * @return 处理结果
     */
    @PostMapping(value = "/page")
    public ResponseData queryPage(@Validated @RequestBody QueryCustomerPageDTO requestParam) {

        TableResultUtil tableResultUtil = customerService.queryPage(requestParam);

        return ResponseData.ok(true, tableResultUtil);
    }

    /**
     * 编辑客户档案
     * @param requestParam 入参
     * @return 处理结果
     */
    @PostMapping(value = "/update")
    public ResponseData update(@Validated @RequestBody UpdateCustomerDTO requestParam) {
        customerService.update(requestParam);
        return ResponseData.ok();
    }

    /**
     * 客户档案信息
     * @param requestParam 入参
     * @return 处理结果
     */
    @PostMapping(value = "/info")
    public ResponseData queryOne(@Validated @RequestBody QueryCustomerIdDTO requestParam) {
        BussCustomerVO bussCustomerVO = customerService.queryById(requestParam);
        String filedName=requestParam.getFiledName();
        if (!requestParam.getIsMask()) {
            // 不脱敏，返回明文
            if (StringUtils.isNotBlank(filedName)) {
                // 指定了非脱敏字段，只处理这个字段值，并返回
                String fieldNomaskVal= SwDeSensitive.decryptSensitive(bussCustomerVO,filedName);
                return ResponseData.ok(true, fieldNomaskVal);
            } else {
                // 处理所有的脱敏字段值，返回整个对象
                return ResponseData.ok(true, SwDeSensitive.removeSensitive(bussCustomerVO));
            }
        } else {
            //脱敏，直接返回
            return ResponseData.ok(true,bussCustomerVO);
        }


    }

    /**
    * @Description: 删除客户档案
    * @Param: [deleteCustomerDTO]
    * @return: com.swxa.prp.util.ResponseData
    * @Author: lwei
    * @Date: 2025/7/23
    */
    @PostMapping(value = "/delete")
    public ResponseData delete(@Validated @RequestBody DeleteCustomerDTO deleteCustomerDTO) {
        customerService.delete(deleteCustomerDTO);
        return ResponseData.ok();
    }

    /**
     * 通过Excel导入客户档案
     * @param file
     * @return 处理结果
     * @throws Exception
     */
    @PostMapping(value = "/importByExcel")
    public ResponseData importByExcel(@Validated @RequestBody MultipartFile file) throws Exception {
	    //解析Excel表格
	    List<ImportCustomerDTO> importList = ExcelUtils.readFromFile(file, "客户档案", ImportCustomerDTO.class);

        // 把ImportCustomerDTO转换成AddCustomerDTO
        List<AddCustomerDTO> customerDTOList = new ArrayList<>();
        for (ImportCustomerDTO importDTO : importList) {
            AddCustomerDTO item = new AddCustomerDTO();
            item.setCustomerCode(importDTO.getCustomerCode());
            item.setCorporation(importDTO.getCorporation());
            item.setCustomerGroup(importDTO.getCustomerGroup());
            item.setCustomerName(importDTO.getCustomerName());
            item.setSalesManagerDept(importDTO.getSalesManagerDept());
            item.setCustomerSource(importDTO.getCustomerSource());
            item.setStrategicCustomerFlag(importDTO.getStrategicCustomerFlag());
            item.setResourceDescription(importDTO.getResourceDescription());
            item.setTeamMembers(importDTO.getTeamMembers());
            item.setBusinessDescription(importDTO.getBusinessDescription());
            item.setFormerName(importDTO.getFormerName());
            item.setTransactionState(importDTO.getTransactionState());
            item.setCustomerLevel(importDTO.getCustomerLevel());
            item.setIndustry(importDTO.getIndustry());
            item.setChannelType(importDTO.getChannelType());
            item.setDiscountHardware(importDTO.getDiscountHardware());
            item.setDiscountSoftware(importDTO.getDiscountSoftware());
            item.setChannelAgreement(importDTO.getChannelAgreement());
            item.setAgreementStart(importDTO.getAgreementStart());
            item.setAgreementEnd(importDTO.getAgreementEnd());
            item.setAgreementExtendFlag(importDTO.getAgreementExtendFlag());
            item.setCertificateNumber(importDTO.getCertificateNumber());
            item.setTaskAmount(importDTO.getTaskAmount());
            item.setFollowUp(importDTO.getFollowUp());
            item.setRegisteredDate(importDTO.getRegisteredDate());
            item.setRegisteredCapital(importDTO.getRegisteredCapital());
            item.setRegisteredIndustry(importDTO.getRegisteredIndustry());
            item.setEnterpriseNature(importDTO.getEnterpriseNature());
            item.setLegalRepresentative(importDTO.getLegalRepresentative());
            item.setOfficialWebsite(importDTO.getOfficialWebsite());
            item.setStaffSize(importDTO.getStaffSize());
            item.setSocialSecurityNumber(importDTO.getSocialSecurityNumber());
            item.setBusinessScope(importDTO.getBusinessScope());
            item.setCompanyEmail(importDTO.getCompanyEmail());
            item.setCompanyTel(importDTO.getCompanyTel());
            item.setName(importDTO.getName());
            item.setContactCode(importDTO.getContactCode());
            item.setPosition(importDTO.getPosition());
            item.setMobile(importDTO.getMobile());
            item.setTel(importDTO.getTel());
            item.setWechat(importDTO.getWechat());
            item.setDepartment(importDTO.getDepartment());
            item.setCountry(importDTO.getCountry());
            item.setProvince(importDTO.getProvince());
            item.setCity(importDTO.getCity());
            item.setRegion(importDTO.getRegion());
            item.setAddress(importDTO.getAddress());
            item.setCompanyName(importDTO.getCompanyName());
            item.setBankName(importDTO.getBankName());
            item.setPhoneNumber(importDTO.getPhoneNumber());
            item.setTaxpayerType(importDTO.getTaxpayerType());
            item.setTaxpayerId(importDTO.getTaxpayerId());
            item.setBankAccount(importDTO.getBankAccount());
            item.setInvoiceAddress(importDTO.getInvoiceAddress());
            
            // 根据销售负责人名称查询用户ID
            if (StringUtils.isNotBlank(importDTO.getSalesManager())) {
                String salesManagerId = AssignedUserTransUtils.getUserIdByName(importDTO.getSalesManager());
                if (StringUtils.isBlank(salesManagerId)) {
                    //FIXME 定义错误码
                    throw new SwPrpException("", String.format("销售负责人[%s]不存在", importDTO.getSalesManager()));
                }
                item.setSalesManagerId(salesManagerId);
                //去掉部门，只保留负责人名称
                if(importDTO.getSalesManager().contains("-")){
                    item.setSalesManager(importDTO.getSalesManager().split("-")[1]);
                }
                else{
                    item.setSalesManager(importDTO.getSalesManager());
                }
            }
            
            // 根据团队成员名称查询用户ID
            if (StringUtils.isNotBlank(importDTO.getTeamMembers())) {
                String[] teamMemberNames = importDTO.getTeamMembers().split(",");
                List<String> teamMemberIds = new ArrayList<>();
                for (String teamMemberName : teamMemberNames) {
                    String teamMemberId = AssignedUserTransUtils.getUserIdByName(teamMemberName.trim());
                    if (StringUtils.isBlank(teamMemberId)) {
                        //FIXME 定义错误码
                        throw new SwPrpException("", String.format("团队成员[%s]不存在", teamMemberName.trim()));
                    }
                    teamMemberIds.add(teamMemberId);
                }
                item.setTeamMembersId(String.join(",", teamMemberIds));
            }
            
            customerDTOList.add(item);
        }

        try {
            customerService.batchAddCustomer(customerDTOList);
        } catch (Exception e) {
            //FIXME 定义错误码
            throw new SwPrpException(e.getMessage());
        }
        return ResponseData.ok();
    }

    /**
     * 导出客户档案
     * @param requestParam 入参
     * @return 处理结果
     */
    @PostMapping(value = "/export")
    public ResponseData export(@Validated @RequestBody ExportCustomerDTO requestParam) {
        customerService.export(requestParam);
        return ResponseData.ok();
    }

    /**
     * 分配客户档案
     * @param requestParam 入参
     * @return 处理结果
     */
    @PostMapping(value = "/assign")
    public ResponseData assign(@Validated @RequestBody AssignCustomerDTO requestParam) {
        customerService.assign(requestParam);
        return ResponseData.ok();
    }

    /**
     * 共享客户档案
     * @param requestParam 入参
     * @return 处理结果
     */
    @PostMapping(value = "/share")
    public ResponseData share(@Validated @RequestBody ShareCustomerDTO requestParam) {
        customerService.share(requestParam);
        return ResponseData.ok();
    }

    /**
     * 客户档案信息---查重
     *
     * @param requestParam 入参
     * @return 处理结果 返回客户list
     */
    @PostMapping (value = "/checkRepeatList")
    public ResponseData queryCheckRepeatList(@Validated @RequestBody CheckRepeatCustomerNameDTO requestParam) {
        TableResultUtil tableResultUtil = customerService.checkRepeatByName(requestParam);
        String filedName = requestParam.getFiledName();
        if (!requestParam.getIsMask()) {
            // 不脱敏，返回明文
            if (StringUtils.isNotBlank(filedName)) {
                // 指定了非脱敏字段，只处理这个字段值，并返回
                String fieldNomaskVal = SwDeSensitive.decryptSensitive(tableResultUtil, filedName);
                return ResponseData.ok(true, fieldNomaskVal);
            } else {
                // 处理所有的脱敏字段值，返回整个对象
                return ResponseData.ok(true, SwDeSensitive.removeSensitive(tableResultUtil));
            }
        } else {
            //脱敏，直接返回
            return ResponseData.ok(true, tableResultUtil);
        }
    }

    /**
     * 客户档案信息 传U9
     * @param requestParam 入参
     * @return 处理结果
     */
    @PostMapping (value = "/syncUnineCreate")
    public ResponseData syncUnineCreate(@Validated @RequestBody QueryCustomerIdDTO requestParam) {
        customerService.syncUnineCreate(requestParam);
        return ResponseData.ok();
    }

    /**
     * 客户档案信息 同步分贝通
     *
     * @param requestParam 入参
     * @return 处理结果
     */
    @PostMapping (value = "/syncFbtCreate")
    public ResponseData syncFbtCreate(@Validated @RequestBody QueryCustomerIdDTO requestParam) {
        customerService.syncFbtCreate(requestParam);
        return ResponseData.ok();
    }

}
