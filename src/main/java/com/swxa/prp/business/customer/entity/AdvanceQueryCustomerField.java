package com.swxa.prp.business.customer.entity;

import com.swxa.prp.annotation.FieldName;
import com.swxa.prp.business.common.inter.AdvanceQueryInterface;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * @ Date：2025-08-20-9:25
 * @ Version：1.0
 * @ Description：客户档案-高级查询-字段类
 */
@Data
@Component
public class AdvanceQueryCustomerField implements AdvanceQueryInterface {
    /**
     * 公司主体
     */
    @FieldName (value = "公司主体")
    private String corporation;
    /**
     * 客户集
     */
    @FieldName (value = "客户集")
    private String customerGroup;
//
//    /**
//     * 客户编号
//     */
//    @FieldName(value = "客户编号")
//    private String customerCode;
    /**
     * 客户名称
     */
    @FieldName (value = "客户名称")
    private String customerName;
    /**
     * 销售部门ID
     */
    @FieldName (value = "销售部门ID")
    private String salesManagerDeptId;
    /**
     * 销售部门
     */
    @FieldName (value = "销售部门")
    private String salesManagerDept;
    /**
     * 销售负责人ID
     */
    @FieldName (value = "销售负责人ID")
    private String salesManagerId;
    /**
     * 销售负责人
     */
    @FieldName (value = "销售负责人")
    private String salesManager;
    /**
     * 客户来源
     */
    @FieldName (value = "客户来源")
    private String customerSource;
    /**
     * 是否战略客户
     */
    @FieldName (value = "是否战略客户")
    private String strategicCustomerFlag;
    /**
     * 资源描述
     */
    @FieldName (value = "资源描述")
    private String resourceDescription;
    /**
     * 业务说明
     */
    @FieldName (value = "业务说明")
    private String businessDescription;
    /**
     * 曾用名
     */
    @FieldName (value = "曾用名")
    private String formerName;
    /**
     * 成交状态
     */
    @FieldName (value = "成交状态")
    private String transactionState;
    /**
     * 客户级别
     */
    @FieldName (value = "客户级别")
    private String customerLevel;
    /**
     * 合作领域/行业
     */
    @FieldName (value = "合作领域/行业")
    private String industry;
    /**
     * 供应商名称
     */
    @FieldName (value = "渠道类型")
    private String channelType;
    /**
     * 合作价格折扣-硬件
     */
    @FieldName (value = "合作价格折扣-硬件", type = "float")
    private Float discountHardware;
    /**
     * 合作价格折扣-软件
     */
    @FieldName (value = "合作价格折扣-软件", type = "float")
    private Float discountSoftware;
    /**
     * 渠道协议
     */
    @FieldName (value = "渠道协议")
    private String channelAgreement;
    /**
     * 协议起始日期
     */
    @FieldName (value = "协议起始日期", type = "date")
    private String agreementStart;
    /**
     * 协议截止日期
     */
    @FieldName (value = "协议截止日期", type = "date")
    private String agreementEnd;
    /**
     * 是否申请延长协议周期
     */
    @FieldName (value = "是否申请延长协议周期")
    private String agreementExtendFlag;
    /**
     * 证书编号
     */
    @FieldName (value = "证书编号")
    private String certificateNumber;
    /**
     * 考核任务额
     */
    @FieldName (value = "考核任务额", type = "int")
    private BigDecimal taskAmount;
    /**
     * 跟进情况
     */
    @FieldName (value = "跟进情况")
    private String followUp;
    /**
     * 注册时间
     */
    @FieldName (value = "注册时间", type = "date")
    private String registeredDate;
    /**
     * 注册资金
     */
    @FieldName (value = "注册资金", type = "int")
    private BigDecimal registeredCapital;
    /**
     * 注册行业
     */
    @FieldName (value = "注册行业")
    private String registeredIndustry;
    /**
     * 企业性质
     */
    @FieldName (value = "企业性质")
    private String enterpriseNature;
    /**
     * 法定代表人
     */
    @FieldName (value = "法定代表人")
    private String legalRepresentative;
    /**
     * 官网地址
     */
    @FieldName (value = "官网地址")
    private String officialWebsite;
    /**
     * 人员规模
     */
    @FieldName (value = "人员规模")
    private String staffSize;
    /**
     * 社保人数
     */
    @FieldName (value = "社保人数", type = "int")
    private Integer socialSecurityNumber;
    /**
     * 经营范围
     */
    @FieldName (value = "经营范围")
    private String businessScope;
    /**
     * 公司Email
     */
    @FieldName (value = "公司Email")
    private String companyEmail;
    /**
     * 公司电话
     */
    @FieldName (value = "公司电话")
    private String companyTel;
    /**
     * 国家
     */
    @FieldName (value = "国家")
    private String country;
    /**
     * 省份
     */
    @FieldName (value = "省份")
    private String province;
    /**
     * 城市
     */
    @FieldName (value = "城市")
    private String city;
    /**
     * 区域
     */
    @FieldName (value = "区域")
    private String region;
    /**
     * 详细地址
     */
    @FieldName (value = "详细地址")
    private String address;
    /**
     * 公司名称
     */
    @FieldName (value = "公司名称")
    private String companyName;
    /**
     * 开户行
     */
    @FieldName (value = "开户行")
    private String bankName;
    /**
     * 电话
     */
    @FieldName (value = "电话")
    private String phoneNumber;
    /**
     * 纳税人资质
     */
    @FieldName (value = "纳税人资质")
    private String taxpayerType;
    /**
     * 纳税人识别号
     */
    @FieldName (value = "纳税人识别号")
    private String taxpayerId;
    /**
     * 银行账户
     */
    @FieldName (value = "银行账户")
    private String bankAccount;
    /**
     * 开票地址
     */
    @FieldName (value = "开票地址")
    private String invoiceAddress;
    /**
     * 是否已传U9
     */
    @FieldName (value = "是否已传U9")
    private String u9Flag;
    /**
     * 是否已传分贝通
     */
    @FieldName (value = "是否已传分贝通")
    private String fbtFlag;
    /**
     * 创建人
     */
    @FieldName (value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @FieldName (value = "创建时间", type = "time")
    private String createTime;
    /**
     * 更新人
     */
    @FieldName (value = "更新人")
    private String updateBy;
    /**
     * 更新时间
     */
    @FieldName (value = "更新时间", type = "time")
    private String updateTime;

    /**
     * @Description: 获取服务编码-客户档案
     */
    @Override
    public String getCode() {
        return "businessCustomerManage";
    }

    /**
     * @Description: 获取定义的服务类
     */
    @Override
    public Class getClsaa() {
        return AdvanceQueryCustomerField.class;
    }


}
