package com.swxa.prp.business.customer.dto;

import com.swxa.prp.annotation.ExcelColumn;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 联系人导入DTO
 */
@Data
public class ImportContactDTO {

	/**
	 * 联系人编号
	 */
	@ExcelColumn(name = "联系人编号", exportIndex = 0, require = false)
	private String contactCode;

	/**
	 * 客户名称（用于查找客户ID）
	 */
	@ExcelColumn(name = "客户名称", exportIndex = 1, require = true)
	@NotNull(message = "客户名称不能为空")
	private String customerName;

	/**
	 * 联系人姓名
	 */
	@ExcelColumn(name = "联系人姓名", exportIndex = 2, require = true)
	@NotNull(message = "联系人姓名不能为空")
	private String name;

	/**
	 * 所在部门
	 */
	@ExcelColumn(name = "所在部门", exportIndex = 3, require = false)
	private String department;

	/**
	 * 职务
	 */
	@ExcelColumn(name = "职务", exportIndex = 4, require = false)
	private String position;

	/**
	 * 性别
	 */
	@ExcelColumn(name = "性别", exportIndex = 5, require = false)
	private String sex;

	/**
	 * 手机号
	 */
	@ExcelColumn(name = "手机号", exportIndex = 6, require = false)
	private String mobile;

	/**
	 * 电话
	 */
	@ExcelColumn(name = "电话", exportIndex = 7, require = false)
	private String tel;

	/**
	 * 微信
	 */
	@ExcelColumn(name = "微信", exportIndex = 8, require = false)
	private String wechat;

	/**
	 * 上级领导
	 */
	@ExcelColumn(name = "上级领导", exportIndex = 9, require = false)
	private String superiorLeader;

	/**
	 * 是否首要联系人
	 */
	@ExcelColumn(name = "是否首要联系人", exportIndex = 10, require = false)
	private String primaryContactFlag;

	/**
	 * 是否函证接收人
	 */
	@ExcelColumn(name = "是否函证接收人", exportIndex = 11, require = false)
	private String confirmationReceiverFlag;

	/**
	 * 接收函证类型
	 */
	@ExcelColumn(name = "接收函证类型", exportIndex = 12, require = false)
	private String confirmationType;

	/**
	 * 备注
	 */
	@ExcelColumn(name = "备注", exportIndex = 13, require = false)
	private String remark;

	/**
	 * 销售部门
	 */
	@ExcelColumn(name = "销售部门", exportIndex = 14, require = false)
	private String salesManagerDept;

	/**
	 * 销售负责人
	 */
	@ExcelColumn(name = "销售负责人", exportIndex = 15, require = false)
	private String salesManager;
}
