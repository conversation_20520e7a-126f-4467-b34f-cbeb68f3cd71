package com.swxa.prp.business.customer.enums;

public enum CustomerPermissionTypeEnum {

	PRINCIPAL("负责人", "1"),
	SUPERIOR("上级领导", "2"),
	TEAM("团队成员", "3")

	;

	private String name;
	private String value;


	private CustomerPermissionTypeEnum(String name, String value) {
		this.name = name;
		this.value = value;
	}

	public String getName() {
		return name;
	}

	public String getValue() {
		return value;
	}

}