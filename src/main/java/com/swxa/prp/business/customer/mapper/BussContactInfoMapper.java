package com.swxa.prp.business.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.swxa.prp.business.customer.entity.BussContactInfoPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 客户档案数据库操作层
 * @Author: zhangweicheng
 * @Date: 2025/7/22
 */

@Mapper
public interface BussContactInfoMapper extends BaseMapper<BussContactInfoPO> {
    //删除
    int deleteByPrimaryKey(String id);


    //添加
    int insertSelective(BussContactInfoPO row);

    //查询
    BussContactInfoPO selectByPrimaryKey(String id);

    //更新
    int updateByPrimaryKeySelective(BussContactInfoPO row);

    List<String> selectListBySaleUserId(@Param("idsArray") List<String> ids);

    List<String> selectListByCreateId(@Param("idsArray") List<String> ids);

}