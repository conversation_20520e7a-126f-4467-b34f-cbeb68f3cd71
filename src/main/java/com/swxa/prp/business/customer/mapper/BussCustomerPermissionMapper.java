package com.swxa.prp.business.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.swxa.prp.business.customer.entity.BussCustomerPermission;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface BussCustomerPermissionMapper extends BaseMapper<BussCustomerPermission> {
    int deleteByPrimaryKey(String id);

    int insertSelective(BussCustomerPermission row);

    BussCustomerPermission selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(BussCustomerPermission row);

}