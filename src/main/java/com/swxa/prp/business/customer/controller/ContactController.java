package com.swxa.prp.business.customer.controller;

import com.swxa.prp.business.customer.dto.*;
import com.swxa.prp.business.customer.service.ContactService;
import com.swxa.prp.business.customer.vo.BussContactVO;
import com.swxa.prp.datamask.SwDeSensitive;
import com.swxa.prp.exception.SwPrpException;
import com.swxa.prp.util.AssignedUserTransUtils;
import com.swxa.prp.util.ExcelUtils;
import com.swxa.prp.util.ResponseData;
import com.swxa.prp.util.TableResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/contact")
public class ContactController {

    @Resource
    private ContactService contactService;

    /**
     * 新增联系人
     * @param requestParam 入参
     * @return 处理结果
     */
    @PostMapping(value = "/add")
    public ResponseData add(@Validated @RequestBody AddContactDTO requestParam) {

        log.info("新增联系人：{}",requestParam);
        contactService.add(requestParam);
        return ResponseData.ok();
    }

    /**
     * 编辑联系人
     * @param requestParam 入参
     * @return 处理结果
     */
    @PostMapping(value = "/update")
    public ResponseData update(@Validated @RequestBody UpdateContactDTO requestParam) {
        contactService.update(requestParam);
        return ResponseData.ok();
    }

    /**
     * 分页查询联系人
     * @param requestParam 入参
     * @return 处理结果
     */
    @PostMapping(value = "/page")
    public ResponseData queryPage(@Validated @RequestBody QueryContactPageDTO requestParam) {

        TableResultUtil tableResultUtil = contactService.queryPage(requestParam);

        return ResponseData.ok(true, tableResultUtil);
    }

    /**
     * 删除联系人
     * @param ids 入参
     * @return 处理结果
     */
    @PostMapping(value = "/delete")
    public ResponseData delete(@Validated @RequestBody String[] ids) {
        contactService.delete(ids);
        return ResponseData.ok();
    }

    /**
     * 通过Excel导入联系人
     * @param file Excel文件
     * @return 处理结果
     */
    @PostMapping(value = "/importByExcel")
    public ResponseData importByExcel(@Validated MultipartFile file) throws Exception {
        // 解析Excel表格
        List<ImportContactDTO> importList = ExcelUtils.readFromFile(file, "联系人", ImportContactDTO.class);

        // 将ImportContactDTO转换成AddContactDTO
        List<AddContactDTO> contactDTOList = new ArrayList<>();
        for (ImportContactDTO importDTO : importList) {
            AddContactDTO item = new AddContactDTO();
            
            // 基本字段映射
            item.setContactCode(importDTO.getContactCode());
            item.setName(importDTO.getName());
            item.setDepartment(importDTO.getDepartment());
            item.setPosition(importDTO.getPosition());
            item.setSex(importDTO.getSex());
            item.setMobile(importDTO.getMobile());
            item.setTel(importDTO.getTel());
            item.setWechat(importDTO.getWechat());
            item.setSuperiorLeader(importDTO.getSuperiorLeader());
            item.setPrimaryContactFlag(importDTO.getPrimaryContactFlag());
            item.setConfirmationReceiverFlag(importDTO.getConfirmationReceiverFlag());
            item.setConfirmationType(importDTO.getConfirmationType());
            item.setRemark(importDTO.getRemark());
            item.setSalesManagerDept(importDTO.getSalesManagerDept());
            item.setSalesManager(importDTO.getSalesManager());
            
            // 根据客户名称查询客户ID
            String customerId = contactService.findCustomerIdByName(importDTO.getCustomerName());
            if (StringUtils.isBlank(customerId)) {
                //FIXME 定义错误码
                throw new SwPrpException("", String.format("客户[%s]不存在", importDTO.getCustomerName()));
            }
            item.setCustomerId(customerId);
            
            // 根据销售负责人姓名查询用户ID
            if (StringUtils.isNotBlank(importDTO.getSalesManager())) {
                String salesManagerId = AssignedUserTransUtils.getUserIdByName(importDTO.getSalesManager());
                if (StringUtils.isBlank(salesManagerId)) {
                    //FIXME 定义错误码
                    throw new SwPrpException("", String.format("销售负责人[%s]不存在", importDTO.getSalesManager()));
                }
                item.setSalesManagerId(salesManagerId);
            }
            
            contactDTOList.add(item);
        }

        try {
            contactService.batchAdd(contactDTOList);
        } catch (Exception e) {
            //FIXME 定义错误码
            throw new SwPrpException("", e.getMessage());
        }
        return ResponseData.ok();
    }

    /**
     * 导出联系人
     * @param requestParam 入参
     * @return 处理结果
     */
    @PostMapping(value = "/export")
    public ResponseData export(@Validated @RequestBody ExportContactDTO requestParam) {
        contactService.export(requestParam);
        return ResponseData.ok();
    }

    /**
     * 联系人信息
     * @param requestParam 入参
     * @return 处理结果
     */
    @PostMapping(value = "/info")
    public ResponseData queryOne(@Validated @RequestBody QueryContactIdDTO requestParam) {
        BussContactVO bussCustomerVO = contactService.queryById(requestParam);
        String filedName=requestParam.getFiledName();

        if (!requestParam.getIsMask()) {
            // 不脱敏，返回明文
            if (StringUtils.isNotBlank(filedName)) {
                // 指定了非脱敏字段，只处理这个字段值，并返回
                String fieldNomaskVal= SwDeSensitive.decryptSensitive(bussCustomerVO,filedName);
                return ResponseData.ok(true, fieldNomaskVal);
            } else {
                // 处理所有的脱敏字段值，返回整个对象
                return ResponseData.ok(true, SwDeSensitive.removeSensitive(bussCustomerVO));
            }
        } else {
            //脱敏，直接返回
            return ResponseData.ok(true,bussCustomerVO);
        }

    }
}
