package com.swxa.prp.business.customer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.swxa.prp.business.act.conditionFields.dto.BussProcessStatusDTO;
import com.swxa.prp.business.advancefunding.entity.AdvanceFundingOrderPO;
import com.swxa.prp.business.customer.dto.*;
import com.swxa.prp.business.customer.entity.BussContactInfoPO;
import com.swxa.prp.business.customer.entity.BussCustomerInfoPO;
import com.swxa.prp.business.customer.enums.DeleteFlagEnum;
import com.swxa.prp.business.customer.mapper.BussContactInfoMapper;
import com.swxa.prp.business.customer.mapper.BussCustomerInfoMapper;
import com.swxa.prp.business.customer.service.ContactService;
import com.swxa.prp.business.customer.vo.BussContactVO;
import com.swxa.prp.business.dataauth._enum.CrmBussTypeEnum;
import com.swxa.prp.business.dataauth.dataauthrules.service.DataAuthService;
import com.swxa.prp.constant.SwErrorCodeConstant;
import com.swxa.prp.exception.SwPrpException;
import com.swxa.prp.util.MyUtil;
import com.swxa.prp.util.SqlToolsUtil;
import com.swxa.prp.util.TableResultUtil;
import com.swxa.prp.util.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 联系人业务逻辑实现
 * @Author: zhangweicheng
 * @Date: 2025/7/22
 */

@Slf4j
@Service
public class ContactServiceImpl implements ContactService {


    @Resource
    private BussContactInfoMapper bussContactInfoMapper;

    @Lazy
    @Autowired
    private DataAuthService dataAuthService;

    @Resource
    private BussCustomerInfoMapper bussCustomerInfoMapper;

    /**
     * 新增联系人
     *
     * @param requestParam 入参
     */
    @Override
    public void add(AddContactDTO requestParam) {

        String customerId = requestParam.getCustomerId();
        String name = requestParam.getName();
        String mobile = requestParam.getMobile();

        // 业务校验-同一客户，同一联系人名称电话
        QueryWrapper<BussContactInfoPO> checkWrapper = Wrappers.query();
        checkWrapper.lambda().eq(BussContactInfoPO::getCustomerId, customerId)
                .eq(BussContactInfoPO::getName, name)
                .eq(BussContactInfoPO::getMobile, mobile);
        Long aLong = bussContactInfoMapper.selectCount(checkWrapper);
        if (aLong > 0) {
            throw new SwPrpException(SwErrorCodeConstant.CUSTOMER_CONTACT_EXIST_ERROR); // 客户联系人已经存在
        }

        // 业务校验 客户档案是否存在
        BussCustomerInfoPO bussCustomerInfo = bussCustomerInfoMapper.selectByPrimaryKey(customerId);

        if (bussCustomerInfo == null) {
            throw new SwPrpException(SwErrorCodeConstant.CUSTOMER_NOT_EXIST_ERROR);
        }

        // 逻辑删除，提示客户档案不存在
        if (DeleteFlagEnum.DELETED.getValue().equals(bussCustomerInfo.getDeleteFlag())) {
            throw new SwPrpException(SwErrorCodeConstant.CUSTOMER_NOT_EXIST_ERROR);
        }

        String id = MyUtil.getRandomID();

        BussContactInfoPO bussContactInfo = new BussContactInfoPO();

        BeanUtil.copyProperties(requestParam, bussContactInfo);
        bussContactInfo.setId(id);
//        bussContactInfo.setContactCode(id); // 使用前端传入的业务编号

        bussContactInfo.setContactCode(requestParam.getContactCode());
        bussContactInfo.setCreateBy(UserUtil.getLoginUserId()); // TODO 创建人，当前登录用户
        bussContactInfo.setCreateTime(DateUtil.now());
        bussContactInfo.setUpdateBy(UserUtil.getLoginUserId()); // TODO 创建人，当前登录用户
        bussContactInfo.setUpdateTime(DateUtil.now());

        bussContactInfoMapper.insertSelective(bussContactInfo);

    }

    /**
     * 联系人分页查询
     *
     * @param requestParam 入参
     * @return 处理结果
     */
    @Override
    public TableResultUtil queryPage(QueryContactPageDTO requestParam) {

        //引入数据授权
        //追加数据授权条件
        List<String> bussIds = dataAuthService.getBussIds(CrmBussTypeEnum.businessContact.getCode());

        String contactCode = requestParam.getContactCode();
        String name = requestParam.getName();
        // 开启分页
        Page<BussContactVO> page = PageHelper.startPage(requestParam.getPageNum(), requestParam.getPageSize());

        QueryWrapper<BussContactInfoPO> queryWrapper = Wrappers.query();
        //todo 客户联系人是否需要客户档案主键来查询？
        // 拼接查询条件sql
        if (CollUtil.isNotEmpty(requestParam.getAdvanceQueryGroups())) {
            String sqlWhere = SqlToolsUtil.constructFields(requestParam.getAdvanceQueryGroups());
            log.info("sqlWhere = {}", sqlWhere);
            queryWrapper.apply(sqlWhere);
        }

        if (StringUtils.isNotEmpty(contactCode)) {
            queryWrapper.lambda().like(BussContactInfoPO::getContactCode, contactCode);
        }
        if (StringUtils.isNotEmpty(name)) {
            queryWrapper.lambda().like(BussContactInfoPO::getName, name);
        }


        if (CollUtil.isNotEmpty(bussIds)){
            queryWrapper.lambda().in(BussContactInfoPO::getId,bussIds);
        }


        // 按更新时间倒序查询
        queryWrapper.lambda().orderByDesc(BussContactInfoPO::getUpdateTime);
        List<BussContactInfoPO> bussContactInfoList = bussContactInfoMapper.selectList(queryWrapper);

        // 返回结果
        List<BussContactVO> resList = new ArrayList<>();

        // 查询内容转换为返回结果
        if (CollUtil.isNotEmpty(bussContactInfoList)) {
            for (BussContactInfoPO tmp : bussContactInfoList) {
                BussContactVO bussContactVO = new BussContactVO();

                BeanUtils.copyProperties(tmp, bussContactVO);

                BussCustomerInfoPO bussCustomerInfo = bussCustomerInfoMapper.selectByPrimaryKey(tmp.getCustomerId());
                if (bussCustomerInfo != null) {
                    bussContactVO.setCustomerName(bussCustomerInfo.getCustomerName());
                }

                resList.add(bussContactVO);
            }
        }

        // 封装返回结果
        PageInfo<BussContactVO> pageInfo = new PageInfo<>(resList);
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());

        return TableResultUtil.buildTableResult(pageInfo);
    }

    @Override
    public void update(UpdateContactDTO requestParam) {

        //判断审批状态


        String id = requestParam.getId();
        String customerId = requestParam.getCustomerId();
        String name = requestParam.getName();
        String mobile = requestParam.getMobile();
        BussContactInfoPO bussContactInfo = bussContactInfoMapper.selectByPrimaryKey(id);

        // 客户联系人不存在
        if (bussContactInfo == null) {
            throw new SwPrpException(SwErrorCodeConstant.CUSTOMER_CONTACT_NOT_EXIST_ERROR);
        }

        // 业务校验-同一客户，同一联系人名称电话
        QueryWrapper<BussContactInfoPO> checkWrapper = Wrappers.query();
        checkWrapper.lambda().eq(BussContactInfoPO::getCustomerId, customerId)
                .eq(BussContactInfoPO::getName, name)
                .eq(BussContactInfoPO::getMobile, mobile)
                .ne(BussContactInfoPO::getId, id);
        Long aLong = bussContactInfoMapper.selectCount(checkWrapper);
        if (aLong > 0) {
            throw new SwPrpException(SwErrorCodeConstant.CUSTOMER_CONTACT_EXIST_ERROR); // 客户联系人已经存在
        }

        // 业务校验 客户档案是否存在
        BussCustomerInfoPO bussCustomerInfo = bussCustomerInfoMapper.selectByPrimaryKey(customerId);

        if (bussCustomerInfo == null) {
            throw new SwPrpException(SwErrorCodeConstant.CUSTOMER_NOT_EXIST_ERROR);
        }

        // 逻辑删除，提示客户档案不存在
        if (DeleteFlagEnum.DELETED.getValue().equals(bussCustomerInfo.getDeleteFlag())) {
            throw new SwPrpException(SwErrorCodeConstant.CUSTOMER_NOT_EXIST_ERROR);
        }

        BeanUtils.copyProperties(requestParam, bussContactInfo);
        bussContactInfo.setUpdateBy(UserUtil.getLoginUserId()); // TODO 创建人，当前登录用户
        bussContactInfo.setUpdateTime(DateUtil.now());

        bussContactInfoMapper.updateByPrimaryKeySelective(bussContactInfo);

    }

    @Transactional
    @Override
    public void delete(String[] ids) {
        if (ids == null || ids.length == 0) {
            throw new SwPrpException(SwErrorCodeConstant.CUSTOMER_CONTACT_ID_NULL_ERROR);
        }
        for (String id : ids) {
            BussContactInfoPO bussContactInfo = bussContactInfoMapper.selectByPrimaryKey(id);

            // 客户联系人不存在
            if (bussContactInfo == null) {
                throw new SwPrpException(SwErrorCodeConstant.CUSTOMER_CONTACT_NOT_EXIST_ERROR);
            }

            // 逻辑删除客户基本信息
            bussContactInfoMapper.deleteByPrimaryKey(id);
        }
    }

    @Override
    public void importContract(ImportContactDTO requestParam) {

    }

    @Override
    public void export(ExportContactDTO requestParam) {

    }

    @Override
    public BussContactVO queryById(QueryContactIdDTO requestParam) {
        String id = requestParam.getId();
        String contactCode = requestParam.getContactCode(); //联系人编号
        if (StringUtils.isEmpty(id) && StringUtils.isEmpty(contactCode)) {
            throw new SwPrpException(SwErrorCodeConstant.CONTACT_ID_CODE_NULL_ERROR);
        }
        QueryWrapper<BussContactInfoPO> wrapper = Wrappers.query();
        wrapper.lambda().eq(StringUtils.isNotEmpty(id), BussContactInfoPO::getId, id);
        // 有id用id，没id用编码
        wrapper.lambda().eq(StringUtils.isEmpty(id) && StringUtils.isNotEmpty(contactCode),
                BussContactInfoPO::getContactCode, contactCode);

        BussContactInfoPO bussContactInfoPO = bussContactInfoMapper.selectOne(wrapper, false);

        if (bussContactInfoPO == null) {
            throw new SwPrpException(SwErrorCodeConstant.CUSTOMER_CONTACT_NOT_EXIST_ERROR);
        }

        // 联系人基本信息
        BussContactVO bussContactVO = new BussContactVO();
        BeanUtils.copyProperties(bussContactInfoPO,bussContactVO);

        BussCustomerInfoPO bussCustomerInfo = bussCustomerInfoMapper.selectByPrimaryKey(bussContactInfoPO.getCustomerId());
        if (bussCustomerInfo != null) {
            bussContactVO.setCustomerName(bussCustomerInfo.getCustomerName());
        }

        return bussContactVO;
    }

    /**
     * @Description: 根据负责人id列表查询对应的记录id列表
     * @Param: [saleUserId]
     * @return: java.util.List<java.lang.String>
     * @Author: lwei
     * @Date: 2025/9/18
     */
    @Override
    public List<String> findBySaleUserIds(List<String> saleUserId) {

        if (CollUtil.isNotEmpty(saleUserId)) {
            List<String> ids = bussContactInfoMapper.selectListBySaleUserId(saleUserId);
            if (CollUtil.isNotEmpty(ids)) {
                return ids;
            }
        }
        return null;
    }

    /**
     * @Description: 根据创建者id列表，查询对应的数据id列表
     * @Param: [createId]
     * @return: java.util.List<java.lang.String>
     * @Author: lwei
     * @Date: 2025/9/18
     */
    @Override
    public List<String> findByCreateIds(List<String> createId) {
        if (CollUtil.isNotEmpty(createId)) {
            List<String> ids = bussContactInfoMapper.selectListByCreateId(createId);
            if (CollUtil.isNotEmpty(ids)) {
                return ids;
            }
        }
        return null;
    }

    /**
     * @Description: 根据id，更新审批状态
     * @Param: [processStatusDTO]
     * @return: void
     * @Author: lwei
     * @Date: 2025/9/19
     */
    @Override
    public void updateProcessStatusById(BussProcessStatusDTO processStatusDTO) {
        String id = processStatusDTO.getId();
        if (StringUtils.isNotBlank(id)) {
            BussContactInfoPO bussContactInfo = new BussContactInfoPO();
            bussContactInfo.setId(id);
            bussContactInfo.setBussProcessStatus(processStatusDTO.getBussProcessStatus());

            bussContactInfoMapper.updateByPrimaryKeySelective(bussContactInfo);
        }

    }

    /**
     * 批量新增联系人
     *
     * @param requestParamList 联系人列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAdd(List<AddContactDTO> requestParamList) {
        if (CollUtil.isEmpty(requestParamList)) {
            return;
        }
        
        for (AddContactDTO requestParam : requestParamList) {
            add(requestParam);
        }
    }

    /**
     * 根据客户名称查询客户ID
     *
     * @param customerName 客户名称
     * @return 客户ID，未找到返回null
     */
    @Override
    public String findCustomerIdByName(String customerName) {
        if (StringUtils.isBlank(customerName)) {
            return null;
        }
        
        QueryWrapper<BussCustomerInfoPO> wrapper = Wrappers.query();
        wrapper.lambda().eq(BussCustomerInfoPO::getCustomerName, customerName);
        wrapper.lambda().ne(BussCustomerInfoPO::getDeleteFlag, DeleteFlagEnum.DELETED.getValue());
        
        BussCustomerInfoPO customer = bussCustomerInfoMapper.selectOne(wrapper, false);
        return customer != null ? customer.getId() : null;
    }

}
