package com.swxa.prp.business.customer.model;

import lombok.Data;

import java.util.List;

/**
 * @ Date：2025-08-20-18:01
 * @ Version：1.0
 * @ Description：分贝通接口 创建档案关系明细 入参
 */
@Data
public class FbtRelationDetailReq {

    private String third_id = "J002"; // 固定 三方系统档案关系ID

    private List<Archive> first_archive; //第一个档案明细信息 客户编码

    private List<Archive> second_archive; //第二个档案明细信息  用户钉钉id

    @Data
    public static class Archive {
        private String third_id;
    }

}
