package com.swxa.prp.business.customer.dto;

import com.swxa.prp.business.common.dto.BasePageParamDTO;
import com.swxa.prp.model.dto.AdvanceQueryGroupDTO;
import lombok.Data;

import java.util.List;

@Data
public class QueryCustomerPageDTO extends BasePageParamDTO {

    private String id;

    private String customerCode;

    private String customerName;

    // 查询条件
    private List<AdvanceQueryGroupDTO> advanceQueryGroups;

}
