package com.swxa.prp.business.customer.dto;

import com.swxa.prp.constant.SwErrorCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;

@Data
public class UpdateCustomerDTO {

    // 物理主键
    @NotEmpty(message = SwErrorCodeConstant.CUSTOMER_ID_NULL_ERROR)
    private String id;

    /**基础信息**/
    // 客户编号
    private String customerCode;

    // 公司主体
    @NotEmpty(message = SwErrorCodeConstant.CUSTOMER_CORPORATION_NULL_ERROR)
    private String corporation;

    // 客户集
    private String customerGroup;

    // 客户名称
    @NotEmpty(message = SwErrorCodeConstant.CUSTOMER_NAME_NULL_ERROR)
    private String customerName;

    /**
     * 销售部门ID
     */
    private String salesManagerDeptId;

    /**
     * 销售部门
     */
    private String salesManagerDept;

    /**
     * 销售负责人ID
     */
    private String salesManagerId;

    /**
     * 销售负责人
     */
    private String salesManager;

    // 客户来源
    private String customerSource;

    // 是否战略客户
    private String strategicCustomerFlag;

    // 资源描述
    private String resourceDescription;

    private String teamMembers; // 团队成员

    private String teamMembersId; // 团队成员id

    // 业务说明
    private String businessDescription;

    // 曾用名
    private String formerName;

    /**客户属性**/
    // 成交状态
    private String transactionState;

    // 客户级别
    private String customerLevel;

    // 合作领域/行业
    private String industry;

    /**渠道管理信息**/
    // 渠道类型
    private String channelType;

    // 合作价格折扣-硬件
    private Float discountHardware;

    // 合作价格折扣-软件
    private Float discountSoftware;

    // 渠道协议
    private String channelAgreement;

    // 协议起始日期
    private String agreementStart;

    // 协议截止日期
    private String agreementEnd;

    // 是否申请延长协议周期
    private String agreementExtendFlag;

    // 证书编号
    private String certificateNumber;

    // 考核任务额
    private BigDecimal taskAmount;

    // 跟进情况
    private String followUp;

    /**工商信息**/
    // 注册时间
    private String registeredDate;

    // 注册资金
    private BigDecimal registeredCapital;

    // 注册行业
    private String registeredIndustry;

    // 企业性质
    private String enterpriseNature;

    // 法定代表人
    private String legalRepresentative;

    // 官网地址
    private String officialWebsite;

    // 人员规模
    private String staffSize;

    // 社保人数
    private Integer socialSecurityNumber;

    // 经营范围
    private String businessScope;

    // 公司Email
    private String companyEmail;

    // 公司电话
    private String companyTel;

    /**地址信息**/
    // 国家
    private String country;

    // 省份
    private String province;

    // 城市
    private String city;

    // 区域
    private String region;

    // 详细地址
    private String address;

    /**开票信息**/
    // 公司名称
    private String companyName;

    // 开户行
    private String bankName;

    // 电话
    private String phoneNumber;

    // 纳税人资质
    private String taxpayerType;

    // 纳税人识别号
    private String taxpayerId;

    // 银行账户
    private String bankAccount;

    // 开票地址
    private String invoiceAddress;

}
