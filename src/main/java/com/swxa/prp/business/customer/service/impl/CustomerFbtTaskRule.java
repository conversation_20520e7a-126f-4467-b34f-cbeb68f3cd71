package com.swxa.prp.business.customer.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.swxa.prp.business.customer.entity.BussCustomerInfoPO;
import com.swxa.prp.business.customer.entity.BussCustomerPermission;
import com.swxa.prp.business.customer.mapper.BussCustomerInfoMapper;
import com.swxa.prp.business.customer.model.FbtCustomerRes;
import com.swxa.prp.business.customer.model.FbtRelationDetailReq;
import com.swxa.prp.business.front.fileservice.async.rule.AsyncTaskRule;
import com.swxa.prp.business.userinfo.dto.QueryUserByIdNameDTO;
import com.swxa.prp.business.userinfo.service.UserInfoService;
import com.swxa.prp.exception.SwPrpException;
import com.swxa.prp.model.SysUserVO;
import com.swxa.prp.util.FbtTokenUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.swxa.prp.business.shippingnotice.constant.ShippingNoticeErrorCodeConstant.ERROR_CODE_1010F00017;


/**
 * @Description: 异步调用分贝通 创建客户接口/更新客户接口
 * @Author: zhangweicheng
 * @Date: 2023/11/27
 */
@Slf4j
@Component
public class CustomerFbtTaskRule implements AsyncTaskRule {


    @Value ("${constant.fbt.ipAddr}")
    private String fbtAddr;

    @Value ("${constant.fbt.token}")
    private String fbtToken;

    @Value ("${constant.fbt.sanweiAppId}")
    private String appId;

    @Value ("${constant.fbt.sanweiAppKey}")
    private String appKey;

    @Value ("${constant.fbt.createCustomerUrl}")
    private String createCustomerUrl;

    @Value ("${constant.fbt.relationShipUrl}")
    private String relationShipUrl;

    @Resource
    private BussCustomerInfoMapper bussCustomerInfoMapper;

    @Autowired
    private UserInfoService userInfoService;

    private BussCustomerInfoPO customerInfo;
    private List<BussCustomerPermission> customerPermissions;

    public CustomerFbtTaskRule() {

    }

    public BussCustomerInfoPO getCustomerInfo() {
        return customerInfo;
    }

    public void setCustomerInfo(BussCustomerInfoPO customerInfo) {
        this.customerInfo = customerInfo;
    }

    public List<BussCustomerPermission> getCustomerPermissions() {
        return customerPermissions;
    }

    public void setCustomerPermissions(List<BussCustomerPermission> customerPermissions) {
        this.customerPermissions = customerPermissions;
    }

    @Override
    public void start() {
        log.info("begin send customer to fbt, customerCode {}", customerInfo.getCustomerCode());

        String token = getFbtToken(); //获取授权
        if (StrUtil.isEmpty(customerInfo.getFbtCode())) {
            //不存在三方id， 走新建----创建自定义档案项目
            //组装参数报文
            String customerParam = assembleCreateParam();
            log.info("Fbt create customer request param: {}", customerParam);
            // 发送分贝通
            String res = FbtTokenUtil.sendFbtPostRequestBodySSL(fbtAddr + createCustomerUrl, customerParam, token);
            //{
            //  "request_id": "JRmys87uRpU9AGAM",
            //  "trace_id": "012a1e8e4b904b89ab34e0e915a9a986.113.17330691232440127",
            //  "code": 0,
            //  "msg": "成功"
            //}
            log.info("Fbt create customer result :{}", res);
            //todo log日志记录
            FbtCustomerRes data = JSONObject.parseObject(res, FbtCustomerRes.class);
            if (0 == data.getCode()) {
                //执行成功
                customerInfo.setFbtFlag("是");
                customerInfo.setFbtCode(customerInfo.getCustomerCode()); //三方id

            } else {
                //执行失败
                customerInfo.setFbtFlag("否");
            }
//            String fbtResInfo = "trace_id:" + data.getTrace_id() + ",msg:" + data.getMsg() + ",code:" + data.getCode();
            customerInfo.setFbtError(res); //记录返回信息
            customerInfo.setFbtTime(DateUtil.now());
//            customerInfo.setUpdateBy(""); // TODO 创建人，当前登录用户
            customerInfo.setUpdateTime(DateUtil.now());

            bussCustomerInfoMapper.updateByPrimaryKeySelective(customerInfo);

            if (0 == data.getCode()) {
                // 新增档案关系明细----钉钉号
                List<String> dingIds = customerPermissions.stream().map(i -> queryUserDingId(i.getPerson()))
                        .distinct() //去重
                        .collect(Collectors.toList());
                addRelationDetail(token, dingIds);

            }
        } else {
            // 增加共享成员， 只传新成员
            //todo  现在是全传？有影响吗
            // 新增档案关系明细----钉钉号
            List<String> dingIds = customerPermissions.stream().map(i -> queryUserDingId(i.getPerson()))
                    .distinct() //去重
                    .collect(Collectors.toList());
            addRelationDetail(token, dingIds);
        }


        log.info("end fbt customer ");
    }

    @Override
    public void doSuccess() {
        //
    }

    @Override
    public void doError() {
        //
        log.error("fbt customer is error!");
    }

    private String getFbtToken() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("app_id", appId); //APP ID
        jsonObject.put("app_key", appKey); // APP Key
        String token = FbtTokenUtil.getToken(fbtAddr + fbtToken, jsonObject.toJSONString()); //获取分贝通的token
        log.info("token = {}", token);
        if ("error".equals(token)) {
            log.error("get token error");
            throw new SwPrpException(ERROR_CODE_1010F00017);
        }
        return token;
    }

    // 将 customer  转换为一个  对象，用于发送给 分贝通系统
    private String assembleCreateParam() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("third_archive_id", "JTDA00001"); // 固定 三方系统档案ID
        List<Object> list = new ArrayList<>();
        JSONObject project = new JSONObject();
        project.put("code", customerInfo.getCustomerCode()); //档案项目编号
        project.put("name", customerInfo.getCustomerName()); // 档案项目名称
        project.put("third_id", customerInfo.getCustomerCode()); //三方系统档案项目id
        list.add(project);
        jsonObject.put("archive_projects", list); // 自定义档案项目列表 数量限制50
        return JSONObject.toJSONString(jsonObject);
    }

    private void addRelationDetail(String token, List<String> dingIds) {
        if (CollUtil.isNotEmpty(dingIds)) {
            for (String dingId : dingIds) {
                String param = assembleRelationParam(dingId);
                // 发送分贝通
                String res = FbtTokenUtil.sendFbtPostRequestBodySSL(fbtAddr + relationShipUrl, param, token);
                //{
                //  "request_id": "JRmys87uRpU9AGAM",
                //  "trace_id": "012a1e8e4b904b89ab34e0e915a9a986.113.17330691232440127",
                //  "code": 0,
                //  "msg": "成功"
                //}
                log.info("Fbt create customer relationship user: {}; result :{}", dingId, res);
                //todo log日志记录
                FbtCustomerRes data = JSONObject.parseObject(res, FbtCustomerRes.class);
                if (0 != data.getCode()) {
                    //执行失败
                    log.error("Fbt archive_relationship_detail is error; customerCode {}; user {}; trace_id {}; msg {}",
                            customerInfo.getCustomerCode(), dingId, data.getTrace_id(), data.getMsg());
                }
                //todo log日志记录
            }
        }
    }

    // 将 成员 转换为一个 FbtRelationDetailReq 对象，用于发送给 分贝通系统,创建档案关系明细
    private String assembleRelationParam(String dingId) {
        FbtRelationDetailReq req = new FbtRelationDetailReq();
        FbtRelationDetailReq.Archive cusCode = new FbtRelationDetailReq.Archive();
        cusCode.setThird_id(customerInfo.getCustomerCode());
        List<FbtRelationDetailReq.Archive> firstArc = new ArrayList<>();
        firstArc.add(cusCode);

        List<FbtRelationDetailReq.Archive> secondArc = new ArrayList<>();
        FbtRelationDetailReq.Archive userId = new FbtRelationDetailReq.Archive();
        userId.setThird_id(dingId);
        secondArc.add(userId);

        req.setFirst_archive(firstArc);
        req.setSecond_archive(secondArc);
        return JSONObject.toJSONString(req);
    }

    /**
     * 通过人员id、用户id查找钉钉id
     *
     * @param id
     * @return
     */
    private String queryUserDingId(String id) {
        QueryUserByIdNameDTO idNameDTO = new QueryUserByIdNameDTO();
        idNameDTO.setId(Long.valueOf(id));
        //调查询用户接口，传入用户id
        SysUserVO sysUserVO = userInfoService.queryUserByIdName(idNameDTO);
        return sysUserVO.getDingId();
    }
}
