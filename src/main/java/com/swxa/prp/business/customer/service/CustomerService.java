package com.swxa.prp.business.customer.service;

import com.swxa.prp.business.act.conditionFields.dto.BussProcessStatusDTO;
import com.swxa.prp.business.customer.dto.*;
import com.swxa.prp.business.customer.vo.BussCustomerVO;
import com.swxa.prp.util.TableResultUtil;

import java.util.List;

/**
 * @Description: 客户档案业务逻辑层
 * @Author: zhangweicheng
 * @Date: 2025/7/22
 */

public interface CustomerService {

    /**
     * @Description: 新增
     * @Param: [requestParam]
     * @return: void
     * @Author: zhangweicheng
     * @Date: 2025/7/22
     */

    void add(AddCustomerDTO requestParam);

    /**
     * @Description: 分页查询
     * @Param: [requestParam]
     * @return: com.swxa.prp.util.TableResultUtil
     * @Author: zhangweicheng
     * @Date: 2025/7/22
     */

    TableResultUtil queryPage(QueryCustomerPageDTO requestParam);

    /**
     * @Description: 更新
     * @Param: [requestParam]
     * @return: void
     * @Author: zhangweicheng
     * @Date: 2025/7/22
     */

    void update(UpdateCustomerDTO requestParam);

    /**
     * @Description: 根据ID查询
     * @Param: [requestParam]
     * @return: com.swxa.prp.business.customer.vo.BussCustomerVO
     * @Author: zhangweicheng
     * @Date: 2025/7/22
     */


    BussCustomerVO queryById(QueryCustomerIdDTO requestParam);

    /**
     * @Description: 根据ID删除多个删除
     * @Param: [ids]
     * @return: void
     * @Author: zhangweicheng
     * @Date: 2025/7/22
     */

    void delete(DeleteCustomerDTO deleteCustomerDTO);

    /** 
    * @Description: 导入客户档案
    * @Param: [requestParam]
    * @return: void
    * @Author: lwei
    * @Date: 2025/7/23
    */
    void importCustomer(ImportCustomerDTO requestParam);

    /** 
    * @Description: 导出客户档案
    * @Param: [requestParam]
    * @return: void
    * @Author: lwei
    * @Date: 2025/7/23
    */
    void export(ExportCustomerDTO requestParam);

    /** 
    * @Description: 给客户分配销售对接人员
    * @Param: [requestParam]
    * @return: void
    * @Author: lwei
    * @Date: 2025/7/23
    */
    void assign(AssignCustomerDTO requestParam);

    /** 
    * @Description: 将客户分享给销售团队成员
    * @Param: [requestParam]
    * @return: void
    * @Author: lwei
    * @Date: 2025/7/23
    */
    void share(ShareCustomerDTO requestParam);

    void syncUnineCreate(QueryCustomerIdDTO requestParam);

    void syncFbtCreate(QueryCustomerIdDTO requestParam);

    /**
     * @Description: 根据负责人id列表查询对应的记录id列表
     * @Param: [saleUserId]
     * @return: java.util.List<java.lang.String>
     * @Author: lwei
     * @Date: 2025/9/18
     */
    List<String> findBySaleUserIds(List<String> saleUserId);

    /**
     * @Description: 根据创建者id列表，查询对应的记录id列表
     * @Param: [createId]
     * @return: java.util.List<java.lang.String>
     * @Author: lwei
     * @Date: 2025/9/18
     */
    List<String> findByCreateIds(List<String> createId);

    /**
     * @Description: 根据id，更新审批状态
     * @Param: [processStatusDTO]
     * @return: void
     * @Author: lwei
     * @Date: 2025/9/19
     */
    void updateProcessStatusById(BussProcessStatusDTO processStatusDTO);

    // 根据客户名称模糊查询客户信息，用来判重
    TableResultUtil checkRepeatByName(CheckRepeatCustomerNameDTO requestParam);

    /**
     * 批量新增客户档案
     * @param requestParamList 批量新增数据列表
     */
    void batchAddCustomer(List<AddCustomerDTO> requestParamList);
}
