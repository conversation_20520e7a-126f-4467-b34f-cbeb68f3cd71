package com.swxa.prp.business.customer.dto;

import com.swxa.prp.constant.SwErrorCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class AssignCustomerDTO {

    // 销售负责人
    @NotNull(message = SwErrorCodeConstant.SALESMAN_NULL_ERROR)
    private String salesman;

    //销售部门
    private String salesDepartment;

    // 客户信息主键列表
    @NotNull(message = SwErrorCodeConstant.CUSTOMER_ID_NULL_ERROR)
    private List<String> ids;
}
