package com.swxa.prp.business.customer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.swxa.prp.business.act.conditionFields.dto.BussProcessStatusDTO;
import com.swxa.prp.business.customer.dto.*;
import com.swxa.prp.business.customer.entity.BussContactInfoPO;
import com.swxa.prp.business.customer.entity.BussCustomerInfoPO;
import com.swxa.prp.business.customer.entity.BussCustomerPermission;
import com.swxa.prp.business.customer.enums.CustomerPermissionTypeEnum;
import com.swxa.prp.business.customer.enums.DeleteFlagEnum;
import com.swxa.prp.business.customer.mapper.BussContactInfoMapper;
import com.swxa.prp.business.customer.mapper.BussCustomerInfoMapper;
import com.swxa.prp.business.customer.mapper.BussCustomerPermissionMapper;
import com.swxa.prp.business.customer.service.CustomerService;
import com.swxa.prp.business.customer.vo.BussCustomerListVO;
import com.swxa.prp.business.customer.vo.BussCustomerVO;
import com.swxa.prp.business.dataauth._enum.CrmBussTypeEnum;
import com.swxa.prp.business.dataauth.dataauthrules.service.DataAuthService;
import com.swxa.prp.business.userinfo.dto.QueryUserByIdNameDTO;
import com.swxa.prp.business.userinfo.service.UserInfoService;
import com.swxa.prp.constant.SwErrorCodeConstant;
import com.swxa.prp.exception.SwPrpException;
import com.swxa.prp.model.SysUserVO;
import com.swxa.prp.util.MyUtil;
import com.swxa.prp.util.SqlToolsUtil;
import com.swxa.prp.util.TableResultUtil;
import com.swxa.prp.util.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class CustomerServiceImpl implements CustomerService {

    @Resource
    private BussCustomerInfoMapper bussCustomerInfoMapper;

    @Lazy
    @Autowired
    private DataAuthService dataAuthService;

    @Resource
    private BussContactInfoMapper bussContactInfoMapper;

    @Resource
    private BussCustomerPermissionMapper bussCustomerPermissionMapper;

    @Resource
    private CustomerUNineTaskRule customerUNineTaskRule;

    @Resource
    private CustomerFbtTaskRule customerFbtTaskRule;

    @Autowired
    private UserInfoService userInfoService;

    /**
     * 新增客户档案
     * @param requestParam 入参
     */
    @Transactional
    @Override
    public void add(AddCustomerDTO requestParam) {

        // 业务校验-客户名称不能重复
        String customerName = requestParam.getCustomerName();
        QueryWrapper<BussCustomerInfoPO> customerInfoWrapper = Wrappers.query();
        customerInfoWrapper.lambda().eq(BussCustomerInfoPO::getCustomerName,customerName)
                .ne(BussCustomerInfoPO::getDeleteFlag, DeleteFlagEnum.DELETED.getValue()); // 查询删除标记不为是的记录
        Long aLong = bussCustomerInfoMapper.selectCount(customerInfoWrapper);
        if (aLong > 0) {
            throw new SwPrpException(SwErrorCodeConstant.CUSTOMER_NAME_EXIST_ERROR);
        }

        String customerId = MyUtil.getRandomID();
        // 保存客户基本信息
        BussCustomerInfoPO bussCustomerInfo = new BussCustomerInfoPO();
        BeanUtils.copyProperties(requestParam,bussCustomerInfo);
        bussCustomerInfo.setId(customerId);
//        bussCustomerInfo.setCustomerCode(customerId); //使用前端传入的业务编号

        bussCustomerInfo.setCreateBy(UserUtil.getLoginUserId()); // TODO 创建人，当前登录用户
        bussCustomerInfo.setCreateTime(DateUtil.now());
        bussCustomerInfo.setUpdateBy(UserUtil.getLoginUserId()); // TODO 创建人，当前登录用户
        bussCustomerInfo.setUpdateTime(DateUtil.now());
        bussCustomerInfoMapper.insertSelective(bussCustomerInfo);

        // 联系人
        String contactCode = MyUtil.getRandomID();
        BussContactInfoPO bussContactInfo = new BussContactInfoPO();
        BeanUtils.copyProperties(requestParam,bussContactInfo);
        bussContactInfo.setId(contactCode);
//        bussContactInfo.setContactCode(contactCode); //使用前端传入的业务编号
        bussContactInfo.setCustomerId(customerId);
        bussContactInfo.setPrimaryContactFlag("是"); // 是否首要联系人

        bussContactInfo.setCreateBy(UserUtil.getLoginUserId()); // TODO 创建人，当前登录用户
        bussContactInfo.setCreateTime(DateUtil.now());
        bussContactInfo.setUpdateBy(UserUtil.getLoginUserId()); // TODO 创建人，当前登录用户
        bussContactInfo.setUpdateTime(DateUtil.now());

        bussContactInfoMapper.insertSelective(bussContactInfo);


        // 客户权限-销售负责人
        BussCustomerPermission bussCustomerPermission = new BussCustomerPermission();
        bussCustomerPermission.setId(MyUtil.getRandomID());
        bussCustomerPermission.setCustomerId(customerId);
        bussCustomerPermission.setPerson(requestParam.getSalesManagerId());
        bussCustomerPermission.setPermissionType(CustomerPermissionTypeEnum.PRINCIPAL.getValue()); // 负责人

        bussCustomerPermission.setCreateBy(UserUtil.getLoginUserId()); // TODO 创建人，当前登录用户
        bussCustomerPermission.setCreateTime(DateUtil.now());
        bussCustomerPermission.setUpdateBy(UserUtil.getLoginUserId()); // TODO 创建人，当前登录用户
        bussCustomerPermission.setUpdateTime(DateUtil.now());
        bussCustomerPermissionMapper.insertSelective(bussCustomerPermission);

        // TODO 客户权限-上级领导
        insertCustomerPermissions(new String[]{
                        String.valueOf(queryUserById(requestParam.getSalesManagerId()).getDepartmentLeaderId())},
                customerId, CustomerPermissionTypeEnum.SUPERIOR.getValue());
        // 客户权限-团队成员
        String teamMembers = requestParam.getTeamMembers();
        String teamMembersId = requestParam.getTeamMembersId(); //团队成员ID，防止名字存在重复
        if (StringUtils.isNotEmpty(teamMembersId)) {
            String[] personsId = teamMembersId.split(",");
            insertCustomerPermissions(personsId,customerId,CustomerPermissionTypeEnum.TEAM.getValue());
        }
    }


    /**
     * 分页查询客户档案
     * @param requestParam 入参
     * @return 处理结果
     */
    @Override
    public TableResultUtil queryPage(QueryCustomerPageDTO requestParam) {

        //引入数据授权
        //追加数据授权条件
        List<String> bussIds = dataAuthService.getBussIds(CrmBussTypeEnum.businessCustomerManage.getCode());


        String id = requestParam.getId();
        String customerCode = requestParam.getCustomerCode();
        String customerName = requestParam.getCustomerName();

        // 开启分页
        Page<BussCustomerListVO> page = PageHelper.startPage(requestParam.getPageNum(), requestParam.getPageSize());
        QueryWrapper<BussCustomerInfoPO> wrapper = Wrappers.query();
        wrapper.lambda().ne(BussCustomerInfoPO::getDeleteFlag, DeleteFlagEnum.DELETED.getValue()); // 查询删除标记不为是的记录
        // 拼接查询条件sql
        if (CollUtil.isNotEmpty(requestParam.getAdvanceQueryGroups())) {
            String sqlWhere = SqlToolsUtil.constructFields(requestParam.getAdvanceQueryGroups());
            log.info("sqlWhere = {}", sqlWhere);
            wrapper.apply(sqlWhere);
        }
        if (StringUtils.isNotEmpty(id)) {
            wrapper.lambda().eq(BussCustomerInfoPO::getId, id);
        }
        if (StringUtils.isNotEmpty(customerCode)) {
            wrapper.lambda().like(BussCustomerInfoPO::getCustomerCode, customerCode);
        }
        if (StringUtils.isNotEmpty(customerName)) {
            wrapper.lambda().like(BussCustomerInfoPO::getCustomerName, customerName);
        }



        if (CollUtil.isNotEmpty(bussIds)){
            wrapper.lambda().in(BussCustomerInfoPO::getId,bussIds);
        }




        // 按更新时间倒序查询
        wrapper.lambda().orderByDesc(BussCustomerInfoPO::getUpdateTime);
        List<BussCustomerInfoPO> customerInfoList = bussCustomerInfoMapper.selectList(wrapper);

        // 返回结果
        List<BussCustomerListVO> resList = new ArrayList<>();

        // 查询内容转换为返回结果
        if (CollUtil.isNotEmpty(customerInfoList)) {
            for (BussCustomerInfoPO tmp : customerInfoList) {
                BussCustomerListVO bussCustomerListVO = new BussCustomerListVO();
                String customerId = tmp.getId();
                // 团队成员
                bussCustomerListVO.setTeamMembers(queryTeamMembers(customerId));

                // 考核期内合同签约额 TODO

                // 查询首要联系人，
                QueryWrapper<BussContactInfoPO> contactWrapper = Wrappers.query();
                contactWrapper.lambda().eq(BussContactInfoPO::getCustomerId, customerId)
                        .eq(BussContactInfoPO::getPrimaryContactFlag, "是");
                List<BussContactInfoPO> bussContactInfos = bussContactInfoMapper.selectList(contactWrapper);
                if (CollUtil.isNotEmpty(bussContactInfos)) {
                    BussContactInfoPO bussContactInfo = bussContactInfos.get(0);
                    BeanUtils.copyProperties(bussContactInfo, bussCustomerListVO);
                    // TODO 团队成员查询需要脱敏 用户名和手机号
                }

                // 客户基本信息，放最后防止id覆盖
                BeanUtils.copyProperties(tmp, bussCustomerListVO);

                bussCustomerListVO.setBussProcessStatus(tmp.getBussProcessStatus());

                resList.add(bussCustomerListVO);
            }
        }

        // 封装返回结果
        PageInfo<BussCustomerListVO> pageInfo = new PageInfo<>(resList);
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());

        return TableResultUtil.buildTableResult(pageInfo);
    }

    /**
     * 查询客户编号对应团队成员
     * 多个成员使用,隔开
     * @param customerId 客户主键
     * @return 团队成员
     */
    private String queryTeamMembers(String customerId){
        StringBuilder teamMembers = new StringBuilder();

        QueryWrapper<BussCustomerPermission> wrapper = Wrappers.query();
        wrapper.lambda().eq(BussCustomerPermission::getCustomerId,customerId)
                .eq(BussCustomerPermission::getPermissionType, CustomerPermissionTypeEnum.TEAM.getValue()); // 团队成员
        List<BussCustomerPermission> bussCustomerPermissions = bussCustomerPermissionMapper.selectList(wrapper);

        if (CollUtil.isNotEmpty(bussCustomerPermissions)) {
            bussCustomerPermissions.forEach(tmp -> {
                teamMembers.append(
                        StringUtils.isNotEmpty(queryUserById(tmp.getPerson()).getNickname()) ?
                        queryUserById(tmp.getPerson()).getNickname() : tmp.getPerson())
                        .append(","); //用户id查询拼接用户name
            });
        }

        if (teamMembers.length() > 0) {
            teamMembers.delete(teamMembers.length() - 1, teamMembers.length());
        }

        return teamMembers.toString();
    }

    /**
    * @Description: 更新客户档案信息
     * 修改客户开票信息，首次审批通过后直接修改，并触发传U9
    * @Param: [requestParam]
    * @return: void
    * @Author: lwei
    * @Date: 2025/7/23
    */

    @Transactional
    @Override
    public void update(UpdateCustomerDTO requestParam) {
        String customerId = requestParam.getId();
        BussCustomerInfoPO bussCustomerInfo = bussCustomerInfoMapper.selectByPrimaryKey(customerId);

        // 客户档案不存在
        if (bussCustomerInfo == null) {
            throw new SwPrpException(SwErrorCodeConstant.CUSTOMER_NOT_EXIST_ERROR);
        }

        // 逻辑删除，提示客户档案不存在
        if (DeleteFlagEnum.DELETED.getValue().equals(bussCustomerInfo.getDeleteFlag())) {
            throw new SwPrpException(SwErrorCodeConstant.CUSTOMER_NOT_EXIST_ERROR);
        }

        // 业务校验-客户名称不能重复
        String customerName = requestParam.getCustomerName();
        QueryWrapper<BussCustomerInfoPO> customerInfoWrapper = Wrappers.query();
        customerInfoWrapper.lambda().eq(BussCustomerInfoPO::getCustomerName,customerName)
                .ne(BussCustomerInfoPO::getId,customerId)
                .ne(BussCustomerInfoPO::getDeleteFlag, DeleteFlagEnum.DELETED.getValue()); // 查询删除标记不为是的记录;
        Long aLong = bussCustomerInfoMapper.selectCount(customerInfoWrapper);
        if (aLong > 0) {
            throw new SwPrpException(SwErrorCodeConstant.CUSTOMER_NAME_EXIST_ERROR);
        }
        //判断是否修改了客户开票信息
        String[] invoiceInfoField = new String[]{"companyName","bankName","phoneNumber","taxpayerType",
                "taxpayerId","bankAccount","invoiceAddress"};
        boolean isEditInvoice = hasFieldChanged(bussCustomerInfo, requestParam, invoiceInfoField);

        BeanUtils.copyProperties(requestParam,bussCustomerInfo);

        bussCustomerInfo.setUpdateBy(UserUtil.getLoginUserId()); // TODO 创建人，当前登录用户
        bussCustomerInfo.setUpdateTime(DateUtil.now());

        bussCustomerInfoMapper.updateByPrimaryKeySelective(bussCustomerInfo);

        // 客户权限-销售人员
        String salesman = requestParam.getSalesManagerId(); // 参数中销售人员
        String salesmanDb = bussCustomerInfo.getSalesManagerId(); // 数据库中销售人员
        if (StringUtils.isNotEmpty(salesman) && !salesman.equals(salesmanDb)) {
            // 客户权限，删除现有权限，销售人员和上级领导
            deleteCustomerPermissions(customerId,new String[]{CustomerPermissionTypeEnum.PRINCIPAL.getValue(),CustomerPermissionTypeEnum.SUPERIOR.getValue()});
            // 客户权限-销售人员
            insertCustomerPermissions(new String[]{salesman},customerId,CustomerPermissionTypeEnum.PRINCIPAL.getValue());
            // TODO 客户权限-上级领导
            insertCustomerPermissions(new String[]{
                            String.valueOf(queryUserById(salesman).getDepartmentLeaderId())},
                    customerId,CustomerPermissionTypeEnum.SUPERIOR.getValue());


            // TODO 项目、合同等权限移交
        }

        // 客户权限-团队成员
        String teamMembers = requestParam.getTeamMembersId();
        if (StringUtils.isNotEmpty(teamMembers)) {
            // 根据客户编号删除团队成员全部记录
            deleteCustomerPermissions(customerId,new String[]{CustomerPermissionTypeEnum.TEAM.getValue()});

            // 插入团队成员全部记录
            String[] persons = teamMembers.split(",");
            insertCustomerPermissions(persons,customerId,CustomerPermissionTypeEnum.TEAM.getValue());
        }
        if (isEditInvoice) {
            //修改开票信息 完成后，同步U9
            customerUNineTaskRule.setCustomerInfo(bussCustomerInfoMapper.selectByPrimaryKey(customerId));
            customerUNineTaskRule.setCanUNine(false); //更新固定false，判断是否可以更新
            customerUNineTaskRule.start(); //执行接口
        }

    }

    /**
    * @Description: 根据id查询客户档案信息
    * @Param: [requestParam]
    * @return: com.swxa.prp.business.customer.vo.BussCustomerVO
    * @Author: lwei
    * @Date: 2025/7/23
    */

    @Override
    public BussCustomerVO queryById(QueryCustomerIdDTO requestParam) {

        String customerId = requestParam.getId();
        String customerCode = requestParam.getCustomerCode(); //客户编码
        if (StringUtils.isEmpty(customerId) && StringUtils.isEmpty(customerCode)) {
            throw new SwPrpException(SwErrorCodeConstant.CUSTOMER_ID_AND_CODE_NULL_ERROR);
        }
        QueryWrapper<BussCustomerInfoPO> wrapper = Wrappers.query();
        wrapper.lambda().eq(StringUtils.isNotEmpty(customerId), BussCustomerInfoPO::getId, customerId);
        // 有id用id，没id用编码
        wrapper.lambda().eq(StringUtils.isEmpty(customerId) && StringUtils.isNotEmpty(customerCode),
                BussCustomerInfoPO::getCustomerCode, customerCode);

        BussCustomerInfoPO bussCustomerInfo = bussCustomerInfoMapper.selectOne(wrapper, false);

        if (bussCustomerInfo == null) {
            throw new SwPrpException(SwErrorCodeConstant.CUSTOMER_NOT_EXIST_ERROR);
        }

        // 逻辑删除，提示客户档案不存在
        if (DeleteFlagEnum.DELETED.getValue().equals(bussCustomerInfo.getDeleteFlag())) {
            throw new SwPrpException(SwErrorCodeConstant.CUSTOMER_NOT_EXIST_ERROR);
        }

        // 客户基本信息
        BussCustomerVO bussCustomerVO = new BussCustomerVO();
        BeanUtils.copyProperties(bussCustomerInfo,bussCustomerVO);

        // 团队成员
        bussCustomerVO.setTeamMembers(queryTeamMembers(bussCustomerInfo.getId()));

        // 考核期内合同签约额 TODO

        // 查询联系人列表
        QueryWrapper<BussContactInfoPO> contactWrapper = Wrappers.query();
        contactWrapper.lambda().eq(BussContactInfoPO::getCustomerId, bussCustomerInfo.getId());
        List<BussContactInfoPO> bussContactInfos = bussContactInfoMapper.selectList(contactWrapper);
        bussCustomerVO.setBussContactInfoList(bussContactInfos);

        return bussCustomerVO;
    }

    /**
    * @Description: 逻辑删除客户档案信息
    * @Param: [deleteCustomerDTO]
    * @return: void
    * @Author: lwei
    * @Date: 2025/7/23
    */

    @Transactional
    @Override
    public void delete(DeleteCustomerDTO deleteCustomerDTO) {
        List<String> ids = deleteCustomerDTO.getIds();

        if (ids == null || ids.size() == 0) {
            throw new SwPrpException(SwErrorCodeConstant.CUSTOMER_ID_NULL_ERROR);
        }
        for (String id : ids) {
            BussCustomerInfoPO bussCustomerInfo = bussCustomerInfoMapper.selectByPrimaryKey(id);
            // 客户档案不存在
            if (bussCustomerInfo == null) {
                throw new SwPrpException(SwErrorCodeConstant.CUSTOMER_NOT_EXIST_ERROR);
            }

            // 逻辑删除客户基本信息
            bussCustomerInfo.setDeleteFlag(DeleteFlagEnum.DELETED.getValue());
            bussCustomerInfo.setUpdateBy(UserUtil.getLoginUserId());
            bussCustomerInfo.setUpdateTime(DateUtil.now());

            bussCustomerInfoMapper.updateByPrimaryKeySelective(bussCustomerInfo);

        }

    }

    @Override
    public void importCustomer(ImportCustomerDTO requestParam) {

    }

    @Override
    public void export(ExportCustomerDTO requestParam) {

    }

    /** 
    * @Description: 给客户指定销售人员
    * @Param: [requestParam]
    * @return: void
    * @Author: lwei
    * @Date: 2025/7/23
    */
    @Transactional
    @Override
    public void assign(AssignCustomerDTO requestParam) {
        String salesman = requestParam.getSalesman();
        String salesDepartment = requestParam.getSalesDepartment();
        List<String> customerIds = requestParam.getIds();

        if (CollUtil.isEmpty(customerIds)) {
            throw new SwPrpException(SwErrorCodeConstant.CUSTOMER_ID_NULL_ERROR);
        }

        for (String customerId : customerIds) {
            BussCustomerInfoPO bussCustomerInfo = bussCustomerInfoMapper.selectByPrimaryKey(customerId);

            if (bussCustomerInfo == null) {
                throw new SwPrpException(SwErrorCodeConstant.CUSTOMER_NOT_EXIST_ERROR);
            }

            // 客户基本信息
            bussCustomerInfo.setSalesManager(salesman);
            bussCustomerInfo.setSalesManagerDept(salesDepartment);

            bussCustomerInfo.setUpdateBy(UserUtil.getLoginUserId()); // TODO 创建人，当前登录用户
            bussCustomerInfo.setUpdateTime(DateUtil.now());
            bussCustomerInfoMapper.updateByPrimaryKeySelective(bussCustomerInfo);

            // 客户权限，删除现有权限，销售人员和上级领导
            deleteCustomerPermissions(customerId,new String[]{CustomerPermissionTypeEnum.PRINCIPAL.getValue(),CustomerPermissionTypeEnum.SUPERIOR.getValue()});
            // 客户权限-销售人员
            insertCustomerPermissions(new String[]{salesman},customerId,CustomerPermissionTypeEnum.PRINCIPAL.getValue());
            // TODO 客户权限-上级领导
            insertCustomerPermissions(new String[]{
                            String.valueOf(queryUserById(salesman).getDepartmentLeaderId())},
                    customerId,CustomerPermissionTypeEnum.SUPERIOR.getValue());


            // TODO 项目、合同等权限移交
        }

    }

    /** 
    * @Description: 将客户分享给销售团队成员
    * @Param: [requestParam]
    * @return: void
    * @Author: lwei
    * @Date: 2025/7/23
    */
    @Transactional
    @Override
    public void share(ShareCustomerDTO requestParam) {
        String teamMembersId = requestParam.getTeamMembersId();
        String customerId = requestParam.getId();

        BussCustomerInfoPO bussCustomerInfo = bussCustomerInfoMapper.selectByPrimaryKey(customerId);

        if (bussCustomerInfo == null) {
            throw new SwPrpException(SwErrorCodeConstant.CUSTOMER_NOT_EXIST_ERROR);
        }


        // 根据客户编号删除团队成员全部记录
        deleteCustomerPermissions(customerId,new String[]{CustomerPermissionTypeEnum.TEAM.getValue()});

        // 插入团队成员全部记录
        String[] persons = teamMembersId.split(",");
        insertCustomerPermissions(persons,customerId,CustomerPermissionTypeEnum.TEAM.getValue());

    }

    /**
     * 将创建客户信息同步U9，审核通过触发
     * todo 定时任务？
     *
     * @param requestParam
     */
    @Override
    public void syncUnineCreate(QueryCustomerIdDTO requestParam) {

        String customerId = requestParam.getId();
        String customerCode = requestParam.getCustomerCode(); //客户编码
        if (StringUtils.isEmpty(customerId) && StringUtils.isEmpty(customerCode)) {
            throw new SwPrpException(SwErrorCodeConstant.CUSTOMER_ID_AND_CODE_NULL_ERROR);
        }
        QueryWrapper<BussCustomerInfoPO> wrapper = Wrappers.query();
        wrapper.lambda().eq(StringUtils.isNotEmpty(customerId), BussCustomerInfoPO::getId, customerId);
        // 有id用id，没id用编码
        wrapper.lambda().eq(StringUtils.isEmpty(customerId) && StringUtils.isNotEmpty(customerCode),
                BussCustomerInfoPO::getCustomerCode, customerCode);

        BussCustomerInfoPO bussCustomerInfo = bussCustomerInfoMapper.selectOne(wrapper, false);

        if (bussCustomerInfo == null) {
            throw new SwPrpException(SwErrorCodeConstant.CUSTOMER_NOT_EXIST_ERROR);
        }

        // 逻辑删除，提示客户档案不存在
        if (DeleteFlagEnum.DELETED.getValue().equals(bussCustomerInfo.getDeleteFlag())) {
            throw new SwPrpException(SwErrorCodeConstant.CUSTOMER_NOT_EXIST_ERROR);
        }
        customerUNineTaskRule.setCustomerInfo(bussCustomerInfo);
        customerUNineTaskRule.setCanUNine(true); //创建U9，可以传递
        customerUNineTaskRule.start(); //执行接口
    }

    /**
     * 将创建客户信息同步分贝通，
     * todo 定时任务？如何触发？
     *
     * @param requestParam
     */
    @Override
    public void syncFbtCreate(QueryCustomerIdDTO requestParam) {

        String customerId = requestParam.getId();
        String customerCode = requestParam.getCustomerCode(); //客户编码
        if (StringUtils.isEmpty(customerId) && StringUtils.isEmpty(customerCode)) {
            throw new SwPrpException(SwErrorCodeConstant.CUSTOMER_ID_AND_CODE_NULL_ERROR);
        }
        QueryWrapper<BussCustomerInfoPO> wrapper = Wrappers.query();
        wrapper.lambda().eq(StringUtils.isNotEmpty(customerId), BussCustomerInfoPO::getId, customerId);
        // 有id用id，没id用编码
        wrapper.lambda().eq(StringUtils.isEmpty(customerId) && StringUtils.isNotEmpty(customerCode),
                BussCustomerInfoPO::getCustomerCode, customerCode);

        BussCustomerInfoPO bussCustomerInfo = bussCustomerInfoMapper.selectOne(wrapper, false);

        if (bussCustomerInfo == null) {
            throw new SwPrpException(SwErrorCodeConstant.CUSTOMER_NOT_EXIST_ERROR);
        }

        // 逻辑删除，提示客户档案不存在
        if (DeleteFlagEnum.DELETED.getValue().equals(bussCustomerInfo.getDeleteFlag())) {
            throw new SwPrpException(SwErrorCodeConstant.CUSTOMER_NOT_EXIST_ERROR);
        }
        // 查询客户--人员--权限
        QueryWrapper<BussCustomerPermission> queryWrapper = Wrappers.query();
        queryWrapper.lambda().eq(BussCustomerPermission::getCustomerId, bussCustomerInfo.getId());
        List<BussCustomerPermission> bussCustomerPermissions = bussCustomerPermissionMapper.selectList(queryWrapper);

        // 客户基本信息
        customerFbtTaskRule.setCustomerInfo(bussCustomerInfo);
        customerFbtTaskRule.setCustomerPermissions(bussCustomerPermissions);
        customerFbtTaskRule.start(); //执行接口
    }

    /** 
    * @Description: 删除指定客户现有权限
    * @Param: [customerId, permissionTypes]
    * @return: void
    * @Author: lwei
    * @Date: 2025/7/23
    */
    private void deleteCustomerPermissions(String customerId,String[] permissionTypes){
        QueryWrapper<BussCustomerPermission> wrapper = Wrappers.query();
        wrapper.lambda().eq(BussCustomerPermission::getCustomerId,customerId)
                .in(BussCustomerPermission::getPermissionType, permissionTypes);
        bussCustomerPermissionMapper.delete(wrapper);
    }

    /** 
    * @Description: 增加客户权限
    * @Param: [persons, customerId, permissionType]
    * @return: void
    * @Author: lwei
    * @Date: 2025/7/23
    */
    private void insertCustomerPermissions(String[] persons,String customerId,String permissionType){
        if (persons != null && persons.length > 0) {
            for (String person : persons) {
                if (StringUtils.isEmpty(person)) {
                    continue;
                }
                BussCustomerPermission tmp = new BussCustomerPermission();
                tmp.setId(MyUtil.getRandomID());
                tmp.setCustomerId(customerId);
                tmp.setPerson(person);
                tmp.setPermissionType(permissionType);

                tmp.setCreateBy(UserUtil.getLoginUserId()); // TODO 创建人，当前登录用户
                tmp.setCreateTime(DateUtil.now());
                tmp.setUpdateBy(UserUtil.getLoginUserId()); // TODO 创建人，当前登录用户
                tmp.setUpdateTime(DateUtil.now());
                bussCustomerPermissionMapper.insertSelective(tmp);
            }
        }
    }

    /**
     * 通过用户id来查找获取用户信息
     *
     * @param personId
     * @return
     */
    private SysUserVO queryUserById(String personId) {
        QueryUserByIdNameDTO idNameDTO = new QueryUserByIdNameDTO();
        try {
            idNameDTO.setId(Long.valueOf(personId));
            //调查询用户接口，传入用户id
            return userInfoService.queryUserByIdName(idNameDTO);
        } catch (Exception e) {
            return new SysUserVO();
        }

    }


    /**
     * @Description: 根据负责人id列表查询对应的记录id列表
     * @Param: [saleUserId]
     * @return: java.util.List<java.lang.String>
     * @Author: lwei
     * @Date: 2025/9/18
     */
    @Override
    public List<String> findBySaleUserIds(List<String> saleUserId) {

        if (CollUtil.isNotEmpty(saleUserId)) {
            List<String> ids = bussCustomerInfoMapper.selectListBySaleUserId(saleUserId);
            if (CollUtil.isNotEmpty(ids)) {
                return ids;
            }
        }
        return null;
    }

    /**
     * @Description: 根据创建者id列表，查询对应的数据id列表
     * @Param: [createId]
     * @return: java.util.List<java.lang.String>
     * @Author: lwei
     * @Date: 2025/9/18
     */
    @Override
    public List<String> findByCreateIds(List<String> createId) {
        if (CollUtil.isNotEmpty(createId)) {
            List<String> ids = bussCustomerInfoMapper.selectListByCreateId(createId);
            if (CollUtil.isNotEmpty(ids)) {
                return ids;
            }
        }
        return null;
    }

    /**
     * @Description: 根据id，更新审批状态
     * @Param: [processStatusDTO]
     * @return: void
     * @Author: lwei
     * @Date: 2025/9/19
     */
    @Override
    public void updateProcessStatusById(BussProcessStatusDTO processStatusDTO) {
        String id = processStatusDTO.getId();
        if (StringUtils.isNotBlank(id)) {
            BussCustomerInfoPO bussStatusInfo = new BussCustomerInfoPO();
            bussStatusInfo.setId(id);
            bussStatusInfo.setBussProcessStatus(processStatusDTO.getBussProcessStatus());
            bussCustomerInfoMapper.updateByPrimaryKeySelective(bussStatusInfo);
        }

    }

    /**
     * 根据客户名称模糊查询客户信息，用来判重
     *
     * @param requestParam 客户名称
     * @return
     */
    @Override
    public TableResultUtil checkRepeatByName(CheckRepeatCustomerNameDTO requestParam) {

        String customerName = requestParam.getCustomerName();

        // 开启分页
        Page<BussCustomerListVO> page = PageHelper.startPage(requestParam.getPageNum(), requestParam.getPageSize());
        QueryWrapper<BussCustomerInfoPO> wrapper = Wrappers.query();
        wrapper.lambda().ne(BussCustomerInfoPO::getDeleteFlag, DeleteFlagEnum.DELETED.getValue()); // 查询删除标记不为是的记录
        if (StringUtils.isNotEmpty(customerName)) {
            wrapper.lambda().like(BussCustomerInfoPO::getCustomerName, customerName);
        }
        // 按更新时间倒序查询
        wrapper.lambda().orderByDesc(BussCustomerInfoPO::getUpdateTime);
        List<BussCustomerInfoPO> customerInfoList = bussCustomerInfoMapper.selectList(wrapper);

        // 返回结果
        List<BussCustomerListVO> resList = new ArrayList<>();
        // 具体展示  客户名称、销售负责人
        // 查询内容转换为返回结果
        if (CollUtil.isNotEmpty(customerInfoList)) {
            for (BussCustomerInfoPO tmp : customerInfoList) {
                BussCustomerListVO bussCustomerListVO = new BussCustomerListVO();
                String customerId = tmp.getId();
                // 团队成员
//                bussCustomerListVO.setTeamMembers(queryTeamMembers(customerId));
                // 客户基本信息，放最后防止id覆盖
//                BeanUtils.copyProperties(tmp, bussCustomerListVO);
                bussCustomerListVO.setId(customerId);
                bussCustomerListVO.setCustomerCode(tmp.getCustomerCode());
                bussCustomerListVO.setCustomerName(tmp.getCustomerName());
                bussCustomerListVO.setSalesManager(tmp.getSalesManager());

                resList.add(bussCustomerListVO);
            }
        }

        // 封装返回结果
        PageInfo<BussCustomerListVO> pageInfo = new PageInfo<>(resList);
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());

        return TableResultUtil.buildTableResult(pageInfo);

    }

    /**
     * 检测两个对象中指定字段是否有变化
     *
     * @param original   原始对象(如数据库中的对象)
     * @param modified   修改后的对象(如传入的对象)
     * @param fieldNames 需要检测的字段名数组
     * @return 变化检测结果
     */
    private boolean hasFieldChanged(Object original, Object modified, String[] fieldNames) {
        // 参数校验
        if (original == null || modified == null) {
            return false;
        }
        // 如果是同一个对象，直接返回无变化
        if (ObjectUtil.equal(original, modified)) {
            return false;
        }
        // 转换为Map
        Map<String, Object> mapDb = BeanUtil.beanToMap(original, new HashMap<>(), false, true);
        Map<String, Object> mapReq = BeanUtil.beanToMap(modified, new HashMap<>(), false, true);

        // 只比较指定的字段
        for (String field : fieldNames) {
            Object valueOri = mapDb.get(field);
            Object valueMod = mapReq.get(field);

            if (!ObjectUtil.equal(valueOri, valueMod)) {
                return true;
            }
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAddCustomer(List<AddCustomerDTO> requestParamList) {
        if (CollUtil.isEmpty(requestParamList)) {
            return;
        }
        for (AddCustomerDTO requestParam : requestParamList) {
                add(requestParam);
        }
    }

}
