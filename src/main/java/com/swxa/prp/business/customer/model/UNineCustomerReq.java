package com.swxa.prp.business.customer.model;

import lombok.Data;

import java.util.List;

/**
 * U9 创建客户接口 入参
 * 修改客户开票信息 新增一个主体字段
 */
@Data
public class UNineCustomerReq {

    private String Code; // 客户代码 客户编码

    private String Name; // 客户名称

    private String ShortName; // 客户简称

    private ShippmentRule Org; //里面code设置U9各个公司主体代码，用来修改客户开票信息使用

    private boolean IsPriceListModify = true;

    private boolean IsShipmentModify = true;

    private ShippmentRule ShippmentRule = new ShippmentRule();

    private boolean IsRecTermModify = true;

    private RecervalTerm RecervalTerm = new RecervalTerm();

    private boolean IsARCfmModify = true;

    private ARConfirmTerm ARConfirmTerm = new ARConfirmTerm();

    private PayCurrency PayCurrency = new PayCurrency();

    private String StateTaxNo = "1"; //没有默认1，纳税人识别号

    private List<UNineCustomerSiteDTO> CustomerSiteDTOs;

    public static class ShippmentRule {
        private String Code = "YZ01";

        public String getCode() {
            return Code;
        }

        public void setCode(String code) {
            Code = code;
        }
    }

    public static class RecervalTerm {
        private String Code = "YZ01";

        public String getCode() {
            return Code;
        }

        public void setCode(String code) {
            Code = code;
        }
    }

    public static class ARConfirmTerm {
        private String Code = "YZ01";

        public String getCode() {
            return Code;
        }

        public void setCode(String code) {
            Code = code;
        }
    }

    public static class PayCurrency {
        private String Code = "C001";

        public String getCode() {
            return Code;
        }

        public void setCode(String code) {
            Code = code;
        }
    }

}
