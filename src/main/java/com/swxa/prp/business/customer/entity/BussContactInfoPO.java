package com.swxa.prp.business.customer.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.swxa.prp.datamask.SwSensitive;
import lombok.Data;

/**
 * @Description: 联系人实体类设计
 * @Author: zhangweicheng
 * @Date: 2025/7/22
 */

@Data
@TableName("t_buss_contact_info")
public class BussContactInfoPO {

    // 物理主键
    @TableId(type = IdType.INPUT)
    private String id;

    // 联系人编号
    private String contactCode;

    // 客户信息主键
    private String customerId;

    // 联系人姓名
    @SwSensitive(fieldName = "name")
    private String name;

    // 所在部门
    private String department;

    // 职务
    private String position;

    // 性别
    private String sex;

    // 手机号
    @SwSensitive(fieldName = "mobile")
    private String mobile;

    // 电话
    @SwSensitive(fieldName = "mobile")
    private String tel;

    // 微信
    @SwSensitive(fieldName = "mobile")
    private String wechat;

    // 上级领导
    private String superiorLeader;

    // 是否首要联系人
    private String primaryContactFlag;

    // 是否函证接收人
    private String confirmationReceiverFlag;

    // 接收函证类型
    private String confirmationType;

    // 备注
    private String remark;

    /**
     * 销售部门ID
     */
    private String salesManagerDeptId;

    /**
     * 销售部门
     */
    private String salesManagerDept;

    /**
     * 销售负责人ID
     */
    private String salesManagerId;

    /**
     * 销售负责人
     */
    private String salesManager;

    /**
     * 扩展字段1
     */
    private Integer extend1;

    // 扩展字段2
    private String extend2;

    // 扩展字段3
    private String extend3;

    // 创建人
    private String createBy;

    // 创建时间
    private String createTime;

    // 更新人
    private String updateBy;

    // 更新时间
    private String updateTime;
    /**
     * 审批流程状态
     *    ("1":"未发起"),
     *    ("2": "运行中"),  //此状态不允许编辑
     *    ("3":"已完成");
     */
    private String bussProcessStatus;
}