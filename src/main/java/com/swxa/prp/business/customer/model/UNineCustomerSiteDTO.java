package com.swxa.prp.business.customer.model;

import lombok.Data;

/**
 * U9 创建客户接口入参 客户站点类
 * 基本使用默认值
 */
@Data
public class UNineCustomerSiteDTO {
    private int OperateType = 1;
    private String Code;
    private OfficeSite OfficeSite = new OfficeSite();
    private boolean IsShipTo = true;
    private boolean IsDefaultShipTo = true;
    private boolean IsBillTo = true;
    private boolean IsDefaultBillTo = true;
    private boolean IsClaim = true;
    private boolean IsDefaultClaim = true;
    private boolean IsPayment = true;
    private boolean IsDefaultPayment = true;
    private boolean IsContrast = true;
    private boolean IsDefaultContrast = true;
    private boolean IsPriceListModify = true;
    private boolean IsShippmentRuleEditable = true;
    private boolean IsPaymentTermEditable = true;
    private PaymentTerm PaymentTerm = new PaymentTerm();
    private ARConfirmTerm ARConfirmTerm = new ARConfirmTerm();
    private String BuyerNoteName; //客户名称
    private String BuyerBankAccount = "2"; //客户开户银行，没有默认为2
    private String BuyerBankAccountCode = "3"; //开户银行账号，没有默认为3
    private String CheckTakerPhoneNo = "4"; //开票联系人电话，没有默认为4

    public static class OfficeSite {
        private String Code = "0004";

        public String getCode() {
            return Code;
        }

        public void setCode(String code) {
            Code = code;
        }
    }

    public static class PaymentTerm {
        private String Code = "YZ01";

        public String getCode() {
            return Code;
        }

        public void setCode(String code) {
            Code = code;
        }
    }

    public static class ARConfirmTerm {
        private String Code = "YZ01";

        public String getCode() {
            return Code;
        }

        public void setCode(String code) {
            Code = code;
        }
    }
}
