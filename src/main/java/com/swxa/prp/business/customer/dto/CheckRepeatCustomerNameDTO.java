package com.swxa.prp.business.customer.dto;

import com.swxa.prp.business.common.dto.BasePageParamDTO;
import lombok.Data;

/**
* @Description: 客户名称 查重 入参
* @Author: zzy
* @Date: 2025/9/22 19:26
*/
@Data
public class CheckRepeatCustomerNameDTO extends BasePageParamDTO {

    // 客户名称
    private String customerName;

    //true是脱敏
    private Boolean isMask=true;

    //实体类字段名称
    private String filedName;
}
