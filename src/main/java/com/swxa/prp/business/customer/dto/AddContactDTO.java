package com.swxa.prp.business.customer.dto;

import com.swxa.prp.constant.SwErrorCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class AddContactDTO {

    private  String contactCode;

    // 客户信息主键
    @NotEmpty(message = SwErrorCodeConstant.CUSTOMER_ID_NULL_ERROR)

    private String customerId;

    // 联系人姓名
    @NotEmpty(message = SwErrorCodeConstant.CONTACT_NAME_NULL_ERROR)
    private String name;

    // 所在部门
    private String department;

    // 职务
    private String position;

    // 性别
    private String sex;

    // 手机号
    private String mobile;

    // 电话
    private String tel;

    // 微信
    private String wechat;

    // 上级领导
    private String superiorLeader;

    // 是否首要联系人
    private String primaryContactFlag;

    // 是否函证接收人
    private String confirmationReceiverFlag;

    // 接收函证类型
    private String confirmationType;

    // 备注
    private String remark;
    /**
     * 销售部门ID
     */
    private String salesManagerDeptId;

    /**
     * 销售部门
     */
    private String salesManagerDept;

    /**
     * 销售负责人ID
     */
    private String salesManagerId;

    /**
     * 销售负责人
     */
    private String salesManager;
}
