package com.swxa.prp.business.register.controller;

import com.swxa.prp.business.register.model.UpdatePwd;
import com.swxa.prp.business.register.service.RegisterService;
import com.swxa.prp.log.SwLogger;
import com.swxa.prp.model.SysUser;
import com.swxa.prp.util.ResponseData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * @Description: 注册
 * @Author: zhangweicheng
 * @Date: 2024/6/2
 */

@Controller
@RequestMapping(value = "/register")
public class RegisterController {

    @Autowired
    private RegisterService registerService;

    /**
     * @Description: 注册用户
     * @Param: [sysUser]
     * @return: com.swxa.prp.util.JsonMsgUtil
     * @Author: zhangweicheng
     * @Date: 2024/6/2
     */

    @ResponseBody
    @PostMapping(value = "/registerUser")
    public ResponseData registerUser(SysUser sysUser) {
        SwLogger.info("测试");
        registerService.registerUser(sysUser);
        return new ResponseData(true, "", null);
    }


    /**
     * @Description: 重置密码时 获取验证码
     * @Param: [email]
     * @return: com.swxa.prp.util.JsonMsgUtil
     * @Author: zhangweicheng
     * @Date: 2024/6/4
     */

    @ResponseBody
    @PostMapping(value = "/resetCode")
    public ResponseData resetCode(@RequestParam("email") String email) {
        registerService.resetCode(email);
        return new ResponseData(true, "Please go to the email to check the verification code.", null);
    }


    /**
     * @Description: 重置密码
     * @Param: [email]
     * @return: com.swxa.prp.util.JsonMsgUtil
     * @Author: zhangweicheng
     * @Date: 2024/6/5
     */

    @ResponseBody
    @PostMapping(value = "/resetPwd")
    public ResponseData resetPwd(UpdatePwd updatePwd) {
        registerService.resetPwd(updatePwd);
        return new ResponseData(true, "Successfully reset password, please go to Login.", null);
    }


}
