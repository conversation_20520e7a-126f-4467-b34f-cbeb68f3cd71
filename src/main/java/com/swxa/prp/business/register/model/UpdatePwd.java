package com.swxa.prp.business.register.model;

import org.apache.commons.lang3.StringUtils;

/**
 * @Description: 更新密码
 * @Author: zhangweicheng
 * @Date: 2024/6/5
 */

public class UpdatePwd {

    private String email;
    private String checkCode;
    private String password;
    private String password1;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCheckCode() {
        return checkCode;
    }

    public void setCheckCode(String checkCode) {
        this.checkCode = checkCode;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPassword1() {
        return password1;
    }

    public void setPassword1(String password1) {
        this.password1 = password1;
    }

    public UpdatePwd() {
    }

    public UpdatePwd(String email, String checkCode, String password, String password1) {
        this.email = email;
        this.checkCode = checkCode;
        this.password = password;
        this.password1 = password1;
    }

    /**
     * @Description: 校验信息不为空
     * @Param: []
     * @return: void
     * @Author: zhangweicheng
     * @Date: 2024/6/5
     */

    public void checkNoneParam() {
        if (StringUtils.isBlank(this.email)) {
            throw new IllegalArgumentException("Email must be filled out.");
        }
        if (StringUtils.isBlank(this.checkCode)) {
            throw new IllegalArgumentException("Verification code must be filled out.");
        }

        if (StringUtils.isBlank(password) || StringUtils.isBlank(password1)) {
            throw new IllegalArgumentException("Password must be filled out.");
        }
    }
}
