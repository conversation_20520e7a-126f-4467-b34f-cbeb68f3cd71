package com.swxa.prp.business.register.service;


import com.swxa.prp.business.register.model.UpdatePwd;
import com.swxa.prp.model.SysUser;

public interface RegisterService {


    /** 
    * @Description: 注册用户
    * @Param: [sysUser]
    * @return: void
    * @Author: zhangweicheng
    * @Date: 2024/6/2
    */
    
    void registerUser(SysUser sysUser);

    /** 
    * @Description: 重置密码时 获取验证码
    * @Param: [email]
    * @return: void
    * @Author: zhangweicheng
    * @Date: 2024/6/4
    */
    
    void resetCode(String email);

    /** 
    * @Description: 重置密码
    * @Param: [updatePwd]
    * @return: void
    * @Author: zhangweicheng
    * @Date: 2024/6/5
    */
    
    void resetPwd(UpdatePwd updatePwd);
}
