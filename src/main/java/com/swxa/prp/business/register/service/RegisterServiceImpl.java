package com.swxa.prp.business.register.service;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.swxa.prp.business.mail.service.IEmailService;
import com.swxa.prp.business.register.model.UpdatePwd;
import com.swxa.prp.business.usercode.model.UserCode;
import com.swxa.prp.business.usercode.service.UserCodeService;
import com.swxa.prp.constant.SwErrorCodeConstant;
import com.swxa.prp.exception.SwPrpException;
import com.swxa.prp.log.SwLogger;
import com.swxa.prp.model.SysRole;
import com.swxa.prp.model.SysRoleUser;
import com.swxa.prp.model.SysUser;
import com.swxa.prp.model.dto.UserQueryDTO;
import com.swxa.prp.service.RoleService;
import com.swxa.prp.service.SysRoleUserService;
import com.swxa.prp.service.UserService;
import com.swxa.prp.util.CheckCodeUtil;
import com.swxa.prp.util.MyDateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class RegisterServiceImpl implements RegisterService {

    @Autowired
    private UserService userService;
    @Autowired
    private RoleService roleService;
    @Autowired
    private SysRoleUserService sysRoleUserService;
    @Autowired
    private IEmailService emailService;
    @Autowired
    private UserCodeService userCodeService;

    private static final String ROLE_NAME = "default";

    @Transactional
    @Override
    public void registerUser(SysUser sysUser) {


        sysUser.frontCheckNoneEmptyParam();

        if (!sysUser.getPassword().equals(sysUser.getPassword1())) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_0A030001);
            //throw new IllegalArgumentException("Passwords do not match. Please try again.");
        }

        //查询用户是不是已经存在
        UserQueryDTO userQueryDTO = new UserQueryDTO();
        userQueryDTO.setUsernameEqual(sysUser.getEmail().trim());
        List<SysUser> sysUsers = userService.queryList(userQueryDTO);
        if (sysUsers != null && !sysUsers.isEmpty()) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_0A030007);
            //throw new IllegalArgumentException("error,User [" + sysUser.getEmail() + "] already exists.");
        }


        sysUser.setUsername(sysUser.getEmail());
        sysUser.setUpdatePwdFlag("1");
        userService.saveUser(sysUser);

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("name", ROLE_NAME);
        List<SysRole> sysRoles = roleService.find(paramMap);
        if (sysRoles == null || sysRoles.isEmpty()) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_0A030008);
            //throw new IllegalArgumentException("User register error...");
        }
        SysRole sysRole = sysRoles.get(0);

        //保存用户角色得关系
        SysRoleUser sysRoleUser = new SysRoleUser();
        sysRoleUser.setUserId(sysUser.getId());
        sysRoleUser.setRoleId(sysRole.getId());
        sysRoleUserService.batchInsert(Arrays.asList(sysRoleUser));

        //注册
        try {
            emailService.registerEmail(sysUser);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Transactional
    @Override
    public void resetCode(String email) {
        if (StringUtils.isBlank(email)) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_0A030004);
            //throw new IllegalArgumentException("error,The Email cannot be empty.");
        }

        SysUser user = userService.getUser(email.trim());
        if (user == null) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_0A030010);
            //throw new IllegalArgumentException("error,The User does not exist.");
        }

        String userId = user.getId().toString();
        String type = "EMAIL";
        String timeDay = MyDateUtil.formatDate(new Date(), "yyyy-MM-dd");

        //先查询是否当天超过10次了
        UserCode record = new UserCode();
        record.setType(type);
        record.setName(userId);
        record.setCreateTimeDayFormat(timeDay);

        List<UserCode> userCodes = userCodeService.query(record);
        if (userCodes != null && userCodes.size() >= 10) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_0A030011);
            //throw new IllegalArgumentException("error,Password reset more than 10 times, Today.");
        }

        //生成验证码
        String code = CheckCodeUtil.buildRandomCode();
        if (StringUtils.isBlank(code)) {
            code = "Swxa12345.";
        }

        record.setCreateTime(new Date());
        record.setCode(code);
        //保存验证码
        userCodeService.insertSelective(record);

        //建立用户和验证码之间的关系   注意十分钟有效  一天重置最多10次
        //发邮件
        emailService.resetPwd(email, code, user.getNickname() + " " + user.getLastname());
    }


    @Override
    public void resetPwd(UpdatePwd updatePwd) {

        //校验信息是不是都存在
        updatePwd.checkNoneParam();
        //2.新旧密码是不是一致
        if (!updatePwd.getPassword().equals(updatePwd.getPassword1())) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_0A030013);
            //throw new IllegalArgumentException("error,The passwords are inconsistent.");
        }


        //查询用户是不是已经存在
        UserQueryDTO userQueryDTO = new UserQueryDTO();
        userQueryDTO.setUsernameEqual(updatePwd.getEmail().trim());
        List<SysUser> sysUsers = userService.queryList(userQueryDTO);
        if (sysUsers == null || sysUsers.isEmpty()) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_0A030010);
            //throw new IllegalArgumentException("error,User [" + updatePwd.getEmail() + "]  does not exist.");
        }
        SysUser sysUser = sysUsers.get(0);


        //1.先查看校验码是不是有效，是不是存在   时间问题是不是小于10分钟
        UserCode record = new UserCode();
        record.setType("EMAIL");
        record.setName(sysUser.getId().toString());
        record.setCode(updatePwd.getCheckCode());
        List<UserCode> userCodes = userCodeService.query(record);
        if (userCodes == null || userCodes.isEmpty()) {
            throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_0A030014);
            //throw new IllegalArgumentException("error,Invalid verification code.");
        }
        Date createTime = userCodes.get(0).getCreateTime();

        //差的分钟数
        Long betweenMinutes = DateUtil.between(createTime, new Date(), DateUnit.MINUTE);

        if (betweenMinutes != null) {
            int minutes = Integer.parseInt(betweenMinutes.toString());
            if (minutes > 10) {
                throw new SwPrpException(SwErrorCodeConstant.ERROR_CODE_0A030015);
                //throw new IllegalArgumentException("error,The verification code has been invalid for more than 10 minutes.");
            }
        }

        //更新密码
        userService.resetPassword(sysUser.getId(), updatePwd.getPassword(), "1");

    }


}
