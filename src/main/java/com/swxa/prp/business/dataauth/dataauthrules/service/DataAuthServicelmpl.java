package com.swxa.prp.business.dataauth.dataauthrules.service;

import com.swxa.prp.business.dataauth._enum.BussOperateDDLTypeEnum;
import com.swxa.prp.business.dataauth.dataauthrules.inter.DataAuthRuleInterface;
import com.swxa.prp.constant.SwErrorCodeConstant;
import com.swxa.prp.exception.SwPrpException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @Description: 数据授权服务
 * @Author: zhangweicheng
 * @Date: 2025/8/19
 */

@Service
public class DataAuthServicelmpl implements DataAuthService {


    private static final Logger log = LoggerFactory.getLogger("dayLogger");


    private final Map<String, DataAuthRuleInterface> dataAuthRuleInterface;

    public DataAuthServicelmpl(@Autowired Map<String, DataAuthRuleInterface> dataAuthRuleInterface) {
        this.dataAuthRuleInterface = dataAuthRuleInterface;
    }


    /**
     * @Description: 查询业务处理类
     * @Param: [actTypeValue]
     * @return: com.swxa.prp.business.dataauth.dataauthrules.inter.DataAuthRuleInterface
     * @Author: zhangweicheng
     * @Date: 2025/9/16
     */

    private DataAuthRuleInterface getDataAuthRuleInterface(String actTypeValue) {
        if (StringUtils.isNotBlank(actTypeValue)) {
            if (dataAuthRuleInterface != null && !dataAuthRuleInterface.isEmpty()) {
                for (Map.Entry<String, DataAuthRuleInterface> entry : dataAuthRuleInterface.entrySet()) {
                    String code = entry.getValue().getCode();
                    if (actTypeValue.equals(code)) {
                        DataAuthRuleInterface value = entry.getValue();
                        return value;
                    }
                }
            }
        }
        return null;
    }

    /**
     * @Description: 根据业务类型获取销售负责人的用户ids
     * @Param: [actTypeValue]
     * @return: java.util.List<java.lang.String>
     * @Author: zhangweicheng
     * @Date: 2025/9/16
     */

    @Override
    public List<String> getBussUserIds(String actTypeValue) {
        DataAuthRuleInterface dataAuthRuleInterface = getDataAuthRuleInterface(actTypeValue);
        if (Objects.nonNull(dataAuthRuleInterface)) {
            return dataAuthRuleInterface.getBussUserIds();
        }

        return new ArrayList<>();
    }

    @Override
    public List<String> getBussIds(String actTypeValue) {
        if (StringUtils.isNotBlank(actTypeValue)) {
            if (dataAuthRuleInterface != null && !dataAuthRuleInterface.isEmpty()) {
                for (Map.Entry<String, DataAuthRuleInterface> entry : dataAuthRuleInterface.entrySet()) {
                    String code = entry.getValue().getCode();
                    if (actTypeValue.equals(code)) {
                        DataAuthRuleInterface value = entry.getValue();
                        return value.getBussIds();
                    }
                }
            }
        }
        return null;
    }

    @Override
    public String getSaleUserId(String actTypeValue, String currBussId) {
        return null;
    }

    @Override
    public Boolean doEditOrDelete(String actTypeValue, String currBussId, BussOperateDDLTypeEnum bussOperateDDLTypeEnum) {
        DataAuthRuleInterface dataAuthRuleInterface = getDataAuthRuleInterface(actTypeValue);
        if (Objects.nonNull(dataAuthRuleInterface)) {
            Boolean aBoolean = dataAuthRuleInterface.doEditOrDelete(currBussId, bussOperateDDLTypeEnum);
            if (!aBoolean) {
                throw new SwPrpException(SwErrorCodeConstant.ERROR_NO_AUTH_OPERATE);
            }
            return true;
        }
        return false;
    }

}
