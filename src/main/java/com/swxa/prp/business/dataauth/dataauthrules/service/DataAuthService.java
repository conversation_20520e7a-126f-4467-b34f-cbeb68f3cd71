package com.swxa.prp.business.dataauth.dataauthrules.service;

import com.swxa.prp.business.dataauth._enum.BussOperateDDLTypeEnum;

import java.util.List;

/**
 * @Description: 数据授权服务
 * @Author: zhangweicheng
 * @Date: 2025/8/19
 */

public interface DataAuthService {


    /**
     * @Description: 获取销售负责人的ids，注意知识管理、产品管理、产品价格服务说明、开票税收信息、项目认证  这些是查看创建者的，其余是查看销售负责人的  获取的是创建者的id
     * @Param: [actTypeValue]
     * @return: java.util.List<java.lang.String>
     * @Author: zhangweicheng
     * @Date: 2025/9/16
     */

    List<String> getBussUserIds(String actTypeValue);


    /**
     * @Description: 根据业务类型查询业务IDS
     * @Param: [actTypeValue]
     * @return: java.util.List<java.lang.String>
     * @Author: zhangweicheng
     * @Date: 2025/9/18
     */

    List<String> getBussIds(String actTypeValue);


    /**
     * @Description: 获取该业务对应的销售负责人id, 没有的返回null
     * @Param: [actTypeValue, currBussId]
     * @return: java.lang.String
     * @Author: zhangweicheng
     * @Date: 2025/9/16
     */

    String getSaleUserId(String actTypeValue, String currBussId);

    /**
     * @Description:查询能不能编辑自己创建的、编辑别人的、删除别人的；知识管理、产品管理、产品价格服务说明、开票税收信息、项目授权 这些是查看创建者的，其余是查看销售负责人的
     * 判断当前记录的销售负责人或者创建者,是不是跟当前用户对应
     * 得有权限分配了才行
     * @Param: [actTypeValue, currBussId]
     * @return: java.lang.String
     * @Author: zhangweicheng
     * @Date: 2025/9/16
     */

    Boolean doEditOrDelete(String actTypeValue, String currBussId, BussOperateDDLTypeEnum bussOperateDDLTypeEnum);
}
