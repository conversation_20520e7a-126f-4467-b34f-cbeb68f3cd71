package com.swxa.prp.business.dataauth.dataauthrules.inter;

import com.swxa.prp.business.dataauth._enum.BussOperateDDLTypeEnum;

import java.util.List;

/**
 * @Description: 定义每个业务流程字段条件
 * @Author: zhangweicheng
 * @Date: 2025/8/19
 */

public interface DataAuthRuleInterface {


    /**
     * @Description: 获取流程条件定义的编码，每个业务对应一个编码：ActProcessTypeEnum
     * @Param: []
     * @return: java.lang.String
     * @Author: zhangweicheng
     * @Date: 2025/8/19
     */

    String getCode();


    /**
     * @Description: 获取销售负责人的ids
     * 1.用户信息里面配置查看谁的：个人的、所有成员的、指定人员的、按照部门；这里是从用户信息里面获取的，如果为空说明获取所有的，否则用户信息里面会游指定的用户id
     * 2.产品管理、产品价格服务说明、开票税收信息  这些是查看创建者的，其余是查看销售负责人的,知识管理:是查看全部的
     * 3.注意再分配的时候，不要光考虑权限 还得考虑分享
     * @Param: []
     * @return: java.util.List<java.lang.String>
     * @Author: zhangweicheng
     * @Date: 2025/9/16
     */

    List<String> getBussUserIds();

    /**
     * @Description: 获取业务IDS, 返回值为空代表查询所有的，否则查询指定的id
     * 注意再分配的时候，不要光考虑权限 还得考虑分享;权限里面考虑俩个因素，一个是按照销售负责人或者按照创建者，还有一个就是允许查看自己创建的，这个时候也得考虑
     * @Param: []
     * @return: java.util.List<java.lang.String>
     * @Author: zhangweicheng
     * @Date: 2025/9/17
     */

    List<String> getBussIds();

    /**
     * @Description:查询能不能编辑自己创建的、编辑别人的、删除别人的；知识管理、产品管理、产品价格服务说明、开票税收信息、项目授权 这些是查看创建者的，其余是查看销售负责人的
     * 判断当前记录的销售负责人或者创建者,是不是跟当前用户对应
     * 得有权限分配了才行
     * 首先数据授权里面要有编辑和删除的功能；其次是编辑和删除的记录是不是在你的范围内
     * @Param: [actTypeValue, currBussId]
     * @return: java.lang.String
     * @Author: zhangweicheng
     * @Date: 2025/9/16
     */

    Boolean doEditOrDelete(String currBussId, BussOperateDDLTypeEnum bussOperateDDLTypeEnum);

    /**
     * @Description: 查询能不能编辑自己创建的、编辑别人的、删除别人的；知识管理、产品管理、产品价格服务说明、开票税收信息、项目授权 这些是查看创建者的，其余是查看销售负责人的
     * @Param: [currBussId]
     * @return: java.lang.String
     * @Author: zhangweicheng
     * @Date: 2025/9/16
     */

    String queryUserId(String currBussId);


    /**
     * @Description: 通过销售负责人的id查询，业务的id集合：产品管理、开票税收信息、项目授权   这个就返回空
     * <p>
     * 产品价格服务说明:这个是有负责人的
     * @Param: [saleUserId]
     * @return: java.util.List<java.lang.String>
     * @Author: zhangweicheng
     * @Date: 2025/9/17
     */

    List<String> queryBussIdsBySalesId(List<String> saleUserId);


    /**
     * @Description: 通过创建者的id查询，业务的id集合,每个表里面创建者的id，必须得存在
     * @Param: [saleUserId]
     * @return: java.util.List<java.lang.String>
     * @Author: zhangweicheng
     * @Date: 2025/9/17
     */

    List<String> queryBussIdsBycreateId(List<String> createId);
}
