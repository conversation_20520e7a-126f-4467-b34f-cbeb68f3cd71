package com.swxa.prp.business.dataauth._enum;


import java.util.HashMap;
import java.util.Map;

/**
 * @Description:授权业务枚举：这个里面的枚举就是 crm页面的左侧菜单的信息，检查一下对应起来
 * @Author: zhangweicheng
 * @Date: 2025/9/15
 */


public enum CrmBussTypeEnum {

    businessContact("businessContact", "联系人"),
    deviceRecord("deviceRecord", "设备档案"),
    returnProduct("returnProduct", "返回产品跟进"),
    shippingNotice("shippingNotice", "发货通知单"),
    transferOrder("transferOrder", "调货单"),
    SupplySpecial("SupplySpecial", "特殊供货申请"),
    contractInternal("contractInternal", "内部关联合同"),
    contractReceipt("contractReceipt", "回款单"),
    contractSaleInvoice("contractSaleInvoice", "销售发票"),
    contractPaymentPlan("contractPaymentPlan", "回款计划"),
    contractOrder("contractOrder", "合同订单"),
    advanceOrder("advanceOrder", "垫资订单"),
    projectAuth("projectAuth", "项目授权"),
    businessOpportunity("businessOpportunity", "项目机会跟进"),
    businessCustomerManage("businessCustomerManage", "客户档案"),
    productManage("productManage", "产品管理"),
    hwswService("hwswService", "产品服务价格说明"),
    //invoiceTaxInfo("invoiceTaxInfo", "开票税收信息"),
    knowledgeManage("knowledgeManage", "知识管理");
   ;
    private String code;

    private String name;


    private static Map<String, String> map = new HashMap<String, String>();

    static {
        for (CrmBussTypeEnum s : CrmBussTypeEnum.values()) {
            map.put(s.getCode(), s.getName());
        }
    }


    /**
     * @Description: 根据code获取名称
     * @Param: [code]
     * @return: java.lang.String
     * @Author: zhangweicheng
     * @Date: 2025/9/5
     */

    public static String getName(String code) {
        for (CrmBussTypeEnum s : CrmBussTypeEnum.values()) {
            if (s.getCode().equals(code)) {
                return s.getName();
            }
        }
        return null;
    }

    public static Map<String, String> map() {
        return map;
    }


    CrmBussTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
