package com.swxa.prp.business.dataauth._enum;


import java.util.HashMap;
import java.util.Map;

/**
 * @Description: 业务逻辑：编辑和删除标识
 * @Author: zhangweicheng
 * @Date: 2025/9/18
 */


public enum BussOperateDDLTypeEnum {

    EDIT("edit", "编辑"),
    DELETE("delete", "删除");
    private String code;

    private String name;


    private static Map<String, String> map = new HashMap<String, String>();

    static {
        for (BussOperateDDLTypeEnum s : BussOperateDDLTypeEnum.values()) {
            map.put(s.getCode(), s.getName());
        }
    }


    /**
     * @Description: 根据code获取名称
     * @Param: [code]
     * @return: java.lang.String
     * @Author: zhangweicheng
     * @Date: 2025/9/5
     */

    public static String getName(String code) {
        for (BussOperateDDLTypeEnum s : BussOperateDDLTypeEnum.values()) {
            if (s.getCode().equals(code)) {
                return s.getName();
            }
        }
        return null;
    }

    public static Map<String, String> map() {
        return map;
    }


    BussOperateDDLTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
