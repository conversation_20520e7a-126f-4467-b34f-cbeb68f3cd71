package com.swxa.prp.business.dataauth.dataauthrules.rule;

import cn.hutool.core.collection.CollUtil;
import com.swxa.prp.business.advancefunding.request.AdvanceFundingOrderIdDTO;
import com.swxa.prp.business.advancefunding.response.AdvanceFundingOrderDetailVO;
import com.swxa.prp.business.auth.dto.ProjectAuthIdDTO;
import com.swxa.prp.business.auth.service.ProjectAuthService;
import com.swxa.prp.business.auth.vo.ProjectAuthDetailVO;
import com.swxa.prp.business.data.dto.ShareDataQueryDTO;
import com.swxa.prp.business.data.service.ShareDataService;
import com.swxa.prp.business.data.vo.ShareDataListVO;
import com.swxa.prp.business.dataauth._enum.BussOperateDDLTypeEnum;
import com.swxa.prp.business.dataauth._enum.CrmBussTypeEnum;
import com.swxa.prp.business.dataauth._enum.CrmDataOperateEnum;
import com.swxa.prp.business.dataauth.dataauthrules.inter.DataAuthRuleInterface;
import com.swxa.prp.business.dataauth.dto.CrmDataUserAuthDTO;
import com.swxa.prp.business.dataauth.vo.CrmDataAuthVO;
import com.swxa.prp.entity.LoginUser;
import com.swxa.prp.util.UserUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @Description: 项目授权
 * @Author: zhangweicheng
 * @Date: 2025/9/5
 */

@Service
public class DataAuthProjectAuthConditionRule implements DataAuthRuleInterface {


    @Autowired
    private ProjectAuthService projectAuthService;
    @Autowired
    private ShareDataService shareDataService;

    private static final Logger log = LoggerFactory.getLogger("dayLogger");


    @Override
    public String getCode() {
        return CrmBussTypeEnum.projectAuth.getCode();
    }

    @Override
    public List<String> getBussUserIds() {
        return null;
    }

    @Override
    public List<String> getBussIds() {
        //业务ids，返回结果不可能为空，为空直接返回默认
        List<String> bussIds = new ArrayList<>();

        //一、根据数据授权，查询业务id
        LoginUser loginUser = UserUtil.getLoginUser();
        if (Objects.nonNull(loginUser)) {
            CrmDataAuthVO crmAuthData = loginUser.getCrmAuthData();
            if (Objects.nonNull(crmAuthData)) {

                List<CrmDataUserAuthDTO> crmUserAuthDatas = crmAuthData.getCrmUserAuthDatas();
                if (CollUtil.isNotEmpty(crmUserAuthDatas)) {
                    //循环处理查询业务ids
                    for (CrmDataUserAuthDTO crmDataUserAuthDTO : crmUserAuthDatas) {
                        String bussType = crmDataUserAuthDTO.getBussType();
                        if (CrmBussTypeEnum.projectAuth.getCode().equals(bussType)) {//
                            List<String> idsBycreateId = queryBussIdsBycreateId(Arrays.asList(loginUser.getId().toString()));
                            if (CollUtil.isNotEmpty(idsBycreateId)) {
                                bussIds.addAll(idsBycreateId);
                            }
                            break;
                        }
                    }
                }
            }
        }

        //根据分享，查询业务id
        ShareDataQueryDTO shareDataQueryDTO = new ShareDataQueryDTO();
        shareDataQueryDTO.setUserId(loginUser.getId().toString());
        List<ShareDataListVO> shareDataListVOS = shareDataService.queryList(shareDataQueryDTO);
        if (CollUtil.isNotEmpty(shareDataListVOS)) {
            for (ShareDataListVO shareDataListVO : shareDataListVOS) {
                String bussId = shareDataListVO.getBussId();
                if (!bussIds.contains(bussId)) {
                    bussIds.add(bussId);
                }
            }
        }

        if (CollUtil.isNotEmpty(bussIds)) {
            return bussIds;
        }

        //直接返回一个iD,匹配不到任何结果，不允许返回为空的信息
        return Arrays.asList("defaultId");
    }


    @Override
    public Boolean doEditOrDelete(String currBussId, BussOperateDDLTypeEnum bussOperateDDLTypeEnum) {


        //通过销售负责人的id查询，业务的id集合：知识管理、产品管理、产品价格服务说明、开票税收信息、项目授权   这个就返回空

        //一、根据数据授权，查询业务id
        LoginUser loginUser = UserUtil.getLoginUser();
        if (Objects.nonNull(loginUser)) {
            CrmDataAuthVO crmAuthData = loginUser.getCrmAuthData();
            if (Objects.nonNull(crmAuthData)) {

                List<CrmDataUserAuthDTO> crmUserAuthDatas = crmAuthData.getCrmUserAuthDatas();
                if (CollUtil.isNotEmpty(crmUserAuthDatas)) {
                    //循环处理查询业务ids
                    for (CrmDataUserAuthDTO crmDataUserAuthDTO : crmUserAuthDatas) {
                        String bussType = crmDataUserAuthDTO.getBussType();
                        if (CrmBussTypeEnum.projectAuth.getCode().equals(bussType)) {

                            //1.查询操作授权
                            List<String> operateRanges = crmDataUserAuthDTO.getOperateRanges();
                            //2.是授权里面的信息
                            if (CollUtil.isEmpty(operateRanges)) {
                                operateRanges = new ArrayList<>();
                            }
                            return queryOperateEditDeleteAuth(loginUser, currBussId, bussOperateDDLTypeEnum, operateRanges);
                        }
                    }
                }
            }
        }

        return false;
    }

    /**
     * @Description: 查询
     * @Param: [currBussId, code, operateRanges]
     * @return: java.lang.Boolean
     * @Author: zhangweicheng
     * @Date: 2025/9/18
     */
    private Boolean queryOperateEditDeleteAuth(LoginUser loginUser, String currBussId, BussOperateDDLTypeEnum bussOperateDDLTypeEnum, List<String> operateRanges) {
        //是否具备编辑权限
        Boolean doEdit = false;

        ProjectAuthDetailVO advanceFundingOrderDetailVO = queryBussData(currBussId);

        //只要自己创建的就可以编辑，不管什么权限
        if (Objects.nonNull(advanceFundingOrderDetailVO)) {
            String handler = advanceFundingOrderDetailVO.getRequester();
            if (loginUser.getId().toString().equals(handler)) {
                doEdit = true;
            }
        }
        return doEdit;
    }


    private ProjectAuthDetailVO queryBussData(String currBussId) {
        ProjectAuthIdDTO projectAuthIdDTO = new ProjectAuthIdDTO();
        projectAuthIdDTO.setId(currBussId);
        ProjectAuthDetailVO projectAuthDetailVO = projectAuthService.findById(projectAuthIdDTO);
        if (Objects.nonNull(projectAuthDetailVO)) {
            return projectAuthDetailVO; //申请人
        }
        return null;
    }


    @Override
    public String queryUserId(String currBussId) {
        ProjectAuthIdDTO projectAuthIdDTO = new ProjectAuthIdDTO();
        projectAuthIdDTO.setId(currBussId);
        ProjectAuthDetailVO projectAuthDetailVO = projectAuthService.findById(projectAuthIdDTO);
        if (Objects.nonNull(projectAuthDetailVO)) {
            return projectAuthDetailVO.getRequester(); //申请人
        }
        return null;
    }

    /**
     * @Description: 通过销售负责人的id查询业务ids，不存在返回空
     * @Param: [saleUserId]
     * @return: java.util.List<java.lang.String>
     * @Author: zhangweicheng
     * @Date: 2025/9/17
     */

    @Override
    public List<String> queryBussIdsBySalesId(List<String> saleUserId) {
        //没有销售负责人id，直接返回null
        return null;
    }

    /**
     * @Description: 通过创建者的id查询业务的ids，不存在返回空
     * @Param: [createId]
     * @return: java.util.List<java.lang.String>
     * @Author: zhangweicheng
     * @Date: 2025/9/17
     */


    @Override
    public List<String> queryBussIdsBycreateId(List<String> createId) {

        if (CollUtil.isNotEmpty(createId)) {
            return projectAuthService.findByCreateIds(createId);

        }
        return null;
    }
}
