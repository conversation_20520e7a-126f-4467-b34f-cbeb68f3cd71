package com.swxa.prp.business.dataauth.dataauthrules.vo;

import lombok.Data;

/**
 * @Description: 数据授权返回用户ids集合
 * @Author: zhangweicheng
 * @Date: 2025/9/5
 */

@Data
public class DataAuthUserIdsVO {

    //按照字段的顺序排序
    private Integer formId;
    //数字类型是10001，字符串类型是1005
    private String columnId;
    //数字类型是4，字符串是1
    private Integer showType;
    //字段显示的名字
    private String showName;
    //对应数据库字段
    private String columnName;
    private String columnType = "String";
    //数字类型是：input-number，其余是input
    private String fieldTypeName;
    //写死就是空
    private String fixedDownBoxValue = "";


    public static DataAuthUserIdsVO buildNumField(String showName, String columnName, Integer formId) {
        DataAuthUserIdsVO actConditionFieldVO = new DataAuthUserIdsVO();
        actConditionFieldVO.setFormId(formId);
        actConditionFieldVO.setColumnId("10001");
        actConditionFieldVO.setShowType(4);
        actConditionFieldVO.setShowName(showName);
        actConditionFieldVO.setColumnName(columnName);
        actConditionFieldVO.setFieldTypeName("input-number");
        return actConditionFieldVO;
    }

    public static DataAuthUserIdsVO buildStringField(String showName, String columnName, Integer formId) {
        DataAuthUserIdsVO actConditionFieldVO = new DataAuthUserIdsVO();
        actConditionFieldVO.setFormId(formId);
        actConditionFieldVO.setColumnId("10005");
        actConditionFieldVO.setShowType(1);
        actConditionFieldVO.setShowName(showName);
        actConditionFieldVO.setColumnName(columnName);
        actConditionFieldVO.setFieldTypeName("input");
        return actConditionFieldVO;
    }

}
