package com.swxa.prp.business.dataauth.dataauthrules.rule;

import cn.hutool.core.collection.CollUtil;
import com.swxa.prp.business.advancefunding.request.AdvanceFundingOrderIdDTO;
import com.swxa.prp.business.advancefunding.response.AdvanceFundingOrderDetailVO;
import com.swxa.prp.business.advancefunding.service.AdvanceFundingOrderService;
import com.swxa.prp.business.data.dto.ShareDataQueryDTO;
import com.swxa.prp.business.data.service.ShareDataService;
import com.swxa.prp.business.data.vo.ShareDataListVO;
import com.swxa.prp.business.dataauth._enum.BussOperateDDLTypeEnum;
import com.swxa.prp.business.dataauth._enum.CrmBussTypeEnum;
import com.swxa.prp.business.dataauth._enum.CrmDataOperateEnum;
import com.swxa.prp.business.dataauth.dataauthrules.inter.DataAuthRuleInterface;
import com.swxa.prp.business.dataauth.dto.CrmDataUserAuthDTO;
import com.swxa.prp.business.dataauth.vo.CrmDataAuthVO;
import com.swxa.prp.entity.LoginUser;
import com.swxa.prp.util.UserUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 垫资订单
 * @Param:
 * @return:
 * @Author: lidi
 * @Date: 2025/9/11
 */

@Service
public class DataAuthAdvanceOrderConditionRule implements DataAuthRuleInterface {

    @Autowired
    private AdvanceFundingOrderService advanceFundingOrderService;
    private static final Logger log = LoggerFactory.getLogger("dayLogger");

    @Autowired
    private ShareDataService shareDataService;

    /**
     * @Description: 指定业务编码
     * @Param: []
     * @return: java.lang.String
     * @Author: zhangweicheng
     * @Date: 2025/9/5
     */

    @Override
    public String getCode() {
        return CrmBussTypeEnum.advanceOrder.getCode();
    }


    /**
     * @Description: 1.从用户权限里面获取：产品管理、产品价格服务说明、开票税收信息  这些是查看创建者的，其余是查看销售负责人的,知识管理:是查看全部的
     * 2.查询销售负责人的id
     * @Param: []
     * @return: java.util.List<java.lang.String>
     * @Author: zhangweicheng
     * @Date: 2025/9/16
     */

    @Override
    public List<String> getBussUserIds() {

        return null;
    }

    @Override
    public List<String> getBussIds() {

        //业务ids，返回结果不可能为空，为空直接返回默认
        List<String> bussIds = new ArrayList<>();

        //一、根据数据授权，查询业务id
        LoginUser loginUser = UserUtil.getLoginUser();
        if (Objects.nonNull(loginUser)) {
            CrmDataAuthVO crmAuthData = loginUser.getCrmAuthData();
            if (Objects.nonNull(crmAuthData)) {

                List<CrmDataUserAuthDTO> crmUserAuthDatas = crmAuthData.getCrmUserAuthDatas();
                if (CollUtil.isNotEmpty(crmUserAuthDatas)) {
                    //循环处理查询业务ids
                    for (CrmDataUserAuthDTO crmDataUserAuthDTO : crmUserAuthDatas) {
                        String bussType = crmDataUserAuthDTO.getBussType();
                        if (CrmBussTypeEnum.advanceOrder.getCode().equals(bussType)) {//处理垫子订单的信息
                            //1.查询数据授权
                            //销售负责人的id，这个一定会有数据，所以说，返回结果不可能为空
                            List<String> userIds = crmDataUserAuthDTO.getUserIds();
                            //根据销售负责人的id查询全部的记录id
                            List<String> idsBySales = queryBussIdsBySalesId(userIds);
                            if (CollUtil.isNotEmpty(idsBySales)) {
                                bussIds.addAll(idsBySales);
                            }
                            //2.查询操作授权
                            List<String> operateRanges = crmDataUserAuthDTO.getOperateRanges();
                            //查看自己创建的
                            if (CollUtil.isNotEmpty(operateRanges) && operateRanges.contains(CrmDataOperateEnum.oper_private.getCode())) {
                                List<String> idsBycreateId = queryBussIdsBycreateId(Arrays.asList(loginUser.getId().toString()));
                                if (CollUtil.isNotEmpty(idsBycreateId)) {
                                    bussIds.addAll(idsBycreateId);
                                }
                            }
                            break;
                        }


                    }
                }
            }
        }

        //根据分享，查询业务id
        ShareDataQueryDTO shareDataQueryDTO = new ShareDataQueryDTO();
        shareDataQueryDTO.setUserId(loginUser.getId().toString());
        List<ShareDataListVO> shareDataListVOS = shareDataService.queryList(shareDataQueryDTO);
        if (CollUtil.isNotEmpty(shareDataListVOS)) {
            for (ShareDataListVO shareDataListVO : shareDataListVOS) {
                String bussId = shareDataListVO.getBussId();
                if (!bussIds.contains(bussId)) {
                    bussIds.add(bussId);
                }
            }
        }

        if (CollUtil.isNotEmpty(bussIds)) {
            return bussIds;
        }

        //直接返回一个iD,匹配不到任何结果，不允许返回为空的信息
        return Arrays.asList("defaultId");
    }

    @Override
    public Boolean doEditOrDelete(String currBussId, BussOperateDDLTypeEnum bussOperateDDLTypeEnum) {


        //通过销售负责人的id查询，业务的id集合：知识管理、产品管理、产品价格服务说明、开票税收信息、项目授权   这个就返回空

        //一、根据数据授权，查询业务id
        LoginUser loginUser = UserUtil.getLoginUser();
        if (Objects.nonNull(loginUser)) {
            CrmDataAuthVO crmAuthData = loginUser.getCrmAuthData();
            if (Objects.nonNull(crmAuthData)) {

                List<CrmDataUserAuthDTO> crmUserAuthDatas = crmAuthData.getCrmUserAuthDatas();
                if (CollUtil.isNotEmpty(crmUserAuthDatas)) {
                    //循环处理查询业务ids
                    for (CrmDataUserAuthDTO crmDataUserAuthDTO : crmUserAuthDatas) {
                        String bussType = crmDataUserAuthDTO.getBussType();
                        if (CrmBussTypeEnum.advanceOrder.getCode().equals(bussType)) {

                            //1.查询操作授权
                            List<String> operateRanges = crmDataUserAuthDTO.getOperateRanges();
                            //2.是授权里面的信息
                            if (CollUtil.isEmpty(operateRanges)) {
                                operateRanges = new ArrayList<>();
                            }
                            return queryOperateEditDeleteAuth(loginUser, currBussId, bussOperateDDLTypeEnum, operateRanges);
                        }
                    }
                }
            }
        }

        return false;
    }

    /**
     * @Description: 查询
     * @Param: [currBussId, code, operateRanges]
     * @return: java.lang.Boolean
     * @Author: zhangweicheng
     * @Date: 2025/9/18
     */
    private Boolean queryOperateEditDeleteAuth(LoginUser loginUser, String currBussId, BussOperateDDLTypeEnum bussOperateDDLTypeEnum, List<String> operateRanges) {
        String code = bussOperateDDLTypeEnum.getCode();
        //是否具备编辑权限
        Boolean doEdit = false;

        AdvanceFundingOrderDetailVO advanceFundingOrderDetailVO = queryBussData(currBussId);


        switch (code) {
            //查看是不是有编辑权限：编辑分为编辑其他成员的单据；编辑自己创建的单据
            case "edit":
                //分两种情况，编辑自己创建的单据、编辑其他成员的单据
                //根据当前业务的id查询数据
                if (operateRanges.contains(CrmDataOperateEnum.oper_edit_other.getCode())) {//编辑其他成员的单据
                    //有这个权限直接返回true
                    doEdit = true;
                } else {
                    //没这个权限，判断销售负责人是不是自己，是自己就可以编辑
                    if (Objects.nonNull(advanceFundingOrderDetailVO)) {
                        String handler = advanceFundingOrderDetailVO.getHandler();
                        if (loginUser.getId().toString().equals(handler)) {
                            doEdit = true;
                        }
                    }
                }

                if (operateRanges.contains(CrmDataOperateEnum.oper_edit_private.getCode())) {//编辑自己创建的单据
                    //销售负责人是自己或者销售负责人不是自己但是创建者是自己的
                    if (Objects.nonNull(advanceFundingOrderDetailVO)) {
                        String handler = advanceFundingOrderDetailVO.getHandler();
                        if (loginUser.getId().toString().equals(handler)) {
                            doEdit = true;
                        } else {
                            if (loginUser.getId().toString().equals(advanceFundingOrderDetailVO.getCreateName())) {
                                doEdit = true;
                            }
                        }
                    }
                } else {
                    //销售负责人是自己或者销售负责人不是自己但是创建者是自己的
                    if (Objects.nonNull(advanceFundingOrderDetailVO)) {
                        String handler = advanceFundingOrderDetailVO.getHandler();
                        if (loginUser.getId().toString().equals(handler)) {
                            doEdit = true;
                        }
                    }
                }
                break;
            //查看是不是有删除权限
            case "delete":
                // 执行语句
                //有这个权限都可以删除，没有这个权限的时候，判断销售负责人是不是自己，是自己就可以删除
                if (operateRanges.contains(CrmDataOperateEnum.oper_delete_other.getCode())) {//删除其他成员的单据
                    //有这个权限直接返回true
                    doEdit = true;
                } else {
                    //没这个权限，判断销售负责人是不是自己，是自己就可以删除
                    if (Objects.nonNull(advanceFundingOrderDetailVO)) {
                        String handler = advanceFundingOrderDetailVO.getHandler();
                        if (loginUser.getId().toString().equals(handler)) {
                            doEdit = true;
                        }
                    }
                }
                break;
            default:
                // 可选：以上都不匹配时执行
                doEdit = false;
        }
        return doEdit;
    }


    private AdvanceFundingOrderDetailVO queryBussData(String currBussId) {
        AdvanceFundingOrderIdDTO idDTO = new AdvanceFundingOrderIdDTO();
        idDTO.setId(currBussId);
        AdvanceFundingOrderDetailVO advanceFundingOrderDetailVO = advanceFundingOrderService.findById(idDTO);
        if (Objects.nonNull(advanceFundingOrderDetailVO)) {
            return advanceFundingOrderDetailVO;
        }
        return null;
    }


    @Override
    public String queryUserId(String currBussId) {
        AdvanceFundingOrderIdDTO idDTO = new AdvanceFundingOrderIdDTO();
        idDTO.setId(currBussId);
        AdvanceFundingOrderDetailVO advanceFundingOrderDetailVO = advanceFundingOrderService.findById(idDTO);
        if (Objects.nonNull(advanceFundingOrderDetailVO)) {
            return advanceFundingOrderDetailVO.getHandler();
        }

        return null;
    }

    /**
     * @Description: 通过销售负责人的id查询业务ids，不存在返回空
     * @Param: [saleUserId]
     * @return: java.util.List<java.lang.String>
     * @Author: zhangweicheng
     * @Date: 2025/9/17
     */

    @Override
    public List<String> queryBussIdsBySalesId(List<String> saleUserId) {
        if (CollUtil.isNotEmpty(saleUserId)) {
            return advanceFundingOrderService.findBySaleUserIds(saleUserId);
        }
        return null;
    }


    /**
     * @Description: 通过创建者的id查询业务的ids，不存在返回空
     * @Param: [createId]
     * @return: java.util.List<java.lang.String>
     * @Author: zhangweicheng
     * @Date: 2025/9/17
     */

    @Override
    public List<String> queryBussIdsBycreateId(List<String> createId) {
        if (CollUtil.isNotEmpty(createId)) {
            return advanceFundingOrderService.findByCreateIds(createId);
        }
        return null;
    }


}
