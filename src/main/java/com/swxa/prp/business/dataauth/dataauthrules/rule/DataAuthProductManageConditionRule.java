package com.swxa.prp.business.dataauth.dataauthrules.rule;

import cn.hutool.core.collection.CollUtil;
import com.swxa.prp.business.data.dto.ShareDataQueryDTO;
import com.swxa.prp.business.data.service.ShareDataService;
import com.swxa.prp.business.data.vo.ShareDataListVO;
import com.swxa.prp.business.dataauth._enum.BussOperateDDLTypeEnum;
import com.swxa.prp.business.dataauth._enum.CrmBussTypeEnum;
import com.swxa.prp.business.dataauth._enum.CrmDataOperateEnum;
import com.swxa.prp.business.dataauth.dataauthrules.inter.DataAuthRuleInterface;
import com.swxa.prp.business.dataauth.dto.CrmDataUserAuthDTO;
import com.swxa.prp.business.dataauth.vo.CrmDataAuthVO;
import com.swxa.prp.business.productmanage.dto.SearchOrDeleteProductDTO;
import com.swxa.prp.business.productmanage.service.ProductManageService;
import com.swxa.prp.business.productmanage.vo.ProductManageVO;
import com.swxa.prp.entity.LoginUser;
import com.swxa.prp.util.UserUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 产品管理
 * @Author: zhangweicheng
 * @Date: 2025/9/5
 */

@Service
public class DataAuthProductManageConditionRule implements DataAuthRuleInterface {

    @Autowired
    private ProductManageService productManageService;
    @Autowired
    private ShareDataService shareDataService;
    private static final Logger log = LoggerFactory.getLogger("dayLogger");


    /**
     * @Description: 指定业务编码
     * @Param: []
     * @return: java.lang.String
     * @Author: zhangweicheng
     * @Date: 2025/9/5
     */

    @Override
    public String getCode() {
        return CrmBussTypeEnum.productManage.getCode();
    }


    @Override
    public List<String> getBussUserIds() {
        return null;
    }

    @Override
    public List<String> getBussIds() {
        //业务ids，返回结果不可能为空，为空直接返回默认
        List<String> bussIds = new ArrayList<>();

        //产品管理全部返回
        return bussIds;
    }

    @Override
    public Boolean doEditOrDelete(String currBussId, BussOperateDDLTypeEnum bussOperateDDLTypeEnum) {
        //都可以编辑
        return true;
    }

    @Override
    public String queryUserId(String currBussId) {
        SearchOrDeleteProductDTO searchOrDeleteProductParam = new SearchOrDeleteProductDTO();
        searchOrDeleteProductParam.setId(currBussId);
        ProductManageVO productManageVO = productManageService.searchProductById(searchOrDeleteProductParam);
        if (Objects.nonNull(productManageVO)) {
            //// TODO: 2025/9/16 此次没有创建者id 
            return productManageVO.getCreatePerson();
        }
        return null;
    }

    /**
     * @Description: 通过销售负责人的id查询业务ids，不存在返回空
     * @Param: [saleUserId]
     * @return: java.util.List<java.lang.String>
     * @Author: zhangweicheng
     * @Date: 2025/9/17
     */

    @Override
    public List<String> queryBussIdsBySalesId(List<String> saleUserId) {
        return null;
    }

    /**
     * @Description: 通过创建者的id查询业务的ids，不存在返回空
     * @Param: [createId]
     * @return: java.util.List<java.lang.String>
     * @Author: zhangweicheng
     * @Date: 2025/9/17
     */

    @Override
    public List<String> queryBussIdsBycreateId(List<String> createId) {
        return null;
    }
}
