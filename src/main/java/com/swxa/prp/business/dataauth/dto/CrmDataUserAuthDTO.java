package com.swxa.prp.business.dataauth.dto;


import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 数据授权添加DTO
 * @Author: zhangweicheng
 * @Date: 2025/9/15
 */


@Data
public class CrmDataUserAuthDTO {


    //业务类型，前端路由的类型
    private String bussType;

    //授权用户的ID
    private List<String> userIds=new ArrayList<>();
    //操作授权  CrmDataOperateEnum
    private List<String> operateRanges;

}
