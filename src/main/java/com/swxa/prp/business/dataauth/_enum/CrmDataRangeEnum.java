package com.swxa.prp.business.dataauth._enum;


import java.util.HashMap;
import java.util.Map;

/**
 * @Description: 权限相关：CRM数据范围枚举
 * @Author: zhangweicheng
 * @Date: 2025/9/15
 */


public enum CrmDataRangeEnum {

    PRIVATE("private", "自己的"),
    ALL("all", "全部"),
    PERSON("person", "按成员"),
    DEPT("dept", "按照部门");
    private String code;

    private String name;


    private static Map<String, String> map = new HashMap<String, String>();

    static {
        for (CrmDataRangeEnum s : CrmDataRangeEnum.values()) {
            map.put(s.getCode(), s.getName());
        }
    }


    /**
     * @Description: 根据code获取名称
     * @Param: [code]
     * @return: java.lang.String
     * @Author: zhangweicheng
     * @Date: 2025/9/5
     */

    public static String getName(String code) {
        for (CrmDataRangeEnum s : CrmDataRangeEnum.values()) {
            if (s.getCode().equals(code)) {
                return s.getName();
            }
        }
        return null;
    }

    public static Map<String, String> map() {
        return map;
    }


    CrmDataRangeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
