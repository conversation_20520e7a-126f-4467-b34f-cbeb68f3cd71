package com.swxa.prp.business.dataauth._enum;


import java.util.HashMap;
import java.util.Map;

/**
 * @Description: 权限相关：CRM数据操作枚举
 * @Author: zhangweicheng
 * @Date: 2025/9/15
 */


public enum CrmDataOperateEnum {


    /**
     * @Description: 1.删除其他成员：有销售负责人的删除负责人不是i自己的；销售负责人实我也能删除，---只有创建者的   就是创建者不是自己的
     * @Param: 2.编辑自己创建的：编辑创建这是自己的，销售负责人不是自己；销售负责人是自己的；---只有创建者：编辑创建者是自己的
     * @return: 3.编辑其他成员的：编辑销售负责人不是自己的单据；只有创建者；编辑创建者不是自己的；     创建者肯定能编辑
     * @Author: zhangweicheng
     * @Date: 2025/9/18
     */

    /**
    * 1.编辑其他成员的单据：
     *    有销售负责人：有这个权限都可以编辑，没有这个权限的时候，判断销售负责人是不是自己，是自己就可以编辑，这是默认的
     *    只有创建者：有这个权限都可以编辑，但是没有这个权限，要看这个创建者是不是你自己，是的话可以编辑
     *
     * 2.删除其他成员：
     *    有销售负责人：有这个权限都可以删除，没有这个权限的时候，判断销售负责人是不是自己，是自己就可以删除，这是默认的
     *    只有创建者：有这个权限都可以删除，没有这个权限的时候，判断创建者是不是自己，是自己就可以删除，这是默认的
     *
     * 3.编辑自己创建的：
     *    有销售负责人：销售负责人是自己或者销售负责人不是自己但是创建者是自己的
     *    只有创建者：创建者是自己的
    */




    //查看自己创建的单据详情
    oper_private("oper_private", "查看自己创建的单据详情"),
    //编辑其他成员的单据
    oper_edit_other("oper_edit_other", "编辑其他成员的单据"),
    //编辑自己创建的单据
    oper_edit_private("oper_edit_private", "编辑自己创建的单据"),
    //删除其他成员的单据
    oper_delete_other("oper_delete_other", "删除其他成员的单据");
    private String code;

    private String name;


    private static Map<String, String> map = new HashMap<String, String>();

    static {
        for (CrmDataOperateEnum s : CrmDataOperateEnum.values()) {
            map.put(s.getCode(), s.getName());
        }
    }


    /**
     * @Description: 根据code获取名称
     * @Param: [code]
     * @return: java.lang.String
     * @Author: zhangweicheng
     * @Date: 2025/9/5
     */

    public static String getName(String code) {
        for (CrmDataOperateEnum s : CrmDataOperateEnum.values()) {
            if (s.getCode().equals(code)) {
                return s.getName();
            }
        }
        return null;
    }

    public static Map<String, String> map() {
        return map;
    }


    CrmDataOperateEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
