package com.swxa.prp.business.dataauth.dataauthrules.controller;

import com.swxa.prp.business.act.conditionFields.dto.ActConditionQueryFieldDTO;
import com.swxa.prp.util.ResponseData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * @Description: 获取是否允许授权访问
 * @Author: zhangweicheng
 * @Date: 2025/8/19
 */

@Controller
@RequestMapping("/data/auth/rule")
public class DataAuthRulesController {


    private static final Logger log = LoggerFactory.getLogger("dayLogger");

    /**
     * @Description: 根据服务编码获取字段信息
     * @Param: [advanceQueryServiceCodeDTO]
     * @return: com.swxa.prp.util.ResponseData
     * @Author: zhangweicheng
     * @Date: 2025/8/19
     */


    @ResponseBody
    @PostMapping(value = "/fields")
    public ResponseData field(@Validated @RequestBody ActConditionQueryFieldDTO actConditionQueryFieldDTO) {
        return ResponseData.ok();
    }


}
