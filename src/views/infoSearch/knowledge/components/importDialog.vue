<template>
  <el-dialog
    title="知识管理导入"
    :visible="dialogVisible"
    width="600px"
    :modal-append-to-body="true"
    :close-on-click-modal="false"
    :before-close="onCloseClick"
    @open="onOpenDig"
  >
    <div v-loading="loading">
      <div class="import-content">
        <div class="import-tips">
          <el-alert
            title="导入说明"
            type="info"
            :closable="false"
            show-icon
          >
            <div slot="default">
              <p>1. 请上传Excel文件（.xlsx或.xls格式）</p>
              <p>2. Excel文件必须包含名为"知识管理"的工作表</p>
              <p>3. 第一行为表头，第二行开始为数据</p>
              <p>4. 必填字段：标题、负责人</p>
            </div>
          </el-alert>
        </div>
        
        <div class="upload-section">
          <el-upload
            ref="upload"
            action=""
            accept=".xlsx,.xls"
            :limit="1"
            :multiple="false"
            :auto-upload="false"
            :show-file-list="true"
            :on-remove="onRemoveFile"
            :on-change="onChangeFile"
            :on-exceed="onExceedFile"
            :file-list="fileList"
            class="upload-demo"
          >
            <el-button type="primary" class="upload-btn">
              <i class="el-icon-upload2"></i>
              选择Excel文件
            </el-button>
            <div slot="tip" class="el-upload__tip">
              支持.xlsx和.xls格式，文件大小不超过10MB
            </div>
          </el-upload>
        </div>

        <div v-if="importResult.show" class="import-result">
          <el-alert
            :title="importResult.title"
            :type="importResult.type"
            :closable="false"
            show-icon
          >
            <div slot="default" v-html="importResult.message"></div>
          </el-alert>
        </div>
      </div>
    </div>
    
    <div slot="footer" class="dialog-footer">
      <el-button class="cancel-btn oper-btn" @click="onCloseClick">
        取消
      </el-button>
      <el-button 
        type="primary" 
        class="oper-btn" 
        @click="onSubmitClick"
        :loading="loading"
        :disabled="fileList.length === 0"
      >
        确定导入
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { importKnowledge } from "@/api/infoSearch/knowledge";

export default {
  name: "ImportDialog",
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      fileList: [],
      importResult: {
        show: false,
        type: "success",
        title: "",
        message: "",
      },
    };
  },
  methods: {
    onOpenDig() {
      this.fileList = [];
      this.importResult.show = false;
    },
    
    onCloseClick() {
      this.$emit("eventClose");
    },
    
    // 文件选择变化
    onChangeFile(file, fileList) {
      let newFile = file.raw || file;
      let fileNames = newFile.name.split(".");
      let type = fileNames[fileNames.length - 1].toLowerCase();
      
      // 验证文件类型
      if (!["xlsx", "xls"].includes(type)) {
        this.$message.error("请上传Excel文件（.xlsx或.xls格式）");
        this.fileList = [];
        return false;
      }
      
      // 验证文件大小（10MB）
      const maxSize = 10 * 1024 * 1024;
      if (newFile.size > maxSize) {
        this.$message.error("文件大小不能超过10MB");
        this.fileList = [];
        return false;
      }
      
      this.fileList = [newFile];
      this.importResult.show = false;
    },
    
    // 移除文件
    onRemoveFile(file, fileList) {
      this.fileList = [];
      this.importResult.show = false;
    },
    
    // 超出文件数量限制
    onExceedFile(files, fileList) {
      this.$message.warning("只能上传一个文件");
    },
    
    // 提交导入
    async onSubmitClick() {
      if (this.fileList.length === 0) {
        this.$message.warning("请先选择要导入的Excel文件");
        return;
      }
      
      this.loading = true;
      this.importResult.show = false;
      
      try {
        const formData = new FormData();
        formData.append("file", this.fileList[0]);
        
        const response = await importKnowledge(formData);
        
        if (response.code === 200) {
          this.importResult = {
            show: true,
            type: "success",
            title: "导入成功",
            message: `成功导入 ${response.data || 0} 条知识管理记录`,
          };
          
          // 延迟关闭弹窗并刷新数据
          setTimeout(() => {
            this.$emit("eventSucc");
            this.onCloseClick();
          }, 1500);
        } else {
          throw new Error(response.msg || "导入失败");
        }
      } catch (error) {
        console.error("导入失败:", error);
        this.importResult = {
          show: true,
          type: "error",
          title: "导入失败",
          message: error.message || "导入过程中发生错误，请检查文件格式和内容",
        };
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.import-content {
  .import-tips {
    margin-bottom: 20px;
    
    .el-alert {
      ::v-deep .el-alert__content {
        p {
          margin: 2px 0;
          font-size: 13px;
        }
      }
    }
  }
  
  .upload-section {
    margin-bottom: 20px;
    
    .upload-demo {
      text-align: center;
      
      .upload-btn {
        padding: 12px 24px;
        font-size: 14px;
        
        i {
          margin-right: 8px;
        }
      }
    }
  }
  
  .import-result {
    margin-top: 20px;
  }
}

.dialog-footer {
  text-align: right;
  
  .oper-btn {
    min-width: 80px;
    margin-left: 10px;
  }
  
  .cancel-btn {
    background: #fff;
    border-color: #dcdfe6;
    color: #606266;
    
    &:hover {
      color: #409eff;
      border-color: #c6e2ff;
      background-color: #ecf5ff;
    }
  }
}
</style>
