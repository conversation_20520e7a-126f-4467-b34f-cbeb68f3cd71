<template>
  <div class="full-height full-width page-container">
    <smart-search-table-content
      class="full-height full-width"
      ref="searchTableRef"
      :searchForm="searchForm"
      :advanceSearchForm="advanceSearchForm"
      :columns="columns"
      :tableData="tableData"
      :table-loading="loading"
      :tablePage="tablePage"
      @reset="resetBase"
      @query="queryBase"
      @pagination="pagination"
      :isSelected="true"
      :isAdvance="isAdvance"
      @queryAdvance="queryAdvance"
      @selectionChange="selectionChange"
      :selectable="selectable"
    >
      <template #pageTitle>
        <page-header title="知识管理">
          <!-- <template #titleWarning>{{ $t("pageTitle.apiKey.desc") }}</template> -->
        </page-header>
      </template>
      <template #search>
        <search-item label="标题" prop="notesTitle">
          <el-input
            v-model="searchForm.notesTitle"
            :placeholder="$t('placeholder.place')"
            clearable
          />
        </search-item>
        <search-item label="文件类型" prop="fileType">
          <el-select
            v-model="searchForm.fileType"
            :placeholder="$t('placeholder.select')"
          >
            <el-option
              v-for="(item, index) in dictData.knowledge_file_type"
              :key="index"
              :name="item.label"
              :value="item.value"
            ></el-option
            >?
          </el-select>
        </search-item>
      </template>
      <template v-slot:actions>
        <batch-action-btns
          :btns="actionBtns"
          :batchBtns="batchBtns"
          :limit="batchLimit"
          :showBatch="selectList.length > 0"
          :getSelectList="() => selectList"
          :isSelectTotal="isSelectTotal"
          :total="tablePage.total"
          @onSelectTotalClick="onSelectTotalClick"
          @clearSelectList="clearSelectList"
        />
      </template>
      <template #notesTitle="{ data }">
        <span
          class="link-btn text-primary"
          @click="onDetailClick(true, data)"
          >{{ data.notesTitle }}</span
        >
      </template>
      <template #operation="{ data }">
        <operatebtns
          :row="data"
          :btns="operateBtns"
          :limit="operbtnsLimit"
        ></operatebtns>
      </template>
    </smart-search-table-content>
    <startFlow
      :dialogVisible="flowDialog.dialogVisible"
      :rowObj="flowDialog.rowObj"
      @eventClose="flowDialog.dialogVisible = false"
    ></startFlow>
    <knowledge-add
      :dialogVisible="addDialog.dialogVisible"
      :rowObj="addDialog.rowObj"
      :isEdit="addDialog.isEdit"
      :dictData="dictData"
      @eventClose="eventClose(addDialog)"
      @eventSucc="resetParamsQuery(!addDialog.isEdit)"
    ></knowledge-add>
    <detailDrawer
      :dialogVisible="detailDig.dialogVisible"
      :rowObj="detailDig.rowObj"
      :isSelf="detailDig.isSelf"
      @eventClose="detailDig.dialogVisible = false"
      @onEditClick="onEditClick"
    ></detailDrawer>
    <importDialog
      :dialogVisible="importDialog.dialogVisible"
      @eventClose="importDialog.dialogVisible = false"
      @eventSucc="onImportSuccess"
    ></importDialog>
  </div>
</template>

<script>
import Page from "@/components/smartSearchTableContent/mixins/page";
import { checkExistRunProcess } from "@/api/flow/index";
import { getKnowledgePage, deleteKnowledge } from "@/api/infoSearch/knowledge";
import knowledgeAdd from "./components/add.vue";
import detailDrawer from "./detail/detailDrawer.vue";
import importDialog from "./components/importDialog.vue";

export default {
  mixins: [Page],
  components: {
    knowledgeAdd,
    detailDrawer,
    importDialog,
  },
  data() {
    return {
      addDialog: {
        dialogVisible: false,
        rowObj: {},
        isEdit: false,
      },
      flowDialog: {
        dialogVisible: false,
        rowObj: {},
        isEdit: false,
      },
      detailDig: {
        dialogVisible: false,
        rowObj: {},
        isSelf: false,
      },
      importDialog: {
        dialogVisible: false,
      },
      actionBtns: [
        {
          name: "新建",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["crm:knowledgeManage:add"],
        },
        {
          name: "导入",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-shangchuan1-line",
          func: this.onImportClick,
          permi: ["crm:knowledgeManage:import"],
        },
        {
          name: "导出",
          id: "exportBtnId",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-daochu",
          func: this.onExportClick,
          permi: ["crm:knowledgeManage:export"],
        },
      ],
      batchBtns: [
        {
          name: "删除",
          class: "danger-text-btn",
          icon: "iconfont icon-shanchu",
          permi: ["crm:knowledgeManage:delete"],
          func: (row) => {
            let ids = this.selectList.map((item) => item.id);
            this.confirmInfoOperFun(
              this.$t("tips.deleteTip"),
              deleteKnowledge,
              { ids },
              this.$t("msg.delete"),
            );
          },
        },
        {
          name: "共享",
          class: "oper-text-btn",
          icon: "iconfont icon-gongxiang",
          func: this.onShareClick,
          permi: ["crm:knowledgeManage:share"],
        },
      ],
      columns: [
        {
          label: "文档编号",
          prop: "noteNo",
        },
        {
          label: "标题",
          prop: "notesTitle",
          render: "notesTitle",
        },
        {
          label: "负责人",
          prop: "assignedUserName",
        },
        {
          label: "文件类型",
          prop: "fileType",
        },
        {
          label: "更新人",
          prop: "modifiedByName",
        },
        {
          label: "更新时间",
          prop: "modifiedTime",
        },
        {
          label: "说明",
          prop: "remark",
        },
        {
          label: "操作",
          prop: "operation",
          render: "operation",
          width: "120px",
          align: "center",
          fixed: "right",
        },
      ],
      operateBtns: [
        {
          name: this.$t("common.edit"),
          class: "oper-text-btn",
          permi: ["crm:knowledgeManagee:edit"],
          func: this.onEditClick,
        },
        {
          name: this.$t("common.delete"),
          class: "delete-text-btn",
          permi: ["crm:knowledgeManage:delete"],
          func: (row) => {
            this.confirmInfoOperFun(
              this.$t("tips.deleteTip"),
              deleteKnowledge,
              { ids: [row.id] },
              this.$t("msg.delete"),
            );
          },
        },
      ],
      // 查询参数
      searchForm: {
        notesTitle: "",
        fileType: "",
      },
      dictData: {
        knowledge_file_type: [],
      },
    };
  },
  created() {
    this.getDictList();
  },
  methods: {
    request: getKnowledgePage,

    onAddClick() {
      this.addDialog = {
        dialogVisible: true,
        rowObj: {},
        isEdit: false,
      };
    },
    onCheckClick() {},
    onImportClick() {
      this.importDialog.dialogVisible = true;
    },
    onExportClick() {},
    onStartFlowClick(row) {
      checkExistRunProcess({
        actTypeValue: this.$route.name,
        currBussId: row.id,
      }).then((res) => {
        this.flowDialog.dialogVisible = true;
        this.flowDialog.rowObj = { ...row };
      });
    },
    onDetailClick(isSelf, row) {
      this.detailDig.dialogVisible = true;
      this.detailDig.rowObj = { ...row };
      this.detailDig.isSelf = isSelf;
    },
    onEditClick(row) {
      this.addDialog = {
        dialogVisible: true,
        rowObj: {
          ...row,
        },
        isEdit: true,
      };
    },
    eventClose(dialog) {
      dialog.dialogVisible = false;
    },
    onImportSuccess() {
      this.importDialog.dialogVisible = false;
      this.resetParamsQuery(true);
    },
  },
  beforeDestroy() {},
};
</script>
